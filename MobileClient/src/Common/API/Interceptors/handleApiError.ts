import AsyncStorage from '@react-native-async-storage/async-storage';
import { decodeToken } from '../../../Utils/decode';
import { refreshAccessToken } from './refreshTokenHelper';
import { showAuthModal } from '../../../Components/TokenExpiryModal/showModalExternally';
import { AxiosError, AxiosInstance } from 'axios';

let isRefreshing = false;
let failedQueue: any[] = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) prom.reject(error);
    else prom.resolve(token);
  });
  failedQueue = [];
};

const retryRequest = (
  error: any,
  axiosInstance: AxiosInstance,
  maxRetries = 3
) => {
  const originalRequest = error.config;
  const retryCount = originalRequest._retryCount || 0;

  if (retryCount >= maxRetries) {
    return Promise.reject(error);
  }

  originalRequest._retryCount = retryCount + 1;
  const delay = Math.pow(2, retryCount) * 1000; // exponential backoff

  return new Promise((resolve) =>
    setTimeout(() => resolve(axiosInstance(originalRequest)), delay)
  );
};

export const handleApiError = async (
  error: AxiosError & { config?: any },
  axiosInstance: AxiosInstance
) => {
  const originalRequest = error.config;

  if (error.response && error.response.status === 401 && originalRequest && !originalRequest._retry) {
    originalRequest._retry = true;

    try {
      const accessToken = await AsyncStorage.getItem('accessToken');
      const decoded = decodeToken(accessToken || '');
      const expirationTime = decoded?.exp ?? 0;
      const currentTime = Date.now();
      const buffer = 60 * 1000;

      const timeUntilRefresh = expirationTime - currentTime;

      if (timeUntilRefresh <= buffer) {
        if (isRefreshing) {
          return new Promise((resolve, reject) => {
            failedQueue.push({
              resolve: (token: string) => {
                originalRequest.headers.Authorization = `Bearer ${token}`;
                resolve(axiosInstance(originalRequest));
              },
              reject,
            });
          });
        }

        isRefreshing = true;
        const newToken = await refreshAccessToken();
        isRefreshing = false;

        if (newToken) {
          processQueue(null, newToken);
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          return axiosInstance(originalRequest);
        } else {
          processQueue(new Error('Refresh failed'), null);
          showAuthModal();
          return Promise.reject(error);
        }
      } else {
        showAuthModal();
        return Promise.reject(error);
      }
    } catch (err) {
      showAuthModal();
      return Promise.reject(err);
    }
  }

  let message = 'An unknown error occurred';

  if (error.response) {
    switch (error.response.status) {
      case 400:
        message = 'Bad request. Please check your input.';
        break;
      case 403:
        message = 'Forbidden. You do not have access.';
        break;
      case 404:
        message = 'Resource not found.';
        break;
      case 500:
        message = 'Server error. Please try again later.';
        break;
       case 503:
      message = 'Service temporarily unavailable. Retrying...';
       return retryRequest(error, axiosInstance);
      default:
        message = (error.response.data as any)?.message || message;
    }
  } else if (error.request) {
    message = 'Network error. Please check your connection.';
  } else if (error.message) {
    message = error.message;
  }

  return Promise.reject({ ...error, message });
};
