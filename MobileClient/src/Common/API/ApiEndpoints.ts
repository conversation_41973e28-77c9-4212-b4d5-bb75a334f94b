import { getSchemaName } from "../Utils/Storage";

export const AUTHENTIC_ENDPOINTS = {
  LOGIN: '/login',
  ACCOUNT: '/account',
  AUTH_SCHEME: '/authscheme',
};

export const WORKFLOW_ENDPOINTS = {
  WORKSPACES: '/getAccountWorkSpaces',
  SCHEMAS: '/getSchemaByWorkspaceId',
  SOLUTIONS: '/getSolutionsAndSchemas',
  SOLUTION_APPS: '/getSolutionApps',
  WORKFLOW_TASKS: '/getWorkflowTasks',
  SCHEMA_NAME: '/getSchemaNameBySchemaId',
  API_RESPONSE: '/entity/getApiResponse',
};

export const COMMON_ENDPOINTS = {
  USER_ROLE: '/getUserRole',
  CONTROL_UNIT: '/getControlUnit',
  SCHEMA_USERS: '/getSchemaUsers',
  GET_MASTERS_DROPDOWN: '/getMastersDropDown',
  PROCESS_ENQUIRY : '/process-thread',
};

export const LEAD_MANAGEMENT_ENDPOITNS = {
  ALL_ENQUIRIES: '/getAllEnquiries',
  ALL_QUOTES: '/getCostSheet',
  ALL_JOBS: '/getAllJobs',
  CREATE_ROAD_ENQUIRY: '/createRoadEnquiry',
  CREATE_AIR_ENQUIRY: '/createAirEnquiry',
  CREATE_SEA_ENQUIRY: '/createSeaEnquiry',
  UPDATE_SEA_ENQUIRY: '/updateSeaEnquiryDetails',
  UPDATE_AIR_ENQUIRY: '/updateAirEnquiryDetails',
  UPDATE_ROAD_ENQUIRY: '/updateRoadEnquiryDetails',
};

export const SALES_MANAGEMENT_ENDPOINTS = {
  SALES_ACTIVITY_EVENTS: '/getAllSalesActivityEvents',
  DSR_EVENTS: '/getEvents',
  RESCHEDULE: '/reshedule',
  CRM_LEADS: '/getAllCrmLeads',
  SUBORDINATES: '/getSubordniates',
  CREATE_EVENT: '/createEvent',
  UPDATE_TASK: '/updateTask',
  GET_EXTERNAL_CONTACTS: '/getExternalContacts',
  GET_MASTERS_DROPDOWN: '/getMastersDropDown',
  DSR_CHECKIN: '/crmCheckInActivity',
  DSR_CHECKOUT: '/crmCheckOut',
  AREA_MAP: '/getUserCheckInCheckOutLogs',
};

export const TRACK_TRACE_ENDPOINTS = {
  AIRSHIPMENTS: '/fetchTheAwbNumberBasedOrgId',
  OCEANSHIPMENTS: '/getShipmentListBasedOnClientId',
  OCEAN_EVENTS: '/getTheEventDataByNumber',
  OCEAN_CLIENTS: '/getClientList',
  AIR_CLIENTS: '/fetchTheAirShipmentsListBasedOnClientId',
  AIR_SHIPMENT_DETAIL: '/triggerTrackingMoreApi',
};

export const CHAT_ENDPOINTS = {};
export const FEED_ENDPOINTS = {};

export const MAIL_ENDPOINTS = {};

export const ENDPOINTS = {
  AUTH: AUTHENTIC_ENDPOINTS,
  WORKFLOW: WORKFLOW_ENDPOINTS,
  LEADS: LEAD_MANAGEMENT_ENDPOITNS,
  SALES: SALES_MANAGEMENT_ENDPOINTS,
  TRACK_TRACE: TRACK_TRACE_ENDPOINTS,
  CHAT: CHAT_ENDPOINTS,
  FEED: FEED_ENDPOINTS,
  MAIL: MAIL_ENDPOINTS,
  COMMON: COMMON_ENDPOINTS,
  
};

export const ApiEndpoints = {
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  PROFILE: '/user/profile',
  CONTROL_UNIT: '/getControlUnit',
  AIRSHIPMENTS: '/fetchTheAwbNumberBasedOrgId',
  OCEANSHIPMENTS: '/getShipmentListBasedOnClientId',
  OCEAN_EVENTS: '/getTheEventDataByNumber',
  OCEAN_CLIENTS: '/getClientList',
  AIR_CLIENTS: '/fetchTheAirShipmentsListBasedOnClientId',
  AIR_SHIPMENT_DETAIL: '/triggerTrackingMoreApi',
  CREATE_EVENT: '/createEvent',
};

export const DSREndPoints = {
  SALES_ACTIVITY_EVENTS: '/getAllSalesActivityEvents',
  DSR_EVENTS: '/getEvents',
  RESCHEDULE: '/reshedule',
  CRM_LEADS: '/getAllCrmLeads',
  SUBORDINATES: '/getSubordniates',
  CREATE_EVENT: '/createEvent',
  UPDATE_TASK: '/updateTask',
  GET_EXTERNAL_CONTACTS: '/getExternalContacts',
  GET_MASTERS_DROPDOWN: '/getMastersDropDown',
  DSR_CHECKIN: '/crmCheckInActivity',
  DSR_CHECKOUT: '/crmCheckOut',
  UPDATE_SALES_ACTIVITY: '/updateSalesActivity',
  DELETE_ACTIVITY: '/deleteSalesActivity',
};

export const PROSPECT_DETAILS_ENDPOINTS = {
  COMPANY_DETAILS: '/getCompanyDetails',
  CONTACTS: '/getOrgContacts',
  ACTIVITIES: '/getActivityEventsBasedOnType',
};

export const LEADS_ENDPOINTS = {
  GET_SUSPECTS: '/getSuspects',
  GET_PROSPECTS: '/getProspects',
  GET_OPPORTUNITIES: '/getOpportunities',
  GET_CUSTOMERS: '/getCustomers',
  GET_ALL_LEADS: '/getAllLeads',
  CREATE_LEAD: '/createLead',
  GET_AIR_ENQUIRY: '/getAirEnquiryDetails',
  GET_SEA_ENQUIRY: '/getSeaEnquiryDetails',
  GET_ROAD_ENQUIRY: '/getRoadEnquiryDetails',
  GET_SEA_JOB_DETAILS: '/getSeaJobDetails',
  GET_AIR_JOB_DETAILS: '/getAirJobDetails',
  GET_ROAD_JOB_DETAILS: '/getRoadJobDetails',
  // endpoints for accound details in leads module
  GET_CRM_CONTACTS: '/getCrmContactsByOrgId',
  UPDATE_CRM_ORG_ACC: '/updateCrmOrgAccountDetails',
  // Added endpoints for creating enquiries
  GET_CUSTOMER_ORGANISATATION_STATICS:'/getOrganizationStatistics',
  GET_ACTIVITY_BASED_ON_TYPE:'/getActivityEventsBasedOnType',
  GET_INVOICE_LIST:'/getInvoiceList',
  GET_ALL_JOBS:'/getAllJobs',
  GET_ORG_CONTACTS:'/getOrgContacts',
  GET_ALL_ENQUIRIES:'/getAllEnquiries',
  CREATE_ROAD_ENQUIRY: '/createRoadEnquiry',
  CREATE_AIR_ENQUIRY: '/createAirEnquiry',
  CREATE_SEA_ENQUIRY: '/createSeaEnquiry',
  DELETE_LEAD: '/deleteLeads',
  UPDARE_ORG_SERVICES: '/updateOrganizationServices',
  GET_ORG_SERVICES: '/getOrganizationServices',
};

export const ENQUIRY_FORM_ENDPOINTS = {
  SEARCH_LOCATION: '/getLocationsList',
  SEARCH_CITIES: '/getCitiesList',
};
