import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { RootStackParamList } from '../Routes/StackTypes';
import { useFocusEffect, useNavigation, useNavigationState } from '@react-navigation/native';
// Apollo Provider
import EmailInboxProvider from '../../Common/ApolloClients/EmailApolloClient';

// Screens
import EmailInbox from '../../Stacks/EmailInboxStack/Screens/EmailInbox';
import EmailCompose from '../../Stacks/EmailInboxStack/Screens/EmailCompose';
import EmailDetail from '../../Stacks/EmailInboxStack/Screens/EmailDetail';
import EmailDrawer from '../../Stacks/EmailInboxStack/Components/EmailDrawer';
import EmailExtractionScreen from '../../Stacks/EmailInboxStack/Screens/EmailExtractionScreen';
const EmailInboxStack = createNativeStackNavigator<RootStackParamList>();

export const EmailInboxStackNavigator: React.FC = () => {
  // const navigation = useNavigation();
  // const currentTab = useNavigationState((state) => state.routes[state.index]?.name);

  // useFocusEffect(
  //   React.useCallback(() => {
  //     const parent = navigation.getParent(); // BottomTabStack is the immediate parent
  //     const drawer = parent?.getParent(); // Drawer is the grandparent

  //     if (currentTab === 'EmailInbox') {
  //       // Set the drawer content to EmailDrawer when inside the EmailInbox tab
  //       drawer?.setOptions({
  //         drawerContent: (props: any) => <EmailDrawer {...props} />,
  //       });
  //     } else {
  //       // Reset the drawer content to the default CustomerDrawer
  //       drawer?.setOptions({
  //         drawerContent: (props: any) => <CustomerDrawer {...props} />,
  //       });
  //     }

  //     // Cleanup: reset to default drawer when leaving the Email context
  //     return () => {
  //       drawer?.setOptions({
  //         drawerContent: (props: any) => <CustomerDrawer {...props} />, // Reset to default when leaving
  //       });
  //     };
  //   }, [navigation, currentTab]) // Dependencies: navigation and currentTab
  // );

  return (
    <EmailInboxProvider>
      <EmailInboxStack.Navigator screenOptions={{ headerShown: false }}>
        <EmailInboxStack.Screen name="EmailInboxScreen" component={EmailInbox} />
        <EmailInboxStack.Screen name="EmailCompose" component={EmailCompose} />
        <EmailInboxStack.Screen name="EmailDetail" component={EmailDetail} />
        <EmailInboxStack.Screen name="EmailExtractionScreen" component={EmailExtractionScreen} />
      </EmailInboxStack.Navigator>
    </EmailInboxProvider>
  );
};
