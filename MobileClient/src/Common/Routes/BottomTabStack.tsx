import React, { useEffect, useState } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Text, useTheme } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import { Keyboard } from 'react-native';
// Lucide icons
import { Home, Radio, MessageCircle, User, Bell, RssIcon, Mail, Mails } from 'lucide-react-native';


import { HomeStackNavigator } from '../StackRoutes/HomeStackNavigator';
import { ChatStackNavigator } from '../StackRoutes/ChatStackNavigator';
import { FeedStackNavigator } from '../StackRoutes/FeedStackNavigator';
import { ProfileStackNavigator } from '../StackRoutes/ProfileStackNavigator';
import { NotificationStack } from '../StackRoutes/NotificationStackNavigator';
import { EmailInboxStackNavigator } from '../StackRoutes/EmailInboxStackNavigator';
import EmailCompose from '../../Stacks/EmailInboxStack/Screens/EmailCompose';
import { moderateScale } from 'react-native-size-matters';
import { useSelector } from 'react-redux';
import { RootState } from '../../State/Store';

const Tab = createBottomTabNavigator();

const CustomTabBar = ({ state, navigation }) => {
  const theme = useTheme();
  const { colors } = theme;
  const insets = useSafeAreaInsets();
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const unreadCount = useSelector(
    (state: RootState) => state.notificationCount.unreadCount
  );

  useEffect(() => {
    const showSubscription = Keyboard.addListener('keyboardDidShow', () =>
      setKeyboardVisible(true)
    );
    const hideSubscription = Keyboard.addListener('keyboardDidHide', () =>
      setKeyboardVisible(false)
    );
    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  if (keyboardVisible) return null;
  return (
    <View
      style={[
        styles.tabBar,
        {
          backgroundColor: '#fff',
          paddingBottom: insets.bottom ? insets.bottom : 16,
        },
      ]}
    >
      {state.routes.map((route, index) => {
        const focused = state.index === index;
        let IconComponent;
        let iconColor = focused ? '#fff' : '#434343';

        switch (route.name) {
          case 'Home':
            IconComponent = <Home color={iconColor} size={18} strokeWidth={2.5} />;
            break;
          case 'Notifications':
            IconComponent = (
              <View style={{ position: 'relative' }}>
                <Bell color={iconColor} size={18} strokeWidth={2.5} />
                {unreadCount > 0 && (
                  <View style={styles.badge}>
                    <Text style={styles.badgeText}>{unreadCount}</Text>
                  </View>
                )}
              </View>
            );
            break;
          case 'Feed':
            IconComponent = <RssIcon color={iconColor} size={18} strokeWidth={3} />;
            break;
          case 'Chat':
            IconComponent = <MessageCircle color={iconColor} size={18} strokeWidth={2.5} />;
            break;
          case 'Profile':
            IconComponent = <User color={iconColor} size={18} strokeWidth={2.5} />;
            break;
          case 'EmailInbox':
            IconComponent = <Mail color={iconColor} size={18} strokeWidth={2.5} />;
            break;
          default:
            IconComponent = <Home color={iconColor} size={18} strokeWidth={2.5} />;
        }

        return (
          <TouchableOpacity
            key={route.key}
            accessibilityRole="button"
            activeOpacity={0.7}
            accessibilityState={focused ? { selected: true } : {}}
            onPress={() => navigation.navigate(route.name)}
            style={styles.tab}
          >
            {focused ? (
              <LinearGradient
                colors={['#91bfff', '#3377ff']}
                style={styles.gradientIconWrapper}
                start={{ x: 0, y: 0.5 }}
                end={{ x: 1, y: 0.5 }}
              >
                <View style={{ padding: 8, paddingHorizontal: 25 }}>{IconComponent}</View>
              </LinearGradient>
            ) : (
              <View style={styles.iconWrapper}>{IconComponent}</View>
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const BottomTabStack = () => (
  <Tab.Navigator
    screenOptions={{
      headerShown: false,
      tabBarHideOnKeyboard:true
      
    }}
    tabBar={(props) => <CustomTabBar {...props} />}
  >
    <Tab.Screen name="Home" component={HomeStackNavigator} 
    options={{
      tabBarHideOnKeyboard: true,
    }}
    />
    <Tab.Screen
      name="Notifications"
      component={NotificationStack}
    />
    <Tab.Screen name="Feed" component={FeedStackNavigator} />
    <Tab.Screen name="Chat" component={ChatStackNavigator} />
    {/* <Tab.Screen name="EmailInbox" component={EmailInboxStackNavigator} /> */}
    <Tab.Screen name="Profile" component={ProfileStackNavigator} />
  </Tab.Navigator>
);


const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderRadius: 0,
    // marginHorizontal: 24,
    // marginTop: 12,
    marginBottom: 0,
    paddingHorizontal: 20,
    paddingVertical: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 5,
    backgroundColor: '#fff',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    // borderWidth:1,
    // marginRight:5,
    
  },
  iconWrapper: {
    // borderRadius: 18,
    padding: 8,
    // borderWidth:1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  gradientIconWrapper: {
    borderRadius: 20,
    // padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badge: {
    position: 'absolute',
    top: -8,       // adjust vertical position
    left: 10,     // move to left side
    backgroundColor: '#FF3B30',
    borderRadius: 8,
    paddingHorizontal: 5,
    paddingVertical: 1,
    minWidth: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeText: {
    color: '#fff',
    fontSize: moderateScale(12),
    fontWeight: '600',
  },

});

export default BottomTabStack;
