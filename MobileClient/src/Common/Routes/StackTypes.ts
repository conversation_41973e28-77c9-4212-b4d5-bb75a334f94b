// Define the file data interface
export interface FileData {
  uri: string;
  type: string;
  name: string;
}
import { AuthSchemeResponse } from '../../Authentication/screens/DomainEntry/DomainEntry.types';
import { string } from 'yup';
// Define the types for navigation
import { SolutionAppItem } from '../../Stacks/HomeStack/screens/SolutionApps/SolutionApps.types';

export type RootStackParamList = {
    DomainEntry: undefined;
  WelcomeScreen: undefined;
  Home: undefined;
  Notifications: undefined;
  Feed: undefined;
  FeedScreen: undefined;
  Chat: undefined;
  Profile: undefined;
  Workspace: undefined;
  UserProfile: undefined;
  WorkFlowTasks: undefined;
  CRMDashboard: undefined;
  Drawer: undefined;
  SplashScreen: undefined;
  Initial: undefined;
  SignIn: undefined;
  SignUp: undefined;
  VerifyOtp: undefined;
  ForgotPassword: undefined;
  ChangePassword: undefined;
  BottomTabStack: undefined;
  TrackingStack: undefined;
  TrackAndTrace: {
    appData?: SolutionAppItem;
  };
  AdaptiveCardExample: undefined;
  CardView: undefined;
  UpdateForm: undefined;
  ThemeScreen: undefined;
  FileAttachment: { fileData: FileData };
  ModuleScreen: undefined;
  ChatRoomsListScreen: undefined;
  ContactListScreen: undefined;
  ChatRoom: { data: any };
  MainNotificationsScreen:undefined;
  CreateRoom: undefined;
  RoomInfo: undefined;
  UsersListScreen: { currentUsers: any };
  TestingScreen: undefined;
  CreatePostScreen: {
    setUploading: (value: boolean) => void;
    setProgress: (value: number) => void;
    uploading: boolean;
    progress: number;
  };
  FeedStack: {
    screen: keyof RootStackParamList;
    params?: object;
  };
  ChatStack: {
    screen: keyof RootStackParamList;
    params?: object;
  };

  FeedBottom: undefined;
  ShipmentTrackingScreen: undefined;
/** addconnection screen  */
AddConnectionScreen:undefined;
FollowersDetailsScreen:undefined;

  Solutions : undefined;
  SolutionApps: undefined;
  AttendanceStack:undefined,
  AttendanceScreen:undefined,
  AttendanceRequest:undefined,
  AttendanceHistory:undefined,
  AttendanceStackNavigator:undefined;
  AttendanceTracker: undefined;
  DailyActivities: undefined;

  // Sales Management Stack
  SalesManagementStack: {
    appData?: SolutionAppItem;
  };
  SalesManagementScreen: undefined;
  DSRScreen: {
    appData?: any;
  };
  DSRDetailView: {
    activity: any;
  };
  DashboardScreen: {
    appData?: any;
  };

  MenuSelectedScreen: undefined;
  drawer: {
    appData: SolutionAppItem;
  };
  SchemasScreen: {
    workspace: { name: string; id: string };
    navigation: any;
    route: any;
  };
  MailCenter: undefined;
  DetailView: undefined;

  EmailInboxScreen: undefined;
  EmailCompose: {
    replyTo?: any;
    draftId?: string;
  };
  EmailDetail: {
    emailId?: string;
    threadId?: string;
    email?: any;
  };
  EmailExtractionScreen: {
    transportationType: string;
    enquiryType: string;
  } | undefined;
  
  OceanClientList: undefined;
  ShipmentScreen: undefined;
  AirShipmentScreen: undefined;
  OceanShipmentScreen: undefined;
  AirClientList: undefined;
  AirShipmentDetail: undefined;
  SalesAreaMapScreen : undefined;
  ProspectDetails: undefined;
  LeadsStack : {
    appData : any
  };
  LeadsScreenMain : undefined;
  EnquiryForm:undefined;
  CreateActivityScreen: undefined;
  LeadSuspectForm:undefined;
  LeadProspectForm:undefined;
  ViewEnquiryForm:undefined;
  ViewJobDetails:undefined;
  QuoteDetailScreen : {
    quoteData : any
  };
  searchLocation:undefined;
  EditServicesForm:undefined;
  LeadContactForm:undefined;
  CompanyDetailsForm:undefined;
  AccountDetailsForm:undefined;
  ActivityDetailsModal: { selectedActivity: any };
};
