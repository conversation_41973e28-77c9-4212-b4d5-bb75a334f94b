import AsyncStorage from '@react-native-async-storage/async-storage';
import Config from 'react-native-config';

export const getChatAppsyncConfig = async () => {
  const domain = await AsyncStorage.getItem('domain');

  let aws_appsync_region = Config.VITE_AWS_CHAT_REGION_DEV ?? '';
  let aws_appsync_authenticationType = Config.VITE_AWS_APPSYNC_AUTHENTICATION_TYPE ?? '';
  let aws_appsync_graphqlEndpoint = Config.VITE_AWS_CHAT_APPSYNC_DEV ?? '';
  let aws_appsync_apiKey = Config.VITE_AWS_CHAT_APPSYNC_APIKEY_DEV ?? '';

  if (domain === 'voltusfreight.net' || domain === 'shipwave.net') {
    aws_appsync_region = Config.VITE_AWS_CHAT_REGION_QA ?? '';
    aws_appsync_authenticationType = Config.VITE_AWS_APPSYNC_AUTHENTICATION_TYPE ?? '';
    aws_appsync_graphqlEndpoint = Config.VITE_AWS_CHAT_APPSYNC_QA ?? '';
    aws_appsync_apiKey = Config.VITE_AWS_CHAT_APPSYNC_APIKEY_QA ?? '';
  } else if (domain === 'voltusfreight.com') {
    aws_appsync_region = Config.VITE_AWS_CHAT_REGION_PROD ?? '';
    aws_appsync_authenticationType = Config.VITE_AWS_APPSYNC_AUTHENTICATION_TYPE ?? '';
    aws_appsync_graphqlEndpoint = Config.VITE_AWS_CHAT_APPSYNC_PROD ?? '';
    aws_appsync_apiKey = Config.VITE_AWS_CHAT_APPSYNC_APIKEY_PROD ?? '';
  }

  return {
    aws_appsync_region,
    aws_appsync_authenticationType,
    aws_appsync_graphqlEndpoint,
    aws_appsync_apiKey,
  };
};

export const chatAppSyncConfig = {
  aws_appsync_graphqlEndpoint: Config.VITE_AWS_CHAT_APPSYNC_GRAPHQL_ENDPOINT,
  aws_appsync_region: Config.VITE_AWS_REGION,
  aws_appsync_authenticationType: Config.VITE_AWS_APPSYNC_AUTHENTICATIONTYPE,
  aws_appsync_apiKey: Config.VITE_AWS_CHAT_APPSYNC_APIKEY,
};

export const getEnvConfig = async () => {
  const domain = await AsyncStorage.getItem('domain');
  let EMAIL_VALIDATION_API_KEY = Config.VITE_ZERO_BOUNCE_EMAIL_VALIDATION_API_KEY ?? '';
  let aws_appsync_region = Config.VITE_AWS_CHAT_REGION_DEV ?? '';
  let aws_appsync_authenticationType = Config.VITE_AWS_APPSYNC_AUTHENTICATION_TYPE ?? '';
  let aws_appsync_graphqlEndpoint = Config.VITE_AWS_CHAT_APPSYNC_DEV ?? '';
  let aws_appsync_apiKey = Config.VITE_AWS_CHAT_APPSYNC_APIKEY_DEV ?? '';
  let AWS_CHAT_BUCKET_NAME = Config.VITE_AWS_CHAT_BUCKET_NAME_DEV;
  let AWS_FEED_BUCKET_NAME = Config.VITE_AWS_FEED_BUCKET_NAME_DEV;
  let AWS_MAIL_BUCKET_NAME = Config.VITE_AWS_EMAIL_BUCKET_NAME_DEV;
  let AWS_REGION = Config.VITE_AWS_GLOBAL_REGION;
  let AWS_ACCESS_KEY = Config.VITE_AWS_ACCESS_KEY_ID_DEV;
  let AWS_SECRET_KEY = Config.VITE_AWS_SECRET_ACCESS_KEY_DEV;
  let DSR_S3_BUCKET_NAME = Config.DSR_S3_BUCKET_NAME_DEV;
  let ENCRYPTION_SECRET_KEY = Config.VITE_PAYLOAD_ENCRYPTION_SECRET_KEY_DEV;
  let APP_ENV = Config.VITE_APP_ENV_DEV;

  if (domain === 'voltusfreight.net' || domain === 'shipwave.net') {
    aws_appsync_region = Config.VITE_AWS_CHAT_REGION_QA ?? '';
    aws_appsync_authenticationType = Config.VITE_AWS_APPSYNC_AUTHENTICATION_TYPE ?? '';
    aws_appsync_graphqlEndpoint = Config.VITE_AWS_CHAT_APPSYNC_QA ?? '';
    aws_appsync_apiKey = Config.VITE_AWS_CHAT_APPSYNC_APIKEY_QA ?? '';
    AWS_CHAT_BUCKET_NAME = Config.VITE_AWS_CHAT_BUCKET_NAME_QA;
    AWS_FEED_BUCKET_NAME = Config.VITE_AWS_FEED_BUCKET_NAME_QA;
    AWS_MAIL_BUCKET_NAME = Config.VITE_AWS_EMAIL_BUCKET_NAME_QA;
    DSR_S3_BUCKET_NAME = Config.DSR_S3_BUCKET_NAME_QA;
    AWS_ACCESS_KEY = Config.VITE_AWS_ACCESS_KEY_ID_QA;
    AWS_SECRET_KEY = Config.VITE_AWS_SECRET_ACCESS_KEY_QA;
    ENCRYPTION_SECRET_KEY = Config.VITE_PAYLOAD_ENCRYPTION_SECRET_KEY_QA;
    // APP_ENV = Config.VITE_APP_ENV_QA;
  } else if (domain === 'voltusfreight.com') {
    aws_appsync_region = Config.VITE_AWS_CHAT_REGION_PROD ?? '';
    aws_appsync_authenticationType = Config.VITE_AWS_APPSYNC_AUTHENTICATION_TYPE ?? '';
    aws_appsync_graphqlEndpoint = Config.VITE_AWS_CHAT_APPSYNC_PROD ?? '';
    aws_appsync_apiKey = Config.VITE_AWS_CHAT_APPSYNC_APIKEY_PROD ?? '';
    AWS_CHAT_BUCKET_NAME = Config.VITE_AWS_CHAT_BUCKET_NAME_PROD;
    AWS_FEED_BUCKET_NAME = Config.VITE_AWS_FEED_BUCKET_NAME_PROD;
    AWS_MAIL_BUCKET_NAME = Config.VITE_AWS_EMAIL_BUCKET_NAME_PROD;
    DSR_S3_BUCKET_NAME = Config.DSR_S3_BUCKET_NAME_PROD;
    AWS_ACCESS_KEY = Config.VITE_AWS_ACCESS_KEY_ID_PROD;
    AWS_SECRET_KEY = Config.VITE_AWS_SECRET_ACCESS_KEY_PROD;
    ENCRYPTION_SECRET_KEY = Config.VITE_PAYLOAD_ENCRYPTION_SECRET_KEY_PROD;
    APP_ENV = Config.VITE_APP_ENV_PROD;
  }

  return {
    aws_appsync_region,
    aws_appsync_authenticationType,
    aws_appsync_graphqlEndpoint,
    aws_appsync_apiKey,
    AWS_CHAT_BUCKET_NAME,
    AWS_FEED_BUCKET_NAME,
    AWS_MAIL_BUCKET_NAME,
    DSR_S3_BUCKET_NAME,
    AWS_REGION,
    AWS_ACCESS_KEY,
    AWS_SECRET_KEY,
    ENCRYPTION_SECRET_KEY,
    APP_ENV,
    EMAIL_VALIDATION_API_KEY,
  };
};

export const awsConfigurations = {
  AWS_BUCKET_NAME: Config.VITE_AWS_CHAT_BUCKET_NAME,
  AWS_REGION: Config.VITE_AWS_CHAT_REGION,
  AWS_ACCESS_KEY: Config.VITE_AWS_ACCESS_KEY_ID,
  AWS_SECRET_KEY: Config.VITE_AWS_SECRET_ACCESS_KEY,
};
