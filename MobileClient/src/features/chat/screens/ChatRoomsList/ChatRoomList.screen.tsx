import React from 'react';
import { View, FlatList } from 'react-native';
import { FlashList } from '@shopify/flash-list';
import {
  Text,
  useTheme as usePaperTheme,
  ActivityIndicator,
  AnimatedFAB,
  Tooltip,
} from 'react-native-paper';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import NoChats from '../../../../../assets/SVG/no-chats.svg';
// components
import ChatRoomsListHeader from '../../components/ChatRoomsListHeader';
import SafeContainerView from '../../../../Components/SafeContainer/index';
import AnimatedChatRoomItem from './components/AnimatedChatRoomItem';
import AnimatedPullToRefresh from '../../components/common/AnimatedPullToRefresh';

// hooks , types, redux
import { useFabAnimation } from './hooks/useFabAnimation';
import { useChatRoomsData } from './hooks/useChatRoomsData';
import { RootStackParamList } from '../../../../Common/Routes/StackTypes';

// styles
import { createStyles } from './ChatRoomList.styles';
import EmptyorErrorComponent from '../../../../Components/EmptyOrError/EmptyOrErrorComponent';
import AppLoader from '../../../../Components/Loader/AppLoader/InitialLoaderWithText';
import { Source } from 'graphql';

const ChatRoomsListScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const paperTheme = usePaperTheme();
  const styles = createStyles();

  const {
    chatRooms,
    loading,
    error,
    searchQuery,
    setSearchQuery,
    searchLoading,
    refreshing,
    refetchUserRooms,
    isFocused,
    setIsFocused,
  } = useChatRoomsData();
  const { fabScale, spin, animateFAB } = useFabAnimation();
  // console.log("chatRooms", chatRooms)
  const handleCreateRoom = () => {
    animateFAB();
    setTimeout(() => {
      navigation.navigate('ChatStack', { screen: 'CreateRoom' });
    }, 300);
  };

  return (
    <SafeContainerView backgroundColor={"#fff"}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={paperTheme.colors.primary} />
        </View>
      ) : (
        <View style={{flex: 1}}>
          <ChatRoomsListHeader
            title="Recent Chats"
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            handleCreateRoom={handleCreateRoom}
            fabScale={fabScale}
            spin={spin}
            isFocused={isFocused}
            setIsFocused={setIsFocused}
          />
          <View style={styles.container}>
            <FlashList
              data={chatRooms}
              keyExtractor={(item) => item?.room_id}
              renderItem={({ item, index }) => <AnimatedChatRoomItem item={item} index={index} />}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ paddingBottom: 130 }}
              keyboardShouldPersistTaps="always"
              estimatedItemSize={10}
              onEndReached={() => console.log('i am at end of the scroll')}
              // maxToRenderPerBatch={10}
              onRefresh={refetchUserRooms}
              refreshing={refreshing}
              ListEmptyComponent={<EmptyorErrorComponent message="You don't have any Chats" 
              lucideIcon={<NoChats width={150} height={150} />}
              />}
            />

            <AnimatedFAB
              icon={'plus'}
              label={'Label'}
              extended={false}
              onPress={() =>
                navigation.navigate('ChatStack', {
                  screen: 'ContactListScreen',
                })
              }
              animateFrom={'right'}
              iconMode={'static'}
              style={[styles.fabStyle]}
            />
          </View>
      </View>
        )} 
  
    </SafeContainerView>
  );
};

export default ChatRoomsListScreen;
