import {StyleSheet, Platform} from 'react-native';
// import {useTheme} from '../../../../Common/Theme/hooks/useTheme';
import {useTheme} from 'react-native-paper';
import {moderateScale, verticalScale, scale} from 'react-native-size-matters';
export const createStyles = () => {
  const {colors, fontFamily, fontSize, borderRadius, shadow} = useTheme();
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor:"#fff",
      paddingHorizontal: scale(16),
      // paddingTop: verticalScale(10),
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: verticalScale(10),
    },
    headerText: {
      fontSize: moderateScale(6),
    },
    addButton: {
      padding: moderateScale(12),
      backgroundColor: '#007AFF',
      borderRadius: borderRadius.round,
      justifyContent: 'center',
      alignItems: 'center',
    },
    addButtonText: {
      fontSize: fontSize.l,
    },
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-start',
      paddingHorizontal: scale(15),
      paddingVertical: verticalScale(4),
      borderColor: '#E0E0E0',
      borderWidth: scale(1),
      borderRadius: moderateScale(25),

      ...shadow,
      // elevation: 1, // For Android shadow
      // marginHorizontal: 16, // Optional: Add margin to the sides
      marginVertical: verticalScale(8), // Optional: Add margin to the top and bottom
    },
    searchBar: {
      flex: 1, // Takes up remaining space
      fontSize: fontSize.l,
      fontFamily: fontFamily.regular,
      marginLeft: scale(10),
      paddingVertical:
        Platform.OS === 'ios' ? verticalScale(8) : verticalScale(6), // Adjust padding for better alignment
      // Removes outline on web (if applicable)
    },
    chatItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      // paddingVertical: verticalScale(8),
      
    },
    avatar: {
      // width: moderateScale(50),
      // height: moderateScale(50),
      borderRadius: moderateScale(25),
      // backgroundColor: '#007AFF',
      justifyContent: 'center',
      alignItems: 'center',
      // marginRight: 15,
    },
    avatarText: {
      // fontSize: fontSize.l,
    },
    activeIndicator: {
      position: 'absolute',
      bottom: 8,
      right: 0,
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#4CAF50',
    },
    chatDetails: {
      flex: 1,
      flexDirection:"row",
      justifyContent:"space-between"
      
    },
    chatName: {
      fontSize: fontSize.m,
    },
    chatMessage: {
      fontSize: fontSize.s,

      marginTop: verticalScale(2),
    },
    chatMeta: {
      // alignSelf: 'flex-start',
      flex : 1,
    },
    chatTime: {
      // fontSize: fontSize.xs,

      // marginTop: -4,
    },
    unReadMessages: {
      alignContent: 'center',
      marginRight: scale(10),
      padding: moderateScale(4),
      backgroundColor: colors.primary,
      borderRadius: moderateScale(20),
      marginTop: verticalScale(4),
      alignItems: 'center',
      justifyContent: 'center',
      minWidth: scale(24),
    },
    emptyText: {},
    fabStyle: {
      bottom: moderateScale(16),
      right: moderateScale(16),
      position: 'absolute',
    },
  });
};
