import React, {useMemo} from 'react';
import {View, Pressable, StyleSheet} from 'react-native';

import {moderateScale} from 'react-native-size-matters';
import {
  Text,
  Avatar,
  Badge,
  useTheme as usePaperTheme,
} from 'react-native-paper';
import {useNavigation, NavigationProp} from '@react-navigation/native';
import {useDispatch} from 'react-redux';

import {createStyles} from '../ChatRoomList.styles';
import {formatLastMessageTime} from '../../../../../../src/Utils/DateUtils';
import {RootStackParamList} from '../../../../../Common/Routes/StackTypes';
import {letterColors, textColors} from '../../../constants/colors';
import {setRoomId} from '../../../../../../src/State/Slices/ChatSlices/RoomId';
import {getFirstLetters} from '../../../../../../src/Utils/formatText';
// import TestingScreen from '../ChatRoom/Attachment/TestingScreen';
// import userOnlineStatus from '../../../Permissions/userOnlineStatus';
import {ChatRoomItemProps} from '../ChatRoomList.types';

type MessageType = 'adaptive-card' | 'file' | 'text';
const ChatRoomItem: React.FC<ChatRoomItemProps> = ({item}) => {
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const paperTheme = usePaperTheme();
  const roomDetails = item.room_details || {};
  const roomName = roomDetails.name || 'Unknown';
  const letters = getFirstLetters(roomName.toUpperCase());
  const firstLetter = letters?.charAt(0);

  const avatarProps = useMemo(() => {
    const backgroundColor = firstLetter ? letterColors[firstLetter] : '#CCCCCC';
    const textColor = firstLetter ? textColors[firstLetter] : '#000000';
    return {backgroundColor, textColor};
  }, [firstLetter]);

  const lastMessageObj = roomDetails.lastMessage || {};
  const messageType = lastMessageObj.message_type?.toLowerCase();
  const messageTypeMap: Record<MessageType, string | undefined> = {
    'adaptive-card': 'AdaptiveCard',
    file: 'File Data',
    text: lastMessageObj.payload,
  };

  const lastMessage = messageType
    ? messageTypeMap[messageType as MessageType] ||
      'Unknown message type'
    : 'No messages yet';

  const lastMessageTime = formatLastMessageTime(
    lastMessageObj.created_date || item.last_updated,
  );

  const unReadMessages = item?.unread_message_count || 0;

  const handleChatRoomPress = () => {
    dispatch(setRoomId(item?.room_id));
    // navigation.navigate('ChatRoom', { data: item });
    navigation.navigate('ChatStack', {
      screen: 'ChatRoom',
      params: {
        data: item,
      },
    });
  };
  const styles = createStyles();
  const size = moderateScale(40)
  return (
    <Pressable
      style={[styles.chatItem, localStyles.chatItem]}
      onPress={handleChatRoomPress}
      onLongPress={() => {
        // Handle long press
        console.log(' i am in long press');
      }}
      android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
    >
      <View style={localStyles.avatarContainer}>
        {/* <Avatar.Text
          size={moderateScale(40)}
          label={letters || '?'}
          style={{backgroundColor: avatarProps.backgroundColor}}
          labelStyle={{
            color: avatarProps.textColor,
            fontSize: moderateScale(14),
            
          }}
        /> */}
        <View
          style={[
            styles.avatar,
            {
              width: size,
              height: size,

              borderRadius: size / 2,
              backgroundColor: avatarProps.backgroundColor,
            },
          ]}
        >
          <Text
            style={{ color: avatarProps.textColor, fontSize: moderateScale(14) }}
            variant="semiBold"
          >
            {letters || '?'}
          </Text>
        </View>
      </View>
      <View style={{flex:1,borderColor:"#eee",borderBottomWidth:1,marginVertical:moderateScale(10),paddingBottom:moderateScale(10)}}>
        <View style={[styles.chatDetails, localStyles.chatDetails]}>
          <Text
            variant="semiBold"
            style={{
              color: paperTheme.colors.onSurface,
              fontSize: moderateScale(12),
            }}
          >
            {roomName}
          </Text>
          <Text
            variant="regular"
            style={{
              color: paperTheme.colors.onSurfaceVariant,
              fontSize: moderateScale(10),
            }}
          >
            {lastMessageTime}
          </Text>
        </View>

        <View style={[styles.chatMeta, localStyles.chatMeta]}>
          <View style = {{flex : 1, marginRight :10}}>

          <Text
            variant="regular"
            style={{
              color: paperTheme.colors.onSurfaceVariant,
              fontSize: moderateScale(11),
              marginTop: moderateScale(2),
            }}
            numberOfLines={1}
          >
            {lastMessage}
          </Text>
          </View>
          {unReadMessages > 0 && (
            <View
              // size={moderateScale(22)}
              style={{
                // flex : 1,
                width : '6%',
                height : '100%',
                borderRadius : 50,
                alignItems : 'center',
                justifyContent : 'center',
                backgroundColor: paperTheme.colors.primary,
                //paddingRight : 10
                // marginTop: moderateScale(4),
              }}
            >
              <Text style = {{ color : '#fff', fontSize : 11} } variant='medium' >
              {unReadMessages}
              </Text>
            </View>
          )}
        </View>
      </View>
    </Pressable>
  );
};

const localStyles = StyleSheet.create({
  chatItem: {
    // paddingVertical: moderateScale(10),
    paddingHorizontal: moderateScale(5),
    alignItems: 'center',
    // height: moderateScale(70),
  },
  avatarContainer: {
    // height: moderateScale(45),
    // width: moderateScale(45),
    marginRight: moderateScale(10),
    justifyContent: 'center',
    alignItems: 'center',
  },
  chatDetails: {
    // height: moderateScale(45),
    // justifyContent: 'center',
  },
  chatMeta: {
    // height: moderateScale(45),
    // justifyContent: 'center',
    flexDirection:"row",
    // paddingRight: moderateScale(5),
    gap : 2,
    justifyContent : 'space-between'
  },
});

export default React.memo(ChatRoomItem);
