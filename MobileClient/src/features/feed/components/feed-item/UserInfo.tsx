import React from 'react';
import {View, StyleSheet} from 'react-native';
import {Avatar, Text, useTheme} from 'react-native-paper';
import {moderateScale} from 'react-native-size-matters';
import {getShortRelativeTime} from '../../utils/dataFormatter';

type UserInfoProps = {
  profilePic: string;
  userName: string;
  createdAt: string;
};

export const UserInfo: React.FC<UserInfoProps> = ({
  profilePic,
  userName,
  createdAt,
}) => {
  const theme = useTheme();
  const relativeTime = getShortRelativeTime(createdAt);

  return (
    <View style={styles.container}>
      <Avatar.Image
        size={moderateScale(42)}
        source={{uri: profilePic}}
        style={[styles.avatar, {backgroundColor: theme.colors.surfaceVariant}]}
      />
      <View style={styles.textContainer}>
        <Text
          variant="semiBold"
          style={[styles.userName, {color: theme.colors.onSurface}]}
          numberOfLines={1}>
          {userName}
        </Text>
        <Text
          variant="regular"
          style={[styles.timeText, {color: theme.colors.onSurfaceVariant}]}>
          {relativeTime}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginBottom: moderateScale(12),
  },
  avatar: {
    marginRight: moderateScale(12),
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  userName: {
    // fontFamily: 'Montserrat-SemiBold',
    fontSize: moderateScale(12),
    letterSpacing: 0.1,
  },
  timeText: {
    marginTop: moderateScale(2),
    // fontFamily: 'Montserrat-Regular',
    fontSize: moderateScale(11),
    opacity: 0.8,
  },
});

export default UserInfo;
