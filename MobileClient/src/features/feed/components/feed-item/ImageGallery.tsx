import React, {useState, useMemo} from 'react';
import {View, TouchableOpacity, StyleSheet, Text} from 'react-native';
import {useTheme} from 'react-native-paper';
import FastImage from 'react-native-fast-image';
import {moderateScale} from 'react-native-size-matters';
import ImagePreview from '../common/ImagePreview';

interface ImageGalleryProps {
  images: string[];
  rightImagesHeight?: number;
}

interface ImageItemProps {
  uri: string;
  height: number;
  width?: string;
  onPress?: () => void;
}

const ImageItem: React.FC<ImageItemProps> = ({uri, height, width, onPress}) => {
  const theme = useTheme();

  return (
    <TouchableOpacity
      onPress={onPress}
      style={[
        width ? {width} : styles.flexOne,
        {borderRadius: moderateScale(8)},
      ]}
      activeOpacity={0.9}>
      <FastImage
        source={{uri}}
        style={[
          styles.image,
          {
            height,
            borderRadius: moderateScale(8),
            borderColor: theme.colors.surfaceVariant,
          },
        ]}
        resizeMode="cover"
      />
    </TouchableOpacity>
  );
};

const RemainingImagesOverlay = ({
  count,
  height,
}: {
  count: number;
  height: number;
}) => {
  const theme = useTheme();

  return (
    <View
      style={[
        styles.overlay,
        {
          height,
          borderRadius: moderateScale(8),
        },
      ]}>
      <Text style={styles.overlayText}>+{count}</Text>
    </View>
  );
};

export const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  rightImagesHeight = moderateScale(264),
}) => {
  const theme = useTheme();
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isPreviewModalVisible, setIsPreviewModalVisible] = useState(false);

  const handleImagePress = (index: number) => {
    console.log('Opening image preview at index:', index);
    setSelectedImageIndex(index);
    setIsPreviewModalVisible(true);
  };

  const handleClosePreview = () => {
    setIsPreviewModalVisible(false);
  };

  if (!images || images.length === 0) return null;

  const rightSectionImageHeight = useMemo(() => {
    const spacing = moderateScale(4);
    return images.length === 3
      ? (rightImagesHeight - spacing) / 2
      : (rightImagesHeight - spacing * 2) / 3;
  }, [images.length, rightImagesHeight]);

  if (images.length === 1) {
    return (
      <>
        <View style={styles.container}>
          <ImageItem
            uri={images[0]}
            height={moderateScale(288)}
            onPress={() => handleImagePress(0)}
          />
        </View>

        <ImagePreview
          visible={isPreviewModalVisible}
          onClose={handleClosePreview}
          images={images}
          initialIndex={selectedImageIndex}
        />
      </>
    );
  }

  if (images.length === 2) {
    return (
      <>
        <View style={[styles.container, styles.row]}>
          {images.map((uri, index) => (
            <ImageItem
              key={index}
              uri={uri}
              height={moderateScale(256)}
              onPress={() => handleImagePress(index)}
            />
          ))}
        </View>

        <ImagePreview
          visible={isPreviewModalVisible}
          onClose={handleClosePreview}
          images={images}
          initialIndex={selectedImageIndex}
        />
      </>
    );
  }

  return (
    <>
      <View style={[styles.container, styles.row]}>
        <ImageItem
          uri={images[0]}
          height={rightImagesHeight}
          width="66%"
          onPress={() => handleImagePress(0)}
        />

        <View style={[styles.rightColumn, styles.spaceBetween]}>
          {images.slice(1, 3).map((uri, index) => (
            <ImageItem
              key={index}
              uri={uri}
              height={rightSectionImageHeight}
              onPress={() => handleImagePress(index + 1)}
            />
          ))}

          {images.length >= 4 && (
            <View style={styles.relativeContainer}>
              <TouchableOpacity onPress={() => handleImagePress(3)}>
                <FastImage
                  source={{uri: images[3]}}
                  style={[
                    styles.image,
                    {
                      height: rightSectionImageHeight,
                      borderRadius: theme.roundness,
                      opacity: images.length > 4 ? 0.4 : 1,
                      borderColor: theme.colors.surfaceVariant,
                    },
                  ]}
                  resizeMode="cover"
                />
                {images.length > 4 && (
                  <RemainingImagesOverlay
                    count={images.length - 4}
                    height={rightSectionImageHeight}
                  />
                )}
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>

      <ImagePreview
        visible={isPreviewModalVisible}
        onClose={handleClosePreview}
        images={images}
        initialIndex={selectedImageIndex}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: moderateScale(12),
    gap: moderateScale(4),
    borderRadius: moderateScale(8),
    overflow: 'hidden',
  },
  row: {
    flexDirection: 'row',
  },
  flexOne: {
    flex: 1,
  },
  rightColumn: {
    flex: 1,
    marginLeft: moderateScale(4),
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  image: {
    width: '100%',
    borderWidth: 0.5,
  },
  relativeContainer: {
    position: 'relative',
    overflow: 'hidden',
    borderRadius: moderateScale(8),
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  overlayText: {
    fontSize: moderateScale(20),
    fontWeight: 'bold',
    fontFamily: 'Montserrat-Bold',
    color: 'white',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: {width: 0, height: 1},
    textShadowRadius: 2,
  },
  borderStyle: {
    borderWidth: 0.5,
  },
});

export default ImageGallery;
