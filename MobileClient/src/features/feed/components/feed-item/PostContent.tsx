import React, {useState} from 'react';
import { TouchableOpacity } from 'react-native';
import {View, StyleSheet} from 'react-native';
import {Text,  useTheme} from 'react-native-paper';
import {moderateScale} from 'react-native-size-matters';

type PostContentProps = {
  payload: string;
  maxLength?: number;
};

export const PostContent: React.FC<PostContentProps> = ({
  payload,
  maxLength = 150,
}) => {
  const theme = useTheme();
  const [isTextExpanded, setIsTextExpanded] = useState(false);

  if (!payload) return null;

  const shouldTruncate = payload && payload.length > maxLength;
  const displayText = isTextExpanded
    ? payload
    : shouldTruncate
    ? `${payload.slice(0, maxLength)}`
    : payload;

  return (
    <View style={styles.container}>
      <Text style={[styles.text, { color: theme.colors.onSurface }]} variant="regular">
        {displayText}
        {shouldTruncate && !isTextExpanded && '... '}
        {shouldTruncate && !isTextExpanded && (
          <TouchableOpacity style={{justifyContent:'flex-end'}} onPress={() => setIsTextExpanded(true)} activeOpacity={0.7}>
            <Text style={[styles.readMore, { color: theme.colors.primary }]} variant="medium">
              Read more
            </Text>
          </TouchableOpacity>
        )}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: moderateScale(6),
    marginBottom: moderateScale(6),
  },
  text: {
    fontSize: moderateScale(12),
    // backgroundColor:"blue",
    // lineHeight: moderateScale(20),
    // fontFamily: 'Montserrat-Regular',
    // letterSpacing: 0.1,
  },
  readMore:{fontSize:moderateScale(12),padding:0,marginBottom:moderateScale(-4),alignSelf:"flex-end"},
  button: {
    marginTop: moderateScale(2),
    alignSelf: 'flex-end',
    padding: 0,
  },
  buttonLabel: {
    fontSize: moderateScale(13),
    letterSpacing: 0.1,
  },
});

export default PostContent;
