import { useEffect } from 'react';
import { useSubscription } from '@apollo/client';
import { ON_LIKE_AND_UNLIKE_POST } from '../graphql/subscriptions';

export const useLikeSubscription = (postId: string, onLikesCountChange: (count: number) => void) => {
  const { data: likeData } = useSubscription(ON_LIKE_AND_UNLIKE_POST, {
    variables: { post_id: postId },
    skip: !postId,
  });

  useEffect(() => {
    if (likeData?.onLikeAndUnlikePost?.likes_count !== undefined) {
      onLikesCountChange(likeData.onLikeAndUnlikePost.likes_count);
    }
  }, [likeData, onLikesCountChange]);

  return null;
};
