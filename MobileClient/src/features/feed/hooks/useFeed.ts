import {useQuery, useSubscription} from '@apollo/client';
import {GET_FEED} from '../graphql/queries';
import {useSelector} from 'react-redux';
import {ON_CREATE_POST} from '../graphql/subscriptions';

interface UseFeedOptions {
  limit?: number;
}

export const useFeed = ({limit = 10}: UseFeedOptions = {}) => {
  const user = useSelector((state: any) => state.userId);
  const {data: FeedData, loading, error, refetch} = useQuery(GET_FEED, {
    variables: {user_id: user},
  });
  
  return {
    feedData: FeedData?.getFeed?.items,
    loading,
    error,
    refetchPosts: refetch,
  };
};

export const useFeedSubscriptionData = () => {
  const user = useSelector((state: any) => state.userId);
  const {data: subscriptionFeedData} = useSubscription(ON_CREATE_POST, {
    variables: {user_id: user},
  });

  return {subscriptionFeedData};
};
