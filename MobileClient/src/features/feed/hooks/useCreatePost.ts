import {useState} from 'react';
import {useMutation} from '@apollo/client';
import {CREATE_FEED_POST} from '../graphql/mutations';
import {showToast} from '../../../Components/AppToaster/AppToaster';
import { getEnvConfig } from '../../chat/api/chatCredentials';
// Import these from the chat module or create your own versions
import {uploadToS3} from '../../../Stacks/ChatStack/ChatRoom/Attachment/aws.functions';
import {formattedJSONToStringfy} from '../../../Utils/formatText';
import {formatAttachmentsData} from '../../../Stacks/ChatStack/ChatUtils/ChatInputUtils';

interface ImageResponse {
  base64: string;
  fileName: string;
  fileSize: number;
  type: string;
  uri: string;
}

interface CreatePostProps {
  userId: string;
  setUploading: (value: boolean) => void;
  setProgress: (value: number) => void;
  setContent: (value: string) => void;
  setRawImages: (value: ImageResponse[]) => void;
  rawImages: ImageResponse[];
  canPost: boolean;
  content: string;
}

export const useCreatePost = ({
  userId,
  setUploading, // This might be undefined, we'll use global functions instead
  setProgress, // This might be undefined, we'll use global functions instead
  content,
  setContent,
  setRawImages,
  rawImages,
  canPost,
}: CreatePostProps) => {
  const [loading, setLoading] = useState(false);
  const [isResponse, setIsResponse] = useState(false);

  const [createFeedPost] = useMutation(CREATE_FEED_POST, {
    variables: {user_id: userId},
  });

  const createPostHandler = async () => {
    if (!canPost) return;

    try {
      setLoading(true);

      // Use the global function if available, otherwise use the passed function
      try {
        if (typeof (global as any).feedSetUploading === 'function') {
          console.log('Using global feedSetUploading');
          (global as any).feedSetUploading(true);
        } else if (typeof setUploading === 'function') {
          console.log('Using passed setUploading');
          setUploading(true);
        } else {
          console.log('No valid setUploading function available');
        }
      } catch (error) {
        console.log('Error calling setUploading:', error);
      }

      let uploadResult: any[] = [];

      if (Array.isArray(rawImages) && rawImages.length > 0) {
        try {
          const formattedAttachments = formatAttachmentsData(rawImages);

          // Create wrapper functions for uploadToS3
          const setUploadingWrapper = (value: boolean) => {
            try {
              if (typeof (global as any).feedSetUploading === 'function') {
                console.log(
                  'Wrapper calling global feedSetUploading with:',
                  value,
                );
                (global as any).feedSetUploading(value);
              } else if (typeof setUploading === 'function') {
                console.log('Wrapper calling passed setUploading with:', value);
                setUploading(value);
              }
            } catch (error) {
              console.log('Error in setUploadingWrapper:', error);
            }
          };

          const setProgressWrapper = (value: number) => {
            try {
              if (typeof (global as any).feedSetProgress === 'function') {
                console.log(
                  'Wrapper calling global feedSetProgress with:',
                  value,
                );
                (global as any).feedSetProgress(value);
              } else if (typeof setProgress === 'function') {
                console.log('Wrapper calling passed setProgress with:', value);
                setProgress(value);
              }
            } catch (error) {
              console.log('Error in setProgressWrapper:', error);
            }
          };

          // Call uploadToS3 with the wrapper functions
          console.log('Calling uploadToS3 with formatted attachments:', {
            attachmentsCount: formattedAttachments.length,
            firstAttachment: formattedAttachments[0],
            uploadToS3Exists: typeof uploadToS3 === 'function',
          });

          const config = await getEnvConfig();

          const results = await uploadToS3(
            formattedAttachments,
            setUploadingWrapper,
            setProgressWrapper,
            config?.AWS_FEED_BUCKET_NAME
          );

          console.log('Upload completed, results:', results);

          if (Array.isArray(results)) {
            console.log('Upload results array length:', results.length);
            uploadResult = results.filter(result => result && result.url);
            console.log('Filtered upload results:', uploadResult);
          } else {
            console.log('Upload results is not an array:', typeof results);
          }
        } catch (error) {
          console.log('Error uploading images:', error);
          setLoading(false);

          // Use the global function if available, otherwise use the passed function
          try {
            if (typeof (global as any).feedSetUploading === 'function') {
              console.log('Using global feedSetUploading in catch block');
              (global as any).feedSetUploading(false);
            } else if (typeof setUploading === 'function') {
              console.log('Using passed setUploading in catch block');
              setUploading(false);
            } else {
              console.log(
                'No valid setUploading function available in catch block',
              );
            }
          } catch (error) {
            console.log('Error calling setUploading in catch block:', error);
          }

          return;
        }
      }

      try {
        const input = {
          user_id: userId,
          payload: content?.trim(),
          post_type: uploadResult.length > 0 ? 'file' : 'text',
          attachments:
            uploadResult.length > 0
              ? formattedJSONToStringfy(uploadResult)
              : null,
        };

        const response = await createFeedPost({variables: {input}});

        if (response) {
          setIsResponse(true);
          showToast.success('Post created successfully');
          console.log('Post created successfully:', response);
        }
      } catch (err: any) {
        setIsResponse(false);
        showToast.error('Failed to create post');
        console.log('Error creating post:', err.message);
      } finally {
        setContent('');
        setRawImages([]);
        setLoading(false);

        // Use the global function if available, otherwise use the passed function
        try {
          if (typeof (global as any).feedSetUploading === 'function') {
            console.log('Using global feedSetUploading in finally block');
            (global as any).feedSetUploading(false);
          } else if (typeof setUploading === 'function') {
            console.log('Using passed setUploading in finally block');
            setUploading(false);
          } else {
            console.log(
              'No valid setUploading function available in finally block',
            );
          }
        } catch (error) {
          console.log('Error calling setUploading in finally block:', error);
        }
      }
    } catch (error) {
      console.log('Unexpected error in createPostHandler:', error);
      setLoading(false);

      // Use the global function if available, otherwise use the passed function
      try {
        if (typeof (global as any).feedSetUploading === 'function') {
          console.log('Using global feedSetUploading in outer catch block');
          (global as any).feedSetUploading(false);
        } else if (typeof setUploading === 'function') {
          console.log('Using passed setUploading in outer catch block');
          setUploading(false);
        } else {
          console.log(
            'No valid setUploading function available in outer catch block',
          );
        }
      } catch (err) {
        console.log('Error calling setUploading in outer catch block:', err);
      }
    }
  };

  return {createPostHandler, loading, isResponse};
};
