import {useQuery, useSubscription, useLazyQuery} from '@apollo/client';
import {
  ON_LIKE_AND_UNLIKE_POST,
  ON_CREATE_COMMENT,
} from '../graphql/subscriptions';
import {GET_COMMENTS} from '../graphql/queries';

/**
 * Custom hook to handle post like subscription
 * @param postId - ID of the post to subscribe to like updates
 * @param onLikesCountChange - Callback function when likes count changes
 */
export const useLikeSubscription = (
  postId: string,
  onLikesCountChange: (count: number) => void,
) => {
  return useSubscription(ON_LIKE_AND_UNLIKE_POST, {
    variables: {post_id: postId},
    skip: !postId,
    fetchPolicy: 'network-only', // Always fetch from network
    onData: ({data}) => {
      const subscriptionData = data?.data?.onLikeAndUnlikePost;
      const likesCount = subscriptionData?.likes_count;

      console.log('Like subscription update received:', {
        postId,
        likesCount,
        subscriptionData,
        success: subscriptionData?.success,
      });

      if (likesCount !== undefined && subscriptionData?.success) {
        console.log('Updating likes count from subscription:', likesCount);
        onLikesCountChange(likesCount);
      }
    },
  });
};

/**
 * Custom hook to handle comment count subscription
 * @param postId - ID of the post to subscribe to comment updates
 * @param onCommentsCountChange - Callback function when comments count changes
 */
export const useCommentSubscription = (
  postId: string,
  onCommentsCountChange: (count: number) => void,
) => {
  return useSubscription(ON_CREATE_COMMENT, {
    variables: {post_id: postId},
    skip: !postId,
    fetchPolicy: 'network-only', // Always fetch from network
    onData: ({data}) => {
      const commentData = data?.data?.onCreateComment;
      const commentsCount = commentData?.comments_count;

      console.log('Comment subscription update received:', {
        postId,
        commentsCount,
      });

      if (commentsCount !== undefined) {
        console.log(
          'Updating comments count from subscription:',
          commentsCount,
        );
        onCommentsCountChange(commentsCount);
      }
    },
  });
};

/**
 * Function to handle post like/unlike action
 * @param likeUnlikeMutation - The mutation function to use
 * @param userId - Current user ID
 * @param postId - Post ID to like/unlike
 * @param postCreatedAt - Post creation timestamp
 * @returns Promise with like/unlike mutation result
 */
export const likeUnlikePost = async (
  likeUnlikeMutation: any,
  userId: string,
  postId: string,
  postCreatedAt: string,
) => {
  if (!userId) {
    console.log('No user ID provided for like/unlike');
    return {success: false};
  }

  const likeAndUnLikeInput = {
    user_id: userId,
    post_id: postId,
    post_created_at: postCreatedAt,
  };

  console.log('Like/unlike input:', likeAndUnLikeInput);

  try {
    const response = await likeUnlikeMutation({
      variables: {input: likeAndUnLikeInput},
    });

    const result = response?.data?.likeAndUnLikePost;
    console.log('Like/unlike response:', result);

    if (!result) {
      return {success: false};
    }

    // Since the API doesn't return is_like_action, we'll infer it from the input
    // This assumes that if the API call was successful, the action was applied as requested
    return {
      success: result.success || false,
      likesCount: result.likes_count,
      // We don't have is_like_action from the server, so we'll return null
      // The component will use the optimistic update value instead
      isLikeAction: null,
    };
  } catch (error) {
    console.log('Like/unlike mutation error:', error);
    return {success: false, error};
  }
};

export const queryComments = (postId: string) => {
  const [fetchComments, {data: commentsResponseData, loading, error}] =
    useLazyQuery(GET_COMMENTS, {
      variables: {post_id: postId},
      fetchPolicy: 'network-only',
    });
  return {
    fetchComments: fetchComments,
    commentsData: commentsResponseData,
    commentsLoader: loading,
    commentsError: error,
  };
};
