import {useState, useEffect} from 'react';
import {useMutation, useQuery, useSubscription} from '@apollo/client';
import {useSelector} from 'react-redux';
import {GET_COMMENTS} from '../graphql/queries';
import {ADD_COMMENT} from '../graphql/mutations';
import {ON_CREATE_COMMENT} from '../graphql/subscriptions';
import {Comment} from '../types/comment.types';

export const useComments = (postId: string, postCreatedAt?: string) => {
  const userId = useSelector((state: any) => state.userId);
  const [comments, setComments] = useState<Comment[]>([]);
  const [replyingTo, setReplyingTo] = useState<any>(null);
  const [parentCommentId, setParentCommentId] = useState('root');
  const [rootParentId, setRootParentId] = useState('root');
  const [replyCommentId, setReplyCommentId] = useState('');

  // Query to fetch comments
  const {
    data: commentsData,
    loading: commentsLoading,
    error: commentsError,
  } = useQuery(GET_COMMENTS, {
    variables: {post_id: postId?.toString() || '', root_parent_id: 'root'},
    skip: !postId,
    fetchPolicy: 'network-only',
    errorPolicy: 'all',
    onError: error => {
      console.log('Error fetching comments:', error);
    },
  });

  // Log for debugging
  useEffect(() => {
    if (commentsError) {
      console.log('Comments query error:', commentsError);
    }
  }, [commentsError]);

  // Log for debugging
  useEffect(() => {
    if (commentsData) {
      console.log('Comments data received:', {
        postId,
        itemsCount: commentsData?.getComments?.items?.length || 0,
      });
    }
  }, [commentsData, postId]);

  // Mutation to add a comment
  const [addComment, {loading: addCommentLoading}] = useMutation(ADD_COMMENT);

  // Subscription for new comments
  const {data: newCommentData} = useSubscription(ON_CREATE_COMMENT, {
    variables: {post_id: postId},
    skip: !postId,
  });

  // Update comments when data changes
  useEffect(() => {
    try {
      if (commentsData?.getComments?.items) {
        // Filter out any invalid comments
        const validComments = commentsData.getComments.items.filter(
          (item: any) => {
            return (
              item && typeof item === 'object' && item.id && item.created_at
            );
          },
        );
        console.log('Valid comments count:', validComments.length);

        // Add uniqueId to each comment for stable rendering
        const commentsWithIds = validComments.map((comment: any) => ({
          ...comment,
          uniqueId:
            comment.id ||
            `comment-${Date.now()}-${Math.random()
              .toString(36)
              .substring(2, 9)}`,
        }));

        // Sort comments by created_at in descending order (newest first)
        const sortedComments = commentsWithIds.sort((a: any, b: any) => {
          try {
            const dateA = new Date(a.created_at).getTime();
            const dateB = new Date(b.created_at).getTime();
            return dateB - dateA;
          } catch (error) {
            console.log('Error sorting comments:', error);
            return 0;
          }
        });

        setComments(sortedComments);
      } else {
        // If no comments data, set empty array
        setComments([]);
      }
    } catch (error) {
      console.log('Error processing comments data:', error);
      // Set empty array on error
      setComments([]);
    }
  }, [commentsData]);

  // Handle new comment from subscription
  useEffect(() => {
    try {
      if (newCommentData?.onCreateComment) {
        const newComment = newCommentData.onCreateComment;

        // Validate the new comment
        if (!newComment || typeof newComment !== 'object' || !newComment.id) {
          console.log('Invalid new comment from subscription:', newComment);
          return;
        }

        console.log('New comment from subscription:', newComment.id);

        // Only add root comments to the list
        if (
          newComment.root_parent_id === 'root' &&
          newComment.parent_comment_id === 'root'
        ) {
          // Check if comment already exists
          const exists = comments.some(c => c.id === newComment.id);
          if (!exists) {
            // Add uniqueId for stable rendering
            const commentWithId = {
              ...newComment,
              uniqueId:
                newComment.id ||
                `comment-${Date.now()}-${Math.random()
                  .toString(36)
                  .substring(2, 9)}`,
            };
            setComments(prev => [commentWithId, ...prev]);
          }
        }
      }
    } catch (error) {
      console.log('Error handling comment subscription:', error);
    }
  }, [newCommentData, comments]);

  // Handler for replying to a comment
  const handleReplyTo = (
    parentId: string,
    rootId: string,
    replyingToUserId: string,
    replyingToUserName: string,
    repliedCommentId: string,
  ) => {
    setParentCommentId(parentId);
    setRootParentId(rootId);
    setReplyingTo({
      userId: replyingToUserId,
      userName: replyingToUserName,
    });
    setReplyCommentId(repliedCommentId);
  };

  // Handler for erasing reply info
  const eraseReplyComment = () => {
    setReplyingTo(null);
    setParentCommentId('root');
    setRootParentId('root');
    setReplyCommentId('');
  };

  // Handler for submitting a comment
  const handleSubmitComment = async (commentData: any) => {
    try {
      if (!commentData?.comment?.trim()) {
        console.log('Empty comment, not submitting');
        return;
      }

      // Validate required fields
      if (!postId) {
        console.log('Cannot submit comment: postId is missing');
        return;
      }

      if (!userId) {
        console.log('Cannot submit comment: userId is missing');
        return;
      }

      console.log('Submitting comment:', {
        postId,
        userId,
        comment: commentData.comment,
        parentCommentId: commentData.parentCommentId || 'root',
        rootParentId: commentData.rootParentId || 'root',
      });

      // Use the provided postCreatedAt or default to current date
      const postCreatedDate = postCreatedAt || new Date().toISOString();

      console.log('Adding comment with post_created_date:', postCreatedDate);

      // Create a temporary comment to show immediately
      const tempId = `temp-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 9)}`;
      const tempComment: any = {
        id: tempId,
        uniqueId: tempId,
        post_id: postId,
        content: commentData.comment,
        created_at: new Date().toISOString(),
        parent_comment_id: commentData.parentCommentId || 'root',
        root_parent_id: commentData.rootParentId || 'root',
        comment_by: {
          user_id: userId,
          user_name: 'You',
          role: 'User',
          profile_pic: 'https://via.placeholder.com/36',
        },
        is_replies: false,
      };

      // Add the temporary comment to the state immediately
      setComments(prev => [tempComment, ...prev]);

      try {
        // Prepare the variables for the mutation
        const variables = {
          post_id: postId,
          user_id: userId,
          post_created_date: postCreatedDate,
          content: commentData.comment,
          parent_comment_id: commentData.parentCommentId || 'root',
          root_parent_id: commentData.rootParentId || 'root',
          reply_to_user_id: commentData.replyingToUserId || null,
        };

        console.log('Mutation variables:', variables);

        // Execute the mutation
        const response = await addComment({
          variables: variables,
        });

        console.log('Comment added successfully:', response?.data);

        // If the comment was added successfully, update the temporary comment
        if (response?.data?.addComment) {
          const newComment = response.data.addComment;
          if (
            newComment.root_parent_id === 'root' &&
            newComment.parent_comment_id === 'root'
          ) {
            // Add uniqueId for stable rendering
            const commentWithId = {
              ...newComment,
              uniqueId:
                newComment.id ||
                `comment-${Date.now()}-${Math.random()
                  .toString(36)
                  .substring(2, 9)}`,
            };

            // Replace the temporary comment with the real one
            setComments(prev =>
              prev.map(comment =>
                comment.id === tempId ? commentWithId : comment,
              ),
            );
          }
        }
      } catch (mutationError) {
        console.log('Error in comment mutation:', mutationError);
        // Remove the temporary comment on error
        setComments(prev => prev.filter(comment => comment.id !== tempId));
      }
    } catch (error) {
      console.log('Error adding comment:', error);
    }
  };

  return {
    comments,
    commentsLoading,
    commentsError,
    addCommentLoading,
    replyingTo,
    parentCommentId,
    rootParentId,
    replyCommentId,
    handleReplyTo,
    eraseReplyComment,
    handleSubmitComment,
  };
};
