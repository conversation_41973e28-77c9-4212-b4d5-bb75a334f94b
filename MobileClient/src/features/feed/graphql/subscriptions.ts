import {gql} from '@apollo/client';

export const ON_LIKE_AND_UNLIKE_POST = gql`
  subscription onLikeAndUnlikePost($post_id: ID!) {
    onLikeAndUnlikePost(post_id: $post_id) {
      success
      message
      likes_count
      post_id
    }
  }
`;

export const ON_CREATE_COMMENT = gql`
  subscription onCreateComment($post_id: ID!) {
    onCreateComment(post_id: $post_id) {
      content
      post_id
      comment_by {
        email_id
        follower_count
        is_following
        is_online
        profile_pic
        role
        user_name
        user_id
      }
      reply_to_user_details {
        email_id
        follower_count
        is_following
        is_online
        profile_pic
        role
        user_id
        user_name
      }
      comments_count
      created_at
      id
      is_replies
      parent_comment_id
      root_parent_id
      reply_to_user_id
      updated_at
      user_id
    }
  }
`;

export const ON_CREATE_POST = gql`
  subscription MySubscription($user_id: String!) {
    onLambdaPost(user_id: $user_id) {
      user_ids
    }
  }
`;
