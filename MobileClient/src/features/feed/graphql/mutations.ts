import {gql} from '@apollo/client';

export const ADD_FOLLOWER = gql`
  mutation addFollower($input: followerInput!) {
    addFollower(input: $input) {
      follow_at
      follower_id
      user_id
    }
  }
`;

export const CREATE_FEED_POST = gql`
  mutation CreateFeedPost($input: CreatePostInput!) {
    createPost(input: $input) {
      id
    }
  }
`;

export const UNFOLLOW_USER = gql`
  mutation unFollow($input: unfollowInput!) {
    unFollow(input: $input) {
      message
      success
    }
  }
`;

export const LIKE_AND_UNLIKE_POST = gql`
  mutation LikeAndUnLikePost($input: likeAndUnLikeInput!) {
    likeAndUnLikePost(input: $input) {
      success
      message
      likes_count
      post_id
    }
  }
`;

export const ADD_COMMENT = gql`
  mutation AddComment(
    $post_id: ID!
    $user_id: ID!
    $post_created_date: AWSDateTime!
    $content: String!
    $parent_comment_id: ID!
    $root_parent_id: ID!
    $reply_to_user_id: ID
  ) {
    addComment(
      input: {
        post_id: $post_id
        user_id: $user_id
        post_created_date: $post_created_date
        content: $content
        parent_comment_id: $parent_comment_id
        root_parent_id: $root_parent_id
        reply_to_user_id: $reply_to_user_id
      }
    ) {
      content
      post_id
      comment_by {
        email_id
        follower_count
        is_following
        is_online
        profile_pic
        role
        user_name
        user_id
      }
      reply_to_user_details {
        email_id
        follower_count
        is_following
        is_online
        profile_pic
        role
        user_id
        user_name
      }
      comments_count
      created_at
      id
      is_replies
      parent_comment_id
      root_parent_id
      reply_to_user_id
      updated_at
      user_id
    }
  }
`;
