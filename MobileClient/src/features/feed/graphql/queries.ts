import { gql } from '@apollo/client';

export const GET_ALL_ACCOUNT_USERS = gql`
 query GetAllAccountUsers($account_id:ID!,$user_id:ID!) {
  GetAccountUsers(account_id:$account_id,user_id:$user_id){
    account_id
    user_details {
      email_id
      is_following
      is_online
      profile_pic
      user_id
      user_name
      role
    }
    user_id
  }
}
`;

export const GET_FEED = gql`
  query MyQuery($user_id: ID!) {
    getFeed(input: { user_id: $user_id }) {
      nextToken
      items {
        created_at
        post_id
        sender_id
        user_id
        sender_details {
          user_id
          user_name
          email_id
          is_online
          profile_pic
          role
          follower_count
        }
        post {
          likes_count
          attachments
          payload
          is_current_user_like_the_post
          comments_count
        }
      }
    }
  }
`;

export const GET_USER_BY_ID = gql`
  query GetUserById($user_id: ID!) {
    getUserById(user_id: $user_id) {
      profile_pic
      user_id
      user_name
    }
  }
`;

export const GET_COMMENTS = gql`
  query MyQuery($post_id: String!,$root_parent_id: String) {
  getComments(post_id: $post_id, root_parent_id: $root_parent_id) {
    items {
      content
      post_id
      comment_by {
        email_id
        follower_count
        is_following
        is_online
        profile_pic
        role
        user_name
        user_id
      }
      reply_to_user_details {
        email_id
        follower_count
        is_following
        is_online
        profile_pic
        role
        user_id
        user_name
      }
      comments_count
      created_at
      id
      is_replies
      parent_comment_id
      root_parent_id
      reply_to_user_id
      updated_at
      user_id
    }
  }
}
`;
