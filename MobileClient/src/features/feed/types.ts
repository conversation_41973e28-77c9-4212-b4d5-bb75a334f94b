export interface UserDetails {
  user_id: string;
  user_name: string;
  email_id: string;
  is_online: boolean;
  profile_pic: string;
  role: string;
  follower_count?: number;
  is_following?: boolean;
}

export interface PostData {
  likes_count: number;
  attachments?: string[];
  attachmentsList?: string[];
  payload: string;
  is_current_user_like_the_post: boolean;
  comments_count: number;
}

export interface Posts {
  created_at: string;
  post_id: string;
  sender_id: string;
  user_id: string;
  sender_details: UserDetails;
  post: PostData;
  postData?: PostData;
}

export interface Comment {
  content: string;
  post_id: string;
  comment_by: UserDetails;
  reply_to_user_details?: UserDetails;
  comments_count: number;
  created_at: string;
  id: string;
  is_replies: boolean;
  parent_comment_id: string;
  root_parent_id: string;
  reply_to_user_id?: string;
  updated_at: string;
  user_id: string;
}

export interface FeedItemProps {
  item: Posts;
  onCommentPress?: () => void;
}

export interface FeedListProps {
  posts: Posts[];
  onCommentPress?: () => void;
}

export interface FeedHeaderProps {
  searchText: string;
  onSearchChange: (text: string) => void;
  onPlusPress?: () => void;
}

export interface UserInfoProps {
  profilePic?: string;
  userName: string;
  createdAt: string;
}

export interface PostContentProps {
  payload: string;
}

export interface ImageGalleryProps {
  images?: string[];
}

export interface ActionButtonsProps {
  item: Posts;
  onLikeStatusChange: () => void;
  onCommentPress: () => void;
  showComments: boolean;
  onCommentModalClose: () => void;
  commentsData: Comment[];
  commentsLoader: boolean;
}
