import moment from 'moment';

export const feedFormatData = (feedData: any) => {
  const formattedPosts = feedData.map((post: any) => ({
    ...post,
    postData: {
      ...post.post,
      attachmentsList: post.post.attachments
        ? post.post.attachments.map((item: any) => JSON.parse(item)?.url)
        : [],
    },
  }));
  return formattedPosts;
};

export const getShortRelativeTime = (timestamp: string) => {
  const now = moment();
  const diff = now.diff(moment(new Date(timestamp)), 'seconds');

  if (diff < 60) return `${diff}s`; // Seconds
  if (diff < 3600) return `${Math.floor(diff / 60)}m`; // Minutes
  if (diff < 86400) return `${Math.floor(diff / 3600)}h`; // Hours
  if (diff < 31536000) return `${Math.floor(diff / 86400)}d`; // Days
  return `${Math.floor(diff / 31536000)}y`; // Years
};
