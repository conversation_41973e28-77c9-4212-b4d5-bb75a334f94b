import { Image, StyleSheet, TouchableOpacity, View, Platform } from "react-native"
import { Text } from "react-native-paper"
import AppHeader from "../../../Components/AppHeader"
import { useRoute } from "@react-navigation/native"
import { ScrollView } from "react-native-gesture-handler"
import { moderateScale, scale } from "react-native-size-matters"
import { UserRoundPlus, MapPin, Mail } from "lucide-react-native"

const FollowersDetailsScreen: React.FC = () => {
  const route = useRoute()
  const item = route.params
  console.log("userdetailsfrom", item.user.email_id)

  return (
    <>
      <AppHeader title="Follower Detail Page" />
      <View style={styles.container}>
        <ScrollView
          showsVerticalScrollIndicator={false}
        >
          <View style={{marginHorizontal:moderateScale(6)}}>
            <View style={styles.headerBackground}>
              <Image
                source={require("../../../../assets/ChatImages/User_detail_page_camera_icon.png")}
                resizeMode="contain"
                style={styles.headerImage}
              />
            </View>

            <View style={styles.profileImageWrapper}>
              <View style={styles.profileImageContainer}>
                <Image
                //   defaultSource={require("../../../../assets/ChatImages/User2.png")}
                  resizeMode="cover"
                  style={styles.profileImage}
                  source={item.user.profile_pic? {uri:item.user.profile_pic}: require('../../../../assets/ChatImages/User2.png')}
                />
              </View>
            </View>

            <View style={styles.userInfoCard}>
              <Text style={styles.userName}>{item.user.user_name}</Text>
              
              <View style={styles.infoRow}>
                <MapPin size={16} color='#666' />
                <Text style={styles.infoText}>{item.user.location}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Mail size={16} color='#666' />
                <Text style={styles.infoText}>{item.user.email_id}</Text>
              </View>

              <View style={styles.buttonContainer}>
                {!item.user.is_following&&<TouchableOpacity style={styles.followButton}>
                  <Text style={styles.followButtonText}>+ Follow</Text>
                </TouchableOpacity>
}
                <TouchableOpacity style={styles.connectButton}>
                  <UserRoundPlus size={16} color='#4285F4'/>
                  <Text style={styles.connectButtonText}>Connect</Text>
                </TouchableOpacity>
              </View>
            </View>
            <View>

            </View>

            <View style={styles.activityBar}><Text variant="semiBold">Activity</Text></View>
          </View>
        </ScrollView>
      </View>
    </>
  )
}

export default FollowersDetailsScreen

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: "#fff",
  },
  activityBar:{
width:'100%',backgroundColor:'#fff',
marginTop:moderateScale(8),
paddingVertical:moderateScale(16),
paddingHorizontal:moderateScale(12),
borderRadius:moderateScale(5)
  },
  headerBackground: {
    marginTop:moderateScale(2),
    height: moderateScale(160),
    backgroundColor: '#e0e6ed',
    position: 'relative',
    borderTopRightRadius:moderateScale(12),borderTopLeftRadius:moderateScale(6)
  },
  headerImage: {
    width: '100%',
    height: '100%',
    opacity: 0.3,
  },
  profileImageWrapper: {
    position: 'absolute',
    top: moderateScale(105),
    left: moderateScale(24),
    zIndex: 2,
  },
  profileImageContainer: {
    width: moderateScale(80),
    height: moderateScale(80),
    borderRadius: moderateScale(40),
    backgroundColor: '#fff',
    padding: moderateScale(3),
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  profileImage: {
    width: '100%',
    height: '100%',
    borderRadius: moderateScale(37),
  },
  userInfoCard: {
    backgroundColor: '#fff',
    // marginTop: moderateScale(50),
    // marginHorizontal: moderateScale(16),
    // borderRadius: moderateScale(8),
    padding: moderateScale(16),
    paddingTop: moderateScale(30),
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  userName: {
    fontSize: moderateScale(22),
    color: "#333",
    marginBottom: moderateScale(16),
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: moderateScale(8),
    marginBottom: moderateScale(12),
  },
  infoText: {
    fontSize: moderateScale(14),
    color: "#666",
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: moderateScale(12),
    marginTop: moderateScale(20),
  },
  followButton: {
    backgroundColor: '#4285F4',
    borderRadius: moderateScale(6),
    paddingVertical: moderateScale(10),
    paddingHorizontal: moderateScale(20),
    alignItems: 'center',
    width:moderateScale(150)
  },
  followButtonText: {
    color: '#fff',
    fontWeight: '500',
    fontSize: moderateScale(14),
  },
  connectButton: {
    backgroundColor: '#fff',
    borderRadius: moderateScale(6),
    paddingVertical: moderateScale(10),
    paddingHorizontal: moderateScale(20),
    borderColor: '#ddd',
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: moderateScale(6),
    width:moderateScale(150)
    

  },
  connectButtonText: {
    color: '#4285F4',
    fontWeight: '500',
    fontSize: moderateScale(14),
  },
  
})