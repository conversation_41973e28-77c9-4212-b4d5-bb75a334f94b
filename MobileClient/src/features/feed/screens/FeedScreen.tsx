import React, {useState, useCallback, useEffect, useRef} from 'react';
import {useLazyQuery, useMutation, useSubscription} from '@apollo/client';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import {ActivityIndicator, Text, useTheme} from 'react-native-paper';
import {useNavigation, NavigationProp} from '@react-navigation/native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInDown,
  SlideOutDown,
  useAnimatedScrollHandler,
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import {RootStackParamList} from '../../../Common/Routes/StackTypes';
import {useFeed, useFeedSubscriptionData} from '../hooks/useFeed';
import {feedFormatData} from '../utils/dataFormatter';
import {moderateScale} from '../../../Utils/responsiveUtils';
import {GET_COMMENTS} from '../graphql/queries';
import {ADD_COMMENT} from '../graphql/mutations';
import {ON_CREATE_COMMENT} from '../graphql/subscriptions';

import FeedHeader from '../components/common/FeedHeader';
import FeedList from '../components/feed-item/FeedList';
import LinearProgressBar from '../components/common/LinearProgressBar';
import EmptyState from '../components/common/EmptyState';
import CommentModal from '../components/comments/CommentModal';
import AppHeader from '../../../Components/AppHeader';
import { Plus, PlusCircle } from 'lucide-react-native';

const AnimatedView = Animated.createAnimatedComponent(View);

const FeedScreen = () => {
  const theme = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const {top, bottom} = useSafeAreaInsets();

  // Animation values
  const scrollY = useSharedValue(0);

  // State
  const [posts, setPosts] = useState<any[]>([]);
  const [searchText, setSearchText] = useState('');
  const [progress, setProgress] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Comment modal state
  const [showCommentModal, setShowCommentModal] = useState(false);
  const [currentPostId, setCurrentPostId] = useState('');
  const [comments, setComments] = useState<any[]>([]);
  const [commentsLoading, setCommentsLoading] = useState(false);

  // GraphQL queries
  const [fetchComments, {loading: fetchCommentsLoading}] = useLazyQuery(
    GET_COMMENTS,
    {
      fetchPolicy: 'network-only', // Always fetch from network to get latest data
      onCompleted: data => {
        console.log('Fetched comments:', data?.getComments?.items?.length || 0);
        // Sort comments by created_at in descending order (newest first)
        const sortedComments = [...(data?.getComments?.items || [])].sort(
          (a, b) => {
            try {
              return (
                new Date(b.created_at).getTime() -
                new Date(a.created_at).getTime()
              );
            } catch (error) {
              return 0;
            }
          },
        );
        setComments(sortedComments);
        setCommentsLoading(false);
      },
      onError: error => {
        console.log('Error fetching comments:', error);
        setCommentsLoading(false);
      },
    },
  );

  const [addComment, {loading: addCommentLoading}] = useMutation(ADD_COMMENT);

  // Comment subscription
  const {data: newCommentData} = useSubscription(ON_CREATE_COMMENT, {
    variables: {post_id: currentPostId},
    skip: !currentPostId || !showCommentModal,
    fetchPolicy: 'network-only', // Always fetch from network to get latest data
  });

  // Data fetching
  const {feedData, loading, refetchPosts} = useFeed();
  const {subscriptionFeedData} = useFeedSubscriptionData();

  // Effects
  useEffect(() => {
    if (subscriptionFeedData?.onLambdaPost?.user_ids) {
      refetchPosts();
    }
  }, [subscriptionFeedData, refetchPosts]);

  useEffect(() => {
    if (Array.isArray(feedData) && feedData?.length > 0) {
      setPosts(feedFormatData(feedData));
    }
  }, [feedData]);

  // Handle new comment from subscription
  useEffect(() => {
    try {
      if (newCommentData?.onCreateComment && currentPostId) {
        const newComment = newCommentData.onCreateComment;

        // Validate the new comment
        if (!newComment || typeof newComment !== 'object' || !newComment.id) {
          console.log('Invalid new comment from subscription:', newComment);
          return;
        }

        console.log('New comment from subscription:', newComment);

        // Check if comment already exists
        const exists = comments.some(c => c.id === newComment.id);
        if (!exists) {
          // Add uniqueId for stable rendering
          const commentWithId = {
            ...newComment,
            uniqueId:
              newComment.id ||
              `comment-${Date.now()}-${Math.random()
                .toString(36)
                .substring(2, 9)}`,
          };

          // Add the new comment to the list while preserving existing comments
          if (newComment.parent_comment_id !== 'root') {
            return;
          }
          setComments((prev) => [commentWithId, ...prev]);
          // setComments(prev => [commentWithId, ...prev]);

          // Update the comment count for the post
          if (newComment.post_id && newComment.comments_count) {
            // Find the post and update its comment count
            setPosts(prevPosts =>
              prevPosts.map(post =>
                post.post_id === newComment.post_id
                  ? {
                      ...post,
                      postData: {
                        ...post.postData,
                        comments_count: newComment.comments_count,
                      },
                    }
                  : post,
              ),
            );
          }
        }
      }
    } catch (error) {
      console.log('Error handling comment subscription:', error);
    }
  }, [newCommentData, currentPostId]);

  // Handlers
  const handleSearchChange = useCallback(
    (newText: string) => setSearchText(newText),
    [],
  );

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    refetchPosts().finally(() => {
      setRefreshing(false);
    });
  }, [refetchPosts]);

  // Comment modal handlers
  const handleOpenCommentModal = useCallback(
    (postId?: string) => {
      if (!postId) {
        console.log('Cannot open comment modal: postId is missing');
        return;
      }

      console.log('Opening comment modal for post:', postId);

      // If we're opening for a different post, clear previous comments
      if (currentPostId !== postId) {
        setComments([]);
      }

      setCurrentPostId(postId);
      setCommentsLoading(true);
      setShowCommentModal(true);

      // Always fetch fresh comments when opening the modal
      fetchComments({
        variables: {
          post_id: postId,
          root_parent_id: 'root',
        },
      });
    },
    [fetchComments, currentPostId],
  );

  const handleCloseCommentModal = useCallback(() => {
    setShowCommentModal(false);
    // Don't clear comments when closing the modal
    // This way they'll still be there if the user reopens the modal
  }, []);

  const handleSubmitComment = useCallback(
    (commentData: any) => {
      if (!commentData?.comment?.trim() || !currentPostId) return;

      const post = posts.find(p => p.post_id === currentPostId);
      if (!post) return;

      const userId = post.user_id; // Get the user ID from the post

      // Create a temporary comment to show immediately
      const tempId = `temp-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 9)}`;
      const tempComment: any = {
        id: tempId,
        uniqueId: tempId,
        post_id: currentPostId,
        content: commentData.comment,
        created_at: new Date().toISOString(),
        parent_comment_id: commentData.parentCommentId || 'root',
        root_parent_id: commentData.rootParentId || 'root',
        comment_by: {
          user_id: userId,
          user_name: 'You',
          role: 'User',
          profile_pic: 'https://via.placeholder.com/36',
        },
        is_replies: false,
      };

      // Add the temporary comment to the state immediately

      addComment({
        variables: {
          post_id: currentPostId,
          post_created_date: post.created_at,
          content: commentData.comment,
          parent_comment_id: commentData.parentCommentId || 'root',
          user_id: userId,
          root_parent_id: commentData.rootParentId || 'root',
          reply_to_user_id: commentData.replyingToUserId || null,
        },
      })
        .then(response => {
          console.log('Comment added successfully:', response?.data);

          // If the comment was added successfully, update the temporary comment
          if (response?.data?.addComment) {
            const newComment = response.data.addComment;

            // Replace the temporary comment with the real one
            // setComments(prev =>
            //   prev.map(comment =>
            //     comment.id === tempId
            //       ? {
            //           // ...newComment,
            //           uniqueId:
            //             newComment.id ||
            //             `comment-${Date.now()}-${Math.random()
            //               .toString(36)
            //               .substring(2, 9)}`,
            //         }
            //       : 
            //       comment,
            //   ),
            // );

            // Update the comment count for the post
            // if (newComment.comments_count) {
            //   setPosts(prevPosts =>
            //     prevPosts.map(p =>
            //       p.post_id === currentPostId
            //         ? {
            //             ...p,
            //             postData: {
            //               ...p.postData,
            //               comments_count: newComment.comments_count,
            //             },
            //           }
            //         : p,
            //     ),
            //   );
            // }
          }
        })
        .catch(error => {
          console.log('Error adding comment:', error);
          // Remove the temporary comment on error
          setComments(prev => prev.filter(comment => comment.id !== tempId));
        });
    },
    [currentPostId, posts, addComment],
  );

const handleAddConnection=()=>{
  navigation.navigate('AddConnectionScreen')
}













  const handleScroll = useAnimatedScrollHandler({
    onScroll: event => {
      scrollY.value = event.contentOffset.y;
    },
  });

  // Define global functions that can be accessed from anywhere
  React.useEffect(() => {
    console.log('Setting up global feed functions');

    // Add these functions to the global window object
    // This is a workaround for the navigation parameter passing issue
    (global as any).feedSetUploading = (value: boolean) => {
      console.log('Global setUploading called with:', value);
      setUploading(value);
    };

    (global as any).feedSetProgress = (value: number) => {
      console.log('Global setProgress called with:', value);
      setProgress(value);
    };

    // Verify the functions are set correctly
    console.log('Global feed functions set up successfully:', {
      setUploadingExists:
        typeof (global as any).feedSetUploading === 'function',
      setProgressExists: typeof (global as any).feedSetProgress === 'function',
    });

    return () => {
      console.log('Cleaning up global feed functions');
      // Clean up when component unmounts
      delete (global as any).feedSetUploading;
      delete (global as any).feedSetProgress;
    };
  }, []);

  const handlePlusPress = useCallback(() => {
    navigation.navigate('FeedStack', {
      screen: 'CreatePostScreen',
      params: {
        // Just pass the current values, not the functions
        uploading,
        progress,
      },
    });
  }, [navigation, uploading, progress]);

  // Animated styles
  const headerAnimatedStyle = useAnimatedStyle(() => {
    const elevation = interpolate(
      scrollY.value,
      [0, 50],
      [0, 4],
      Extrapolate.CLAMP,
    );

    return {
      elevation,
      shadowOpacity: interpolate(
        scrollY.value,
        [0, 50],
        [0, 0.15],
        Extrapolate.CLAMP,
      ),
      shadowOffset: {
        width: 0,
        height: interpolate(scrollY.value, [0, 50], [0, 3], Extrapolate.CLAMP),
      },
      zIndex: 10,
    };
  });

  // Render content based on loading state and data availability
  const renderContent = () => {
    if (loading && !refreshing) {
      return (
        <Animated.View
          style={styles.centerContainer}
          entering={FadeIn.duration(300)}
          exiting={FadeOut.duration(300)}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
        </Animated.View>
      );
    }

    if (posts?.length > 0) {
      return (
        <Animated.View style={styles.content} entering={FadeIn.duration(300)}>
          <FeedList
            posts={posts}
            onCommentPress={handleOpenCommentModal}
            onScroll={handleScroll}
            refreshing={refreshing}
            onRefresh={handleRefresh}
          />
        </Animated.View>
      );
    }

    return (
      <Animated.View
        style={styles.centerContainer}
        entering={FadeIn.duration(300).delay(100)}>
        <EmptyState
          title="No Posts Available"
          description="Be the first to create a post!"
          icon="post-outline"
          buttonText="Create Post"
          onButtonPress={handlePlusPress}
        />
      </Animated.View>
    );
  };

  return (
    <>
    <AppHeader title='My Feed' hideBackAction rightElement={ 
      <View style={{flexDirection:'row',gap:moderateScale(12),justifyContent:'center',alignItems:'center'}}>
        <TouchableOpacity
        style={styles.headerButtons}
        onPress={()=>handleAddConnection()}
        ><Text style={{color:'#fff',paddingVertical:moderateScale(1),paddingHorizontal:moderateScale(6),fontSize:moderateScale(12)}}>Add Connection</Text></TouchableOpacity>

      <TouchableOpacity
                    style={styles.headerButtons}
                    onPress={() => handlePlusPress()}
                  >
                    <Plus size={moderateScale(20)} color="#FFF" strokeWidth={2.5} />
                  </TouchableOpacity>
      </View>    
                }
                  />
      <View
        style={[
          styles.container,
          {
            backgroundColor: theme.colors.background,
            // paddingTop: top,
            // paddingBottom: bottom,
          },
        ]}>
        {/* <AnimatedView style={[styles.headerContainer, headerAnimatedStyle]}> */}
          {/* <FeedHeader
            searchText={searchText}
            onSearchChange={handleSearchChange}
            onPlusPress={handlePlusPress}
          /> */}
        {/* </AnimatedView> */}

        {uploading && (
          <Animated.View
            entering={SlideInDown.springify().damping(15)}
            exiting={SlideOutDown.springify().damping(15)}>
            <LinearProgressBar uploading={uploading} progress={progress} />
          </Animated.View>
        )}

        {renderContent()}

        {/* Comment Modal */}
      </View>
      {showCommentModal && <CommentModal
        visible={showCommentModal}
        onClose={handleCloseCommentModal}
        onSubmitComment={handleSubmitComment}
        postId={currentPostId}
        comments={comments}
        loading={commentsLoading}
      />}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerButtons:{
    backgroundColor: '#3377FF',
                      padding: moderateScale(5),
    
                      borderRadius: 5,
                     
                      borderColor: '#3377FF',
  },
  headerContainer: {
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowRadius: 4,
    marginBottom: moderateScale(12),
  },
  content: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: moderateScale(20),
  },
});

export default FeedScreen;
