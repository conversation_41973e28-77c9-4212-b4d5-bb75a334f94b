import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Appbar, Text, ActivityIndicator, useTheme } from 'react-native-paper';
import { useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { moderateScale } from '../../../Utils/responsiveUtils';

import { useProfile } from '../hooks/useProfile';
import { useCreatePost } from '../hooks/useCreatePost';
import CreatePostForm from '../components/create-post/CreatePostForm';
import PostPreview from '../components/create-post/PostPreview';
import ImagePreviewModal from '../components/create-post/ImagePreviewModal';
import CustomDialog from '../components/common/CustomDialog';
import AppHeader from '../../../Components/AppHeader';

interface CreatePostScreenProps {}

const CreatePostScreen: React.FC<CreatePostScreenProps> = () => {
  const theme = useTheme();
  const navigation = useNavigation();
  const {top, bottom} = useSafeAreaInsets();
  const userId = useSelector((state: any) => state.userId);

  // Check if global functions are available when component mounts
  useEffect(() => {
    console.log('CreatePostScreen mounted, checking global functions:', {
      feedSetUploadingExists: typeof (global as any).feedSetUploading === 'function',
      feedSetProgressExists: typeof (global as any).feedSetProgress === 'function'
    });
  }, []);


  const setUploading = (value: boolean) => {
    console.log('CreatePostScreen setUploading called with:', value);
    if (typeof (global as any).feedSetUploading === 'function') {
      console.log('CreatePostScreen using global feedSetUploading');
      (global as any).feedSetUploading(value);
    } else {
      console.log('Global feedSetUploading not available');
    }
  };

  const setProgress = (value: number) => {
    console.log('CreatePostScreen setProgress called with:', value);
    if (typeof (global as any).feedSetProgress === 'function') {
      console.log('CreatePostScreen using global feedSetProgress');
      (global as any).feedSetProgress(value);
    } else {
      console.log('Global feedSetProgress not available');
    }
  };

  const [content, setContent] = useState('');
  const [rawImages, setRawImages] = useState<any[]>([]);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [isImagePreviewVisible, setIsImagePreviewVisible] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const canPost = content.trim().length > 0 || rawImages.length > 0;

  const { profile, loading: profileLoading, error: profileError } = useProfile(userId);

  const {
    createPostHandler,
    loading: postLoading
  } = useCreatePost({
    userId,
    setUploading,
    setProgress,
    setContent,
    content,
    setRawImages,
    rawImages,
    canPost,
  });

  const handleBack = () => {
    if (canPost) {
      setDialogVisible(true);
    } else {
      navigation.goBack();
    }
  };

  const handlePost = () => {
    if (canPost) {
      createPostHandler();
      navigation.goBack();
    }
  };

  const handleImagePreview = (index: number) => {
    setSelectedImageIndex(index);
    setIsImagePreviewVisible(true);
  };


  if (profileLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  if (profileError) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: theme.colors.background }]}>
        <Text variant="bodyLarge" style={{ color: theme.colors.error }}>
          Error loading profile
        </Text>
      </View>
    );
  }

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: theme.colors.background,
          // paddingTop: insets.top,
        },
      ]}
    >
      {/* <Appbar.Header style={styles.header} mode='small' >
        <Appbar.BackAction onPress={handleBack} />
        <Appbar.Content title="Create Post" />
        <View style={styles.headerActions}>
          {canPost && (
            <TouchableOpacity
              style={[
                styles.previewButton,
                { backgroundColor: theme.colors.surfaceVariant }
              ]}
              onPress={() => setIsPreviewMode(!isPreviewMode)}
            >
              <Text
                variant="labelLarge"
                style={{ color: theme.colors.onSurfaceVariant }}
              >
                {isPreviewMode ? 'Edit' : 'Preview'}
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              styles.postButton,
              {
                backgroundColor: canPost
                  ? theme.colors.primary
                  : theme.colors.surfaceDisabled
              }
            ]}
            onPress={handlePost}
            disabled={!canPost || postLoading}
          >
            {postLoading ? (
              <ActivityIndicator size="small" color={theme.colors.onPrimary} />
            ) : (
              <Text
                variant="labelLarge"
                style={{
                  color: canPost
                    ? theme.colors.onPrimary
                    : theme.colors.onSurfaceDisabled
                }}
              >
                Post
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </Appbar.Header> */}
      <AppHeader
        title="Create Post"
        rightElement={
          <View style={{ flexDirection: 'row', gap: 10 }}>
            {canPost && (
              <TouchableOpacity
                style={[styles.previewButton, { backgroundColor: theme.colors.surfaceVariant }]}
                onPress={() => setIsPreviewMode(!isPreviewMode)}
              >
                <Text
                  variant="semiBold"
                  style={{ color: theme.colors.onSurfaceVariant, fontSize: moderateScale(14) }}
                >
                  {isPreviewMode ? 'Edit' : 'Preview'}
                </Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={[
                styles.postButton,
                {
                  backgroundColor: canPost ? theme.colors.primary : theme.colors.surfaceDisabled,
                },
              ]}
              onPress={handlePost}
              disabled={!canPost || postLoading}
            >
              {postLoading ? (
                <ActivityIndicator size="small" color={theme.colors.onPrimary} />
              ) : (
                <Text
                  variant="semiBold"
                  style={{
                    color: canPost ? theme.colors.onPrimary : theme.colors.onSurfaceDisabled,
                    fontSize: moderateScale(14),
                  }}
                >
                  Post
                </Text>
              )}
            </TouchableOpacity>
          </View>
        }
      />
      <View style={[!isPreviewMode?styles.content:{flex:1}]}>
        {isPreviewMode ? (
          <PostPreview
            content={content}
            images={rawImages.map((img) => img.uri)}
            profile={profile}
          />
        ) : (
          <CreatePostForm
            content={content}
            setContent={setContent}
            rawImages={rawImages}
            setRawImages={setRawImages}
            profile={profile}
            onImagePreview={handleImagePreview}
          />
        )}
      </View>

      {/* Dialogs and Modals */}
      <CustomDialog
        visible={dialogVisible}
        onDismiss={() => setDialogVisible(false)}
        onConfirm={() => {
          setDialogVisible(false);
          navigation.goBack();
        }}
        title="Discard Post?"
        message="Are you sure you want to discard this post? Your draft will not be saved."
        confirmText="Discard"
        cancelText="Keep Editing"
        confirmColor={theme.colors.error}
      />

      {isImagePreviewVisible && (
        <ImagePreviewModal
          images={rawImages}
          initialIndex={selectedImageIndex}
          onClose={() => setIsImagePreviewVisible(false)}
          onConfirm={(updatedImages) => {
            setRawImages(updatedImages);
            setIsImagePreviewVisible(false);
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: moderateScale(16),
  },
  header: {
    elevation: 0,
    backgroundColor: 'transparent',
  },
  headerActions: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: moderateScale(8),
    marginRight: moderateScale(8),
  },
  previewButton: {
    borderRadius: moderateScale(5),
    marginVertical: 0,
    paddingHorizontal : 7,
    paddingVertical : 4,
  },
  postButton: {
    borderRadius: moderateScale(5),
    paddingHorizontal : 7,
    paddingVertical : 4,
    marginVertical: 0,
  },
  content: {
    flex: 1,
    paddingHorizontal: moderateScale(8),
  },
});

export default CreatePostScreen;
