import { ActivityIndicator, Text } from "react-native-paper";
import AppHeader from "../../../Components/AppHeader";
import { <PERSON><PERSON>, FlatList, Image, StyleSheet, TouchableOpacity, View } from "react-native";
import { moderateScale } from "react-native-size-matters";
import { Check } from "lucide-react-native";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { useMutation, useQuery } from "@apollo/client";
import { gql } from "@apollo/client";
import { useCallback, useEffect, useState } from "react";
import { ADD_FOLLOWER, GET_ALL_ACCOUNT_USERS, UNFOLLOW_USER } from "./FeedUsersQuery";
import { getAccountId, getUserId } from "../../../Common/Utils/Storage";

interface UserDetails {
  email_id?: string;
  is_following?: boolean;
  is_online?: string;
  profile_pic?: string | null;
  user_id?: string;
  user_name?: string;
  role?: string | null;
  __typename?: string;
}

interface AccountUser {
  account_id?: string;
  user_details: UserDetails;
  user_id?: string;
  __typename?: string;
}

const AddConnectionScreen: React.FC = () => {
  const navigation = useNavigation();
  const [loaderUserId, setLoaderUserId] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [accountId, setAccountId] = useState<string | null>(null);
  const [initialLoader,setInitialLoader]=useState<boolean | null>(true)

  useEffect(() => {
    (async () => {
      try {
        const id = await getUserId();
        const acc_id = await getAccountId();
        console.log(id, acc_id, "userid");
        setUserId(id);
        setAccountId(acc_id);
        setInitialLoader(false)
      } catch (error) {
        console.log("Error fetching user ID:", error);
      }
    })();
  }, []);

  const { data: allAccountUsers, loading, error, refetch } = useQuery<{
    GetAccountUsers: AccountUser[];
  }>(GET_ALL_ACCOUNT_USERS, {
    variables: { account_id: accountId, user_id: userId },
    skip: !accountId || !userId,
    fetchPolicy: "cache-and-network",
    notifyOnNetworkStatusChange: true,
  });
  console.log(initialLoader,'dsfgs',loading)

  useFocusEffect(
    useCallback(() => {
      if (accountId && userId) {
        refetch();
      }
    }, [refetch, accountId, userId])
  );

  const [handleFollowFunc, { error: followError }] = useMutation(ADD_FOLLOWER, {
    optimisticResponse: (variables) => ({
      addFollower: {
        __typename: "Followers",
        follow_at: new Date().toISOString(),
        follower_id: variables.input.follower_id,
        user_id: variables.input.user_id,
      },
    }),
    update: (cache, { data }) => {
      const cacheData = cache.readQuery<{ GetAccountUsers: AccountUser[] }>({
        query: GET_ALL_ACCOUNT_USERS,
        variables: { account_id: accountId, user_id: userId },
      });

      const followedUserId = data?.addFollower?.user_id;

      if (cacheData && followedUserId) {
        const updatedUsers = cacheData.GetAccountUsers.map((accountUser) => {
          if (accountUser.user_details.user_id === followedUserId) {
            return {
              ...accountUser,
              user_details: {
                ...accountUser.user_details,
                is_following: true,
              },
            };
          }
          return accountUser;
        });

        cache.writeQuery({
          query: GET_ALL_ACCOUNT_USERS,
          variables: { account_id: accountId, user_id: userId },
          data: { GetAccountUsers: updatedUsers },
        });
      }
    },
  });

 const [handleUnFollowFunc,{error:UnfollowError}] = useMutation(UNFOLLOW_USER);

  const allFetchedUsers = allAccountUsers?.GetAccountUsers?.map(
    (accountUser) => accountUser.user_details
  ).filter((user): user is UserDetails => user !== null && user !== undefined) || [];

  const handleFollow = async (id_to_follow: string) => {
    setLoaderUserId(id_to_follow);
    const followInput = {
      follower_id: userId,
      user_id: id_to_follow,
    };

    try {
      await handleFollowFunc({
        variables: { input: followInput },
      });
    } catch (error) {
      console.error("Error adding follower:", error);
      Alert.alert("Error", "Failed to follow user. Please try again.");
    } finally {
      setLoaderUserId(null);
    }
  };

  const handleUnFollow = async (id_to_unFollow: string) => {
    setLoaderUserId(id_to_unFollow);
    const followInput = {
      follower_id: userId,
      user_id: id_to_unFollow,
    };

    try {
      await handleUnFollowFunc({
        variables: { input: followInput },
        update: (cache, { data }) => {
          if (data?.unFollow?.success) {
            const cacheData = cache.readQuery<{ GetAccountUsers: AccountUser[] }>({
              query: GET_ALL_ACCOUNT_USERS,
              variables: { account_id: accountId, user_id: userId },
            });

            if (cacheData) {
              const updatedUsers = cacheData.GetAccountUsers.map((accountUser) => {
                if (accountUser.user_details.user_id === id_to_unFollow) {
                  return {
                    ...accountUser,
                    user_details: {
                      ...accountUser.user_details,
                      is_following: false,
                    },
                  };
                }
                return accountUser;
              });

              cache.writeQuery({
                query: GET_ALL_ACCOUNT_USERS,
                variables: { account_id: accountId, user_id: userId },
                data: { GetAccountUsers: updatedUsers },
              });
            }
          }
        },
      });
    } catch (error) {
      console.error("Error removing follower:", error);
      Alert.alert("Error", "Failed to unfollow user. Please try again.");
    } finally {
      setLoaderUserId(null);
    }
  };

  const handleNavigationToUserDetails = (user: UserDetails) => {
    // navigation.navigate("FollowersDetailsScreen", { user });
    console.log('redirected to conncection paged',user)
  };

  const UserConnectionCard = ({ item }: { item: UserDetails }) => (
    <TouchableOpacity
      style={styles.container}
      activeOpacity={0.7}
      onPress={() => handleNavigationToUserDetails(item)}
    >
      <View style={styles.connectionContainer}>
        <View style={styles.userInfoContainer}>
          <View style={styles.imageWrapper}>
            <Image
              source={
                item.profile_pic
                  ? { uri: item.profile_pic }
                  : require("../../../../assets/ChatImages/User1.png")
              }
              style={styles.userImage}
            />
          </View>
          <Text variant="labelLarge" style={styles.userName}>
            {item.user_name
              ? item.user_name.length > 20
                ? item.user_name.substring(0, 18) + "..."
                : item.user_name
              : "Unknown User"}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.followButton}
          onPress={() => {
            if (item.is_following) {
              handleUnFollow(item.user_id!);
            } else {
              handleFollow(item.user_id!);
            }
          }}
          disabled={loaderUserId === item.user_id}
        >
          {loaderUserId === item.user_id ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              {item.is_following && <Check size={16} color="#fff" />}
              <Text style={styles.followButtonText}>
                {item.is_following ? "Following" : "Follow"}
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  if (loading || initialLoader) {
    return (
      <View style={styles.loadingIndicator}>
        <ActivityIndicator size='large' color="#3377EE" />
      </View>
    );
  }

  if (error) return <Text>Error: {error.message}</Text>;
  if (followError) return <Text>Error: {followError.message}</Text>;
  if(UnfollowError) return <Text> Error: {UnfollowError.message}</Text>

  return (
    <View style={styles.listContainer}>
      <AppHeader title="Add Connections" />
      <FlatList
        data={allFetchedUsers} 
        keyExtractor={(item) => item.user_id?.toString() || Math.random().toString()}
        renderItem={({ item }) => <UserConnectionCard item={item} />}
        ListEmptyComponent={
          <View  style={styles.centerAlign}>

            <Text  variant='medium' style={styles.noUserFoundText}>No users found</Text>
        </View>
        }
        contentContainerStyle={allFetchedUsers.length===0&&styles.centerAlign}
        showsVerticalScrollIndicator={false}

      />
    </View>
  );
};

export default AddConnectionScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  listContainer: {
    flex: 1,
  },
  
  noUserFoundText: {
    fontSize: moderateScale(14),
    color: "#333",
    textAlign:'center'

  },
 
  loadingIndicator: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  connectionContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    margin: moderateScale(2),
    borderRadius: moderateScale(12),
    borderColor: "gray",
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(12),
  },
  centerAlign:{
    flex:1,justifyContent:'center',alignItems:'center'
  },
  userInfoContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: moderateScale(16),
  },
  imageWrapper: {
    position: "relative",
  },
  userImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  activeIndicator: {
    position: "absolute",
    bottom: 2,
    right: 1,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: "green",
    borderWidth: 2,
    borderColor: "#fff",
  },
  userName: {
    fontSize: moderateScale(12),
  },
  followButton: {
    backgroundColor: "#3377ee",
    borderRadius: moderateScale(8),
    width: "30%",
    maxHeight: moderateScale(35),
    height: moderateScale(32),
    paddingVertical: moderateScale(8),
    paddingHorizontal: moderateScale(6),
    flexDirection: "row",
    justifyContent: "center",
    gap: moderateScale(8),
  },
  followButtonText: {
    color: "#fff",
    fontSize: moderateScale(11),
  },
});