import { gql } from '@apollo/client';

export const GET_ALL_ACCOUNT_USERS = gql`
query GetAllAccountUsers($account_id: ID!, $user_id: ID!) {
  GetAccountUsers(account_id: $account_id, user_id: $user_id) {
    account_id
    user_details {
      email_id
      is_following
      is_online
      profile_pic
      user_id
      user_name
      role
    }
    user_id
  }
}
`;


export const ADD_FOLLOWER = gql`
mutation addFollower($input: followerInput!) {
    addFollower(input:$input) {
      follow_at
      follower_id
      user_id
    }
  }

`


export const UNFOLLOW_USER = gql`

mutation unFollow($input:unfollowInput!) {
  unFollow(input:$input) {
    message
    success
  }
}
`
