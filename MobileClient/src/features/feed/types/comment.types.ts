export interface User {
  email_id: string;
  follower_count: number | null;
  is_following: boolean | null;
  is_online: boolean | string;
  profile_pic: string;
  role: string;
  user_name: string;
  user_id: string;
}

export interface Comment {
  content: string;
  comment_by: User;
  root_parent_id: string;
  reply_to_user_details?: User;
  created_at: string;
  id: string;
  is_replies: boolean;
  parent_comment_id: string;
  post_id: string;
  uniqueId?: string; // Used for rendering optimization
}

export interface CommentHeaderProps {
  onClose: () => void;
}

export interface CommentInputProps {
  parentCommentId: string;
  rootParentId: string;
  replyingTo: {
    userName: string;
    userId: string;
  };
  replyCommentId: string;
  onSubmit: (comment: any) => void;
  eraseReplyComment: () => void;
}

export interface CommentItemProps {
  comment: Comment;
  onReply: (
    parentCommentId: string,
    rootParentId: string,
    replyingToUserId: string,
    replyingToUserName: string,
    repliedCommentId: string
  ) => void;
  repliedComments: Comment[];
  showingReplies: boolean;
  loading: boolean;
  collapseReplyHandler: () => void;
}

export interface CommentItemWrapperProps {
  item: Comment;
  index: number;
  isNewestComment: boolean;
  replyTo: (
    parentCommentId: string,
    rootParentId: string,
    replyingToUserId: string,
    replyingToUserName: string,
    repliedCommentId: string
  ) => void;
}

export interface CommentModalProps {
  visible: boolean;
  onClose: () => void;
  comments: Comment[];
  onSubmitComment: (comment: any) => void;
  loading: boolean;
  postId: string;
}
