export interface Attachment {
  extension: string;
  size: number;
  mime_type: string;
  name: string;
  url: string;
}

export interface Posts {
  created_at: string;
  post_id: string;
  sender_id: string;
  user_id: string;
  sender_details: {
    user_id: string;
    user_name: string;
    email_id: string;
    is_online: boolean;
    profile_pic: string;
    role: string;
  };
  postData: {
    attachment: Attachment[];
    attachmentsList: string[];
    payload: string;
    likes_count: number;
    is_current_user_like_the_post: boolean;
    comments_count: number;
  };
}

export type FeedItemProps = {
  item: Posts;
  onCommentPress?: () => void;
  comments?: any[];
  commentsLoading?: boolean;
  handleSubmitComment?: (commentData: any) => Promise<void>;
};

export interface FeedHeaderProps {
  searchText: string;
  onSearchChange: (text: string) => void;
  onPlusPress?: () => void;
}

export interface FeedListProps {
  posts: Posts[];
  onCommentPress?: (postId?: string) => void;
  onScroll: any;
  refreshing?: boolean;
  onRefresh?: () => void;
}
