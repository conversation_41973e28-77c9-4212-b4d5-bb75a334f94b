import { StyleSheet, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { Appbar, Menu, Divider, Avatar, Text, Button } from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { Building2, ChevronDown, Thermometer, User, User2, UserRound } from 'lucide-react-native';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import FastImage from 'react-native-fast-image';
// Define the menu item interface
interface MenuItem {
  title: string;
  onPress: () => void;
  icon?: string;
}

// Define unit option interface
interface UnitOption {
  id: string;
  label: string;
  value: string;
  description: string;
}

// Define the props type for the component
interface AppHeaderProps {
  menuVisible?: boolean;
  setMenuVisible?: (visible: boolean) => void;
  title?: string;
  menuItems?: MenuItem[];
  showControlUnit?: boolean;
  showPeopleHirerachy?: boolean;
  onPeopleHirerachyPress?: () => void;
  showMenu?: boolean;
  onControlUnitsPress?: () => void;
  handleUsers?: () => void;
  selectedControlUnit?: any;
  selectedPeopleHirerachy?: any;
  selectedUser?: any;
  showLocationInfo?: any;
  showUnitSelector?: boolean;
  selectedUnit?: string;
  onUnitChange?: (unit: string, unitId: string) => void;
  handleCalendarPress?: () => void;
  showCalendarButton?: boolean;
  selectedDate?: Date;
  rightElement?: any;
  customHeaderStyles?: any;
  hideBackAction?:any
  showDrawer?:any
  isLogoVisible?:boolean
}

const AppHeader: React.FC<AppHeaderProps> = ({
  menuVisible = false,
  setMenuVisible = () => {},
  title = 'Leads',
  menuItems = [],
  showMenu = false,
  onControlUnitsPress = () => {},
  onPeopleHirerachyPress = () => {},
  selectedControlUnit,
  selectedPeopleHirerachy,
  showControlUnit = false,
  showPeopleHirerachy = false,
  handleCalendarPress = () => {},
  showCalendarButton = false,
  selectedDate = new Date(),
  rightElement,
  customHeaderStyles = {},
  showUnitSelector = false,
  selectedUnit = 'SI',
  onUnitChange = () => {},
  hideBackAction,
  showDrawer=false,
  isLogoVisible=false
}) => {
  const theme = useTheme();
  const navigation = useNavigation();

  // State for unit menu
  const [unitMenuVisible, setUnitMenuVisible] = React.useState(false);

  // Unit options (moved from UnitSelector component)
  const unitOptions: UnitOption[] = [
    {
      id: '1',
      label: 'SI (Metric)',
      value: 'SI',
      description: 'kg, cm, m³, °C',
    },
    {
      id: '2',
      label: 'US (Imperial)',
      value: 'Imperial',
      description: 'lb, in, ft³, °F',
    },
  ];

  // Get current selected unit for display
  const currentUnit = unitOptions.find((unit) => unit.value === selectedUnit);

  return (
    <View
    // style={{ marginBottom: verticalScale(10) }}
    >
      <Appbar.Header mode="small" style={[styles.header, customHeaderStyles]}>
        {!hideBackAction && !showDrawer && (
          <Appbar.BackAction
            size={moderateScale(20)}
            color={theme.colors.onSurface}
            onPress={() => navigation.goBack()}
            // style={styles.backButton}
          />
        )}
        {showDrawer && (
          <Appbar.Action
            icon="menu"
            size={moderateScale(25)}
            color={theme.colors.onSurface}
            onPress={() => navigation.openDrawer()}
            style={styles.drawerButton}
          />
        )}
        <Appbar.Content
          style={{ backgroundColor: '#fff' }}
          title={
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              {isLogoVisible && (
                <FastImage
                  source={require('../../../assets/icons/headerLogo.png')} // update path to your logo
                  style={{ width: moderateScale(130), height: moderateScale(50), marginRight: 8 }}
                  resizeMode="contain"
                />
              )}
              {!isLogoVisible && <Text style={styles.title}>{title}</Text>}
            </View>
          }
          titleStyle={styles.title}
        />

        {(selectedControlUnit?.controlUnitCode || selectedControlUnit?.controlUnitName) &&
          showControlUnit && (
            <TouchableOpacity
              style={styles.locationContainer}
              onPress={() => {
                onControlUnitsPress();
              }}
            >
              <View style={styles.locationIconContainer}>
                <Building2 size={16} color={theme.colors.onSurface} style={styles.locationIcon} />
              </View>
              <Text style={styles.locationText}>
                {selectedControlUnit?.controlUnitCode
                  ? selectedControlUnit?.controlUnitCode
                  : selectedControlUnit?.controlUnitName}
              </Text>
            </TouchableOpacity>
          )}

        {showPeopleHirerachy && (
          <TouchableOpacity
            style={styles.peopleHirerachyContainer}
            onPress={() => {
              onPeopleHirerachyPress();
            }}
          >
            {selectedPeopleHirerachy?.first_name ? (
              <Avatar.Text
                label={`${selectedPeopleHirerachy?.first_name?.slice(0, 1)?.toUpperCase()}` || 'S'}
                size={moderateScale(30)}
              />
            ) : (
              <View style={{ width: moderateScale(30), height: moderateScale(30), borderRadius: 15, backgroundColor: '#3377ff', justifyContent: 'center', alignItems: 'center' }}>
                <User size={16} strokeWidth={2.5}color={"#fff"} />
              </View>
            )}
          </TouchableOpacity>)
        }

        {/* Unit Selector Menu */}
        {showUnitSelector && (
          <Menu
            visible={unitMenuVisible}
            onDismiss={() => setUnitMenuVisible(false)}
            contentStyle={styles.menuContent}
            anchor={
              <TouchableOpacity
                style={styles.unitSelectorButton}
                onPress={() => setUnitMenuVisible(true)}
              >
                <View style={styles.unitButtonContainer}>
                  <Text style={styles.unitButtonText} variant="medium">
                    {currentUnit?.label || 'Unit'}
                  </Text>
                  <ChevronDown size={12} color={theme.colors.onSurface} />
                </View>
              </TouchableOpacity>
            }
          >
            {unitOptions.map((option, index) => (
              <Menu.Item
                key={option.id}
                onPress={() => {
                  onUnitChange(option.value, option.id);
                  setUnitMenuVisible(false);
                }}
                title={option.label}
                titleStyle={[
                  styles.menuItemTitle,
                  {
                    fontWeight: selectedUnit === option.value ? 'bold' : 'normal',
                    color:
                      selectedUnit === option.value ? theme.colors.primary : theme.colors.onSurface,
                  },
                ]}
                trailingIcon={selectedUnit === option.value ? 'check' : ''}
                style={styles.menuItem}
              />
            ))}
          </Menu>
        )}

        {/* Existing Menu */}
        {showMenu && (
          <Menu
            visible={menuVisible}
            onDismiss={() => setMenuVisible(false)}
            contentStyle={styles.menuContent}
            anchor={
              <Appbar.Action
                icon="dots-vertical"
                color={theme.colors.onSurface}
                onPress={() => setMenuVisible(true)}
              />
            }
          >
            {menuItems.map((item, index) => (
              <Menu.Item
                key={index}
                onPress={() => {
                  setMenuVisible(false);
                  item.onPress();
                }}
                title={item.title}
                titleStyle={styles.menuItemTitle}
                leadingIcon={item.icon}
                style={styles.menuItem}
              />
            ))}
          </Menu>
        )}

        {rightElement && (
          <View style={{ marginRight: 12 }}>
            {typeof rightElement === 'function' ? rightElement() : rightElement}
          </View>
        )}
      </Appbar.Header>
    </View>
  );
};

export default AppHeader;

const styles = StyleSheet.create({
  header: {
    elevation: 0,
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  backButton: {
    marginLeft: -8, // Reduce left margin
    marginRight: -12, // Reduce space between back and menu
  },
  drawerButton: {
    // marginLeft: -12, // Reduce space from back button
    // marginRight: 8, // Add some space before content
  },
  title: {
    fontSize: moderateScale(16),
    fontFamily: 'Geist-SemiBold',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
    backgroundColor: '#F1F5F9',
    padding: 5,
    borderRadius: 20,
    paddingHorizontal: 10,
  },
  locationIconContainer: {
    padding: 5,
    backgroundColor: '#fff',
    justifyContent: 'center',
    marginRight: 4,
    borderRadius: 10,
  },
  locationIcon: {
    backgroundColor: '#fff',
  },
  locationText: {
    fontSize: moderateScale(11),
    fontFamily: 'Geist-Medium',
  },
  menuContent: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginTop: 50,
  },
  menuItem: {
    height: 50,
  },
  menuItemTitle: {
    fontSize: moderateScale(12),
    fontFamily: 'Geist-SemiBold',
  },
  peopleHirerachyContainer: {
    marginRight: moderateScale(20),
  },
  unitSelectorButton: {
    marginRight: 8,
  },
  unitButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F1F5F9',
    padding: 6,
    borderRadius: 16,
    paddingHorizontal: 10,
  },
  unitButtonText: {
    fontSize: moderateScale(11),

    marginRight: 10,
    color: '#374151',
  },
});
