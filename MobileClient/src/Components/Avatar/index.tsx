import React from 'react';
import { View, StyleSheet, Image, ImageSourcePropType, ImageStyle } from 'react-native';
import { moderateScale } from 'react-native-size-matters';
import Text from '../UI/Text';
interface CommonAvatarProps {
  label?: string; // initials/text fallback
  size?: number;
  backgroundColor?: string;
  textColor?: string;
  imageSource?: ImageSourcePropType; // for photo/logo image
  imageStyle?: ImageStyle;
  icon?: React.ReactElement; // icon element (e.g., from react-native-vector-icons)
  showBorder?: boolean;
  borderColor?: string;
  borderWidth?: number;
}

const CommonAvatar: React.FC<CommonAvatarProps> = ({
  label = '',
  size = 40,
  backgroundColor = '#cccccc',
  textColor = '#000000',
  imageSource,
  imageStyle,
  icon,
  showBorder = false,
  borderColor = '#fff',
  borderWidth = 2,
}) => {
  const radius = size / 2;

  if (icon) {
    return (
      <View
        style={[
          styles.iconContainer,
          {
            width: size,
            height: size,
            borderRadius: radius,
            backgroundColor,
            borderWidth: showBorder ? borderWidth : 0,
            borderColor,
            justifyContent: 'center',
            alignItems: 'center',
          },
        ]}
      >
        {React.cloneElement(icon, { size: size * 0.6, color: textColor })}
      </View>
    );
  }

  if (imageSource) {
    return (
      <Image
        source={imageSource}
        style={[
          {
            width: size,
            height: size,
            borderRadius: radius,
            borderWidth: showBorder ? borderWidth : 0,
            borderColor: borderColor,
          },
          imageStyle,
        ]}
        resizeMode="cover"
      />
    );
  }

  return (
    <View
      style={[
        styles.avatarContainer,
        {
          width: size,
          height: size,
          borderRadius: radius,
          backgroundColor,
          borderWidth: showBorder ? borderWidth : 0,
          borderColor,
          justifyContent: 'center',
          alignItems: 'center',
        },
      ]}
    >
      <Text style={[styles.avatarText, { color: textColor, fontSize: moderateScale(size / 2) }]} variant="medium">{label}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  avatarContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    // same centering and styling as avatarContainer
  },
  avatarText: {
    // fontWeight: 'bold',
  },
});

export default CommonAvatar;
