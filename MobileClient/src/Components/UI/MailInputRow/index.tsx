import React, { useState } from "react";
import { View,  TextInput, StyleSheet, TouchableOpacity, FlatList } from "react-native";
import {Text} from "react-native-paper"
import { ChevronDown, ChevronUp, X } from "lucide-react-native";
import { moderateScale, scale, verticalScale } from "react-native-size-matters";

interface Recipient {
  email: string;
}

interface MailInputRowProps {
  label: string;
  value: Recipient[];
  onChange: (recipients: Recipient[]) => void;
  showDropdown?: boolean;
  isOpen?: boolean;
  onToggleDropdown?: () => void;
  placeholder?: string;
}

const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

const MailInputRow: React.FC<MailInputRowProps> = ({
  label,
  value,
  onChange,
  showDropdown = false,
  isOpen = false,
  onToggleDropdown,
  placeholder,
}) => {
  const [input, setInput] = useState("");
  const [showSuggestion, setShowSuggestion] = useState(false);

  const handleAddRecipient = (text: string) => {
    if (emailRegex.test(text)) {
      onChange([...value, { email: text }]);
      setInput("");
      setShowSuggestion(false);
    }
  };

  const handleRemove = (email: string) => {
    onChange(value.filter(r => r.email !== email));
  };

  const handleChangeText = (text: string) => {
    setInput(text);
    if (emailRegex.test(text)) {
      setShowSuggestion(true);
    } else {
      setShowSuggestion(false);
    }
  };

  return (
    <View style={styles.row}>
      {label && <Text style={styles.label} variant="medium">{label}: </Text>}
      <View style={{ flex: 1 }}>
        <View style={styles.chipContainer}>
          {value.map((recipient, index) => (
            <View key={index} style={[styles.chip,
              // { marginBottom: index < value.length - 1 ? verticalScale(3) : 0 },
            ]}>
              <Text style={styles.chipText} variant="regular">{recipient.email}</Text>
              <TouchableOpacity onPress={() => handleRemove(recipient.email)}>
                <X strokeWidth={2.5} size={moderateScale(12)} color="#555" />
              </TouchableOpacity>
            </View>
          ))}
          <TextInput
            value={input}
            onChangeText={handleChangeText}
            placeholder={placeholder}
            style={styles.input}
            cursorColor="#3377ff"
            selectionColor="#3377ff"
            onSubmitEditing={() => handleAddRecipient(input)}
            placeholderTextColor={"#a4a4a4"}
          />
        </View>
        {showSuggestion && (
          <TouchableOpacity
            style={styles.suggestion}
            onPress={() => handleAddRecipient(input)}
          >
            <Text style={styles.suggestionText}>{input}</Text>
          </TouchableOpacity>
        )}
      </View>

      {showDropdown && (
        <TouchableOpacity onPress={onToggleDropdown} style={styles.dropdownBtn}>
          {isOpen ? (
            <ChevronUp size={moderateScale(18)} color="#555" />
          ) : (
            <ChevronDown size={moderateScale(18)} color="#555" />
          )}
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  row: {
    flexDirection: "row",
    alignItems: "flex-start",
    paddingHorizontal: scale(12),
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
    paddingVertical: verticalScale(10),
  },
  label: {
    marginRight: 4,
    fontSize: moderateScale(13),
    color: "#000",
  },
  chipContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  chip: {
    flexDirection: "row",
  
    alignItems: "center",
    backgroundColor: "#e0ecff",
    borderRadius: 20,
    paddingHorizontal: scale(7),
    paddingVertical: verticalScale(5),
    marginRight:scale(5)
  },
  chipText: {
    color: "#0057d9",
    marginRight: scale(5),
  },
  input: {
    flex: 1,
    fontSize: moderateScale(13),
    color: "#000",
    minWidth: 100,
  },
  suggestion: {
    backgroundColor: "#f1f1f1",
    padding: 8,
    borderRadius: 6,
    marginTop: 4,
  },
  suggestionText: {
    color: "#0057d9",
  },
  dropdownBtn: {
    justifyContent: "center",
    alignItems: "center",
    paddingLeft: scale(5),
  },
});

export default MailInputRow;
