import { Plane, Ship, Truck } from "lucide-react-native";

export const TransportationTypeIcon = (icon: string) => {
  switch (icon) {
    case "ship":
      return (props: any) => <Ship {...props} color="#020618" />;
    case "plane":
      return (props: any) => <Plane {...props} color="#E4004B" />;
    case "truck":
      return (props: any) => <Truck {...props} color="#3377FF" />;
    default:
      return (props: any) => <Truck {...props} color="#3377FF" />;
  }
};  

