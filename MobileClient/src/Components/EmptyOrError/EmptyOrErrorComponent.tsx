import {
  Image,
  ImageSourcePropType,
  StyleProp,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import React from 'react';
import { Button, useTheme, Text } from 'react-native-paper';
import { moderateScale, verticalScale } from 'react-native-size-matters';
import { FileText, RefreshCw } from 'lucide-react-native';
import { fontFamily } from '../../Common/Theme/typography';
import FastImage from 'react-native-fast-image';
interface EmptyTypes {
  message: string;
  customViewStyles?: StyleProp<ViewStyle>;
  customTextStyles?: StyleProp<TextStyle>;
  customButtonStyles?: any;
  handleRefresh?: () => void;
  buttonName?: string;
  emptyImage?: ImageSourcePropType;
  lucideIcon?: any;
}

const EmptyorErrorComponent: React.FC<EmptyTypes> = ({
  message,
  customViewStyles,
  customTextStyles,
  customButtonStyles,
  handleRefresh,
  buttonName = 'refresh',
  emptyImage,
  lucideIcon,
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  return (
    <View style={[styles.container, customViewStyles]}>
      {lucideIcon}

      {emptyImage && <FastImage source={emptyImage} style={[styles.emptyImageStyles]} />}

      <Text variant="semiBold" style={[styles.textStyles, customTextStyles]}>
        {message}
      </Text>
      {handleRefresh && (
        <TouchableOpacity style={styles.retryButton} onPress={handleRefresh}>
          <RefreshCw size={16} color="#2563eb" />
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default EmptyorErrorComponent;

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: moderateScale(20),
    },
    textStyles: {
      color: theme.colors.outline,
      textAlign: 'center',
      fontSize: moderateScale(13),
      marginBottom: verticalScale(10),
      fontFamily: fontFamily.medium,
    },
    refreshButton: { marginTop: moderateScale(16) },
    emptyImageStyles: {
      height: moderateScale(220),
      width: moderateScale(220),
    },
    retryButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#fff',
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#e2e8f0',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },

    retryText: {
      fontSize: 14,
      color: '#2563eb',
      fontWeight: '600',
      marginLeft: 8,
    },
  });
