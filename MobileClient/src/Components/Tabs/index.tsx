import React, { useState, useEffect, useRef } from 'react';
import { View, TouchableOpacity, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { Text } from 'react-native-paper';
import { moderateScale } from 'react-native-size-matters';
import { convertFirstCharacterCapital } from '../../Utils';
const Tabs = ({
  tabs,
  activeTab,
  onTabPress,
  showCounts = false,
  labelKey = 'label',
  valueKey = 'key',
  rounded=false,
}) => {
  const [shouldScroll, setShouldScroll] = useState(false);
  const [screenWidth, setScreenWidth] = useState(Dimensions.get('window').width);
  const [tabWidths, setTabWidths] = useState({});
  const scrollViewRef = useRef(null);

  useEffect(() => {
    const updateLayout = () => {
      setScreenWidth(Dimensions.get('window').width);
    };

    const subscription = Dimensions.addEventListener('change', updateLayout);
    return () => subscription?.remove();
  }, []);

  useEffect(() => {
    // Calculate individual tab widths and total width
    const newTabWidths = {};
    let totalWidth = 0;

    tabs.forEach((tab) => {
      const labelLength = tab[labelKey]?.length || 0;
      const countText = showCounts && tab.count !== undefined ? ` (${tab.count})`.length : 0;
      const textWidth = (labelLength + countText) * moderateScale(8.5);
      const tabWidth =  textWidth + moderateScale(40);

      newTabWidths[tab[valueKey]] = tabWidth;
      totalWidth += tabWidth;
    });

    setTabWidths(newTabWidths);
    setShouldScroll(totalWidth > screenWidth);
  }, [tabs, screenWidth, showCounts, labelKey, valueKey]);

  // Function to center the selected tab
  const centerActiveTab = (selectedTabKey) => {
    if (!shouldScroll || !scrollViewRef.current) return;

    const activeIndex = tabs.findIndex((tab) => tab[valueKey] === selectedTabKey);
    if (activeIndex === -1) return;

    // Calculate the position of the selected tab
    let tabPosition = 0;
    for (let i = 0; i < activeIndex; i++) {
      tabPosition += tabWidths[tabs[i][valueKey]] || moderateScale(80);
    }

    // Get the width of the selected tab
    const selectedTabWidth = tabWidths[selectedTabKey] || moderateScale(80);

    // Calculate the center position of the selected tab
    const tabCenter = tabPosition + selectedTabWidth / 2;

    // Calculate scroll position to center the tab on screen
    const screenCenter = screenWidth / 2;
    const scrollToX = Math.max(0, tabCenter - screenCenter);

    // Account for container padding
    const containerPadding = moderateScale(12);
    const adjustedScrollX = Math.max(0, scrollToX+containerPadding );

    scrollViewRef.current.scrollTo({
      x: adjustedScrollX,
      animated: true,
    });
  };

  const handleTabPress = (tabKey) => {
    onTabPress(tabKey);
    // Center the tab after a short delay to ensure the active state is updated
    setTimeout(() => centerActiveTab(tabKey), 100);
  };

  const TabButton = ({ tab, isActive, onPress, rounded }) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        isActive && styles.activeTabButton,
        !shouldScroll && styles.flexTabButton,
        shouldScroll && { width: tabWidths[tab[valueKey]] || moderateScale(80) },
        rounded && styles.roundedTabButton,
        rounded && isActive && styles.roundedActiveTab,
      ]}
      onPress={onPress}
    >
      {tab.icon ? tab.icon(isActive ? '#fff' : '#64748b') : null}
      <Text
        variant="semiBold"
        numberOfLines={1}
        style={[
          styles.tabText,
          isActive && styles.activeTabText,
          rounded && isActive ? styles.roundedActiveTabText : styles.roundedTabText,
        ]}
      >
        {convertFirstCharacterCapital(tab[labelKey])}
        {showCounts && tab.count !== undefined && (
          <Text style={[styles.countText, isActive && styles.activeCountText]}>
            {` (${tab.count})`}
          </Text>
        )}
      </Text>
    </TouchableOpacity>
  );

  if (shouldScroll) {
    return (
      <View>
        <ScrollView
          ref={scrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={rounded?styles.scrollContainerRounded:styles.scrollContainer}
          bounces={false}
          decelerationRate="fast"
        >
          {tabs.map((tab) => (
            <TabButton
              key={tab[valueKey]}
              tab={tab}
              isActive={activeTab === tab[valueKey]}
              onPress={() => handleTabPress(tab[valueKey])}
              rounded={rounded}
            />
          ))}
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={styles.tabContainer}>
      {tabs.map((tab) => (
        <TabButton
          key={tab[valueKey]}
          tab={tab}
          isActive={activeTab === tab[valueKey]}
          onPress={() => onTabPress(tab[valueKey])}
          rounded={rounded}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    justifyContent: 'space-around',
  },
  scrollContainer: {
    backgroundColor: '#fff',
    // paddingHorizontal: moderateScale(12),
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
  },
  scrollContainerRounded: {
    // backgroundColor: '#fff',
    // paddingHorizontal: moderateScale(12),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // paddingVertical:10,
    paddingHorizontal: moderateScale(12),
  },
  tabButton: {
    paddingVertical: moderateScale(8),
    paddingHorizontal: moderateScale(16),
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 5,

    // borderBottomColor: 'transparent',
    minWidth: moderateScale(60),
  },
  flexTabButton: {
    flex: 1,
    paddingHorizontal: moderateScale(8),
  },
  activeTabButton: {
    borderBottomColor: '#3377ff',
    borderBottomWidth: 2.5,
  },
  tabText: {
    fontSize: moderateScale(13),
    color: '#64748b',
    textAlign: 'center',
    // textTransform: 'capitalize',
  },
  activeTabText: {
    color: '#3377ff',
  },
  countText: {
    fontSize: moderateScale(12),
    color: '#666',
  },
  activeCountText: {
    color: '#3377ff',
  },
  roundedTabButton: {
    backgroundColor: '#fff',
    borderRadius: 24,
    marginHorizontal: moderateScale(4),
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingVertical: moderateScale(4),
  },
  roundedActiveTab: {
    backgroundColor: '#3377ff',
    borderColor: '#3377ff',
    shadowColor: '#3377ff',
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  roundedTabText: {
    fontSize: moderateScale(12),
    // color:"#fff"
  },
  roundedActiveTabText: {
    color: '#fff',
    fontSize: moderateScale(12),
  },
});

export default Tabs;
