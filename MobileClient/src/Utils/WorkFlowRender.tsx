import React, { useEffect, useState } from 'react';
import { View,  Image, TouchableOpacity, Alert,StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import ApiClient from '../Common/API/ApiClient';// Import your configured Axios instance
import { WORKFLOW_ENDPOINTS } from '../Common/API/ApiEndpoints';

// Helper: convert CSS-like styles (e.g. "120px") to numbers for RN
const convertSelectiveStyles = (styles) => {
  if (!styles) return {};
  const allowedKeys = [
    // 'padding',
    // 'paddingTop',
    // 'paddingBottom',
    // 'paddingLeft',
    // 'paddingRight',
    'backgroundColor',
    'flex',
    'flexDirection',
    'justifyContent',
    // 'width',
    'alignItems',
    'flexWrap',
    'gap',
    'flexBasis',
    'flexGrow',
    'flexShrink',
    'justifySelf',
    'alignSelf',
    'alignContent',
    
    'margin',
    // 'marginTop',
    // 'marginBottom',
    // 'marginLeft',
    // 'marginRight',
    'borderRadius',
    // 'height',
    
    // 'fontSize',
    'color',
    'fontWeight',
  ];

  const convertedStyles = {};

  for (const key of allowedKeys) {
    if (styles.hasOwnProperty(key)) {
      let val = styles[key];
      // Remove 'px' and convert to number for padding, margin, fontSize
      if (typeof val === 'string' && val.endsWith('px')) {
        val = parseFloat(val);
      }
      if(key==='fontWeight' && val === 'bold'){
        val = 'bold';
      }
      

      // For colors, allow string as is
      convertedStyles[key] = val;
    }
  }

  return convertedStyles;
};

// Event handler that triggers API call for button's events
const handleElementEvents = async (events = []) => {
  if (!events.length) return;

  const onClickEvent = events.find((e) => e.eventType === 'onclick');
  if (!onClickEvent) return;

  try {
    const { config, parameterData } = onClickEvent;

    const response = await ApiClient.request({
      method: config.method || 'POST',
      url: '/your-api-endpoint', // Replace with actual endpoint or dynamic mapping
      parameterData,
    });

    Alert.alert('Success', 'Action triggered successfully.');
    console.log('API response:', response.data);
  } catch (error) {
    Alert.alert('Error', 'Failed to trigger action.');
    console.error('API call failed', error);
  }
};

// Main element renderer
const RenderElement = ({ element, children }) => {
   const style = convertSelectiveStyles(element.styles);
    console.log("element",element)
  switch (element.type) {
    case 'page':
    case 'div':
      return <View style={[style,element.parentId === "" && {borderWidth:1,borderColor:"#ddd",padding:10,}]}>{children}</View>;

    case 'text':
      return <Text style={[style,element.fontWeight && defaultStyles.text]} variant={element.fontWeight}>{element.content}</Text>;

    case 'image':
      return <Image source={{ uri: element.src }} style={[style]} alt={element.alt} />;

    case 'button':
      return (
        <TouchableOpacity style={[style,defaultStyles.button]} onPress={() => handleElementEvents(element.events)}>
          <Text style={{color:"white"}} variant='semiBold'>{element.content}</Text>
        </TouchableOpacity>
      );

    // Add more types here as needed

    default:
      return null;
  }
};

// Recursive renderer
const RecursiveRenderer = ({ elements, parentId }) => {
  const children = elements.filter((e) => e.parentId === parentId);

  return children.map((child) => (
    <RenderElement key={child.id} element={child}>
      <RecursiveRenderer elements={elements} parentId={child.id} />
    </RenderElement>
  ));
};

// The dynamic UI component entry
export default  function DynamicUI({ data,schemaId }) {
  const [schemaName, setSchemaName] = useState('');
    const fetchSchemaName = async (schemaId: string) => {
      try {
        console.log('schemaId----<>', schemaId);
        const response = await ApiClient.post(WORKFLOW_ENDPOINTS.SCHEMA_NAME, { schemaId });

        console.log('response', response.data.data.schema_name);
        return response?.data?.data?.schema_name;
      } catch (error) {
        console.error('Error fetching schema name:', error);
        return null;
      }
    };
    console.log("schemaName",schemaName);
 useEffect(() => {
    const getSchemaName = async () => {
      const name = await fetchSchemaName(schemaId);
      setSchemaName(name);
    };
    getSchemaName();
  }, []);
 console.log("schemaName",schemaName)

  return (
    <View style={{ flex: 1 }}>
      <RecursiveRenderer elements={data} parentId="" />
  
    </View>
  );
}
const defaultStyles = StyleSheet.create({
  button: {
    // backgroundColor: '#3B82F6',
    padding: 10,
    borderRadius: 5,
    margin: 5,

  },
  text:{
   marginLeft:10 
  }
  
})