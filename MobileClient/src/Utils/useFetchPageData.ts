import React, { useEffect, useState } from 'react';
import ApiClient from '../Common/API/ApiClient';// Adjust path

export default function useFetchPageData(pageId) {
  const [uiData, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!pageId) return;


    const fetchPageData = async () => {
      setLoading(true);
      setError(null);
      try {
        const url = `/getMastersDropDown/Editor_Schema_dev/page_ref?columns=json_data&page_id=${pageId}`;
        console.log("url for page data", url);
        const response = await ApiClient.get(url);
                const parsedData = JSON.parse(response.data[0].json_data);
              
        // console.log("response data for page", response.data);
        // Assuming the response has the page data in response.data
        setData(parsedData);
      } catch (err) {
        console.log("error in fetching page data", err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchPageData();
  }, [pageId]);

  return { uiData, loading, error };
}
