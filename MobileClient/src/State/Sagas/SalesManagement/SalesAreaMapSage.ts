import { call, put, takeLatest } from 'redux-saga/effects';
import ApiClient from '../../../Common/API/ApiClient';
import { ApiEndpoints, ENDPOINTS } from '../../../Common/API/ApiEndpoints';
import { getUserId, getSchemaName } from '../../../Common/Utils/Storage';
import {
  fetchSalesAreaFailure,
  fetchSalesAreaRequest,
  fetchSalesAreaSuccess,
} from '../../Slices/SalesManagement/SalesAreaMapSlice';
import { encryptPayload } from '../../../Utils/payloadEncryption';

function* fetchSalesAreaMapSaga(action: any): any {
  try {
    console.log('action payload for sales area map', action.payload);
    const { date, userId } = action.payload;
    // console.log("userId in sales area map", userId)
    const loginUserId = yield call(getUserId);
    const schemaName = yield call(getSchemaName);
    const salesAreaPayload = {
      userId: userId ?? loginUserId,
      schemaName: schemaName,
      date: date ?? new Date(),
    };
    console.log("sales area payload", salesAreaPayload)
    const encryptedPayload = yield call(encryptPayload, salesAreaPayload);
    // console.log("sales area payload", encryptedPayload)
    const response = yield call(ApiClient.post, ENDPOINTS.SALES.AREA_MAP, encryptedPayload);
    // console.log('response for sales area', response?.data?.data);
    yield put(fetchSalesAreaSuccess(response?.data?.data));
  } catch (error: any) {
    console.log('Error in fetchSalesAreaMap saga:', error);
    yield put(fetchSalesAreaFailure(error.message));
  }
}

export default function* salesAreaWatcherSaga() {
  yield takeLatest(fetchSalesAreaRequest.type, fetchSalesAreaMapSaga);
}
