import { gql } from "@apollo/client";

// Updated to match the actual schema
export const GET_NOTIFICATIONS = gql`
  query GetMyNotifications(
    $userId: String!,
    $filter: NotificationFilterInput,
    $pagination: PaginationInput
  ) {
    getNotifications(
      userId: $userId,
      filter: $filter,
      pagination: $pagination
    ) {
      items {
        notificationId
        title
        is_read
        status
        message
        read_at
        type
        userId
        createdAt
        timestamp
      }
      total
      nextToken
    }
  }
`;

// Query for getting notification counts
export const GET_NOTIFICATION_COUNTS = gql`
  query GetNotificationCounts($userId: String!) {
    getNotificationCounts(userId: $userId) {
      read
      total
      unread
    }
  }
`;

export const MARK_NOTIFICATION_AS_READ_OR_UNREAD = gql`
  mutation markNotificationAsReadorUnread(
    $notificationId: String!
    $is_read: Boolean!
    $timestamp: AWSTimestamp!
  ) {
    markNotificationAsReadorUnread(
      notificationId: $notificationId
      is_read: $is_read
      timestamp: $timestamp
    ) {
      message
      notification {
        notificationId
        timestamp
        is_read
      }
    }
  }
`;





export const ON_CREATE_NOTIFICATION = gql`
  subscription OnCreateNotification($userId: String!) {
    onCreateNotification(userId: $userId) {
       notificationId
        title
        is_read
        status
        message
        read_at
        type
        userId
        createdAt
        timestamp
    }
  }
`;



export const MARK_ALL_NOTIFICATIONS_READ = gql`
mutation MyMutation($userId: String!) {
  markAllNotificationsAsRead(userId: $userId) {
    read
    total
    unread
  }
}`;
// Type definitions
export interface Notification {
  notificationId: string;
  title: string;
  message: string;
  is_read: boolean;
  status: string;
  read_at?: string;
  type: string;
  userId: string;
  createdAt: string;
  timestamp: number;
}

export interface NotificationsResponse {
  getNotifications: {
    items: Notification[];
    total: number;
    nextToken?: string;
  };
}

export interface NotificationCounts {
  getNotificationCounts: {
    read: number;
    total: number;
    unread: number;
  };
}

export interface MarkNotificationResponse {
  markNotificationAsReadorUnread: {
    message: string;
    notification: {
      notificationId: string;
      timestamp: number;
      is_read:boolean
    };
  };
}