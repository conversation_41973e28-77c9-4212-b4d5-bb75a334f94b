import React, { useState, useCallback, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, FlatList, Alert, Image } from 'react-native';
import { Text, Badge, ActivityIndicator, Button } from 'react-native-paper';
import { useQuery, useMutation, useSubscription, NetworkStatus, useApolloClient } from '@apollo/client';
import {
  GET_NOTIFICATIONS,
  GET_NOTIFICATION_COUNTS,
  MARK_NOTIFICATION_AS_READ_OR_UNREAD,
  type Notification,
  type NotificationsResponse,
  type NotificationCounts,
  type MarkNotificationResponse,
  ON_CREATE_NOTIFICATION,
  MARK_ALL_NOTIFICATIONS_READ,
} from './queries';
import NoDataComponent from './NoDataComponent';
import AppHeader from '../../Components/AppHeader';
import { moderateScale, scale } from 'react-native-size-matters';
import { useDispatch, useSelector } from 'react-redux';
import { Root } from 'postcss';
import { setUnreadCount } from '../../State/Slices/NotificationsSlice';
import AppLoader from '../../Components/Loader/AppLoader/InitialLoaderWithText';
import EmptyorErrorComponent from '../../Components/EmptyOrError/EmptyOrErrorComponent';
import { getUserId } from '../../Common/Utils/Storage';
import Animated,{FadeInLeft,FadeOutRight,LinearTransition} from 'react-native-reanimated';

const LIMIT = 10


enum TabType {
  UNREAD = 0, 
  READ = 1,   
  ALL = 2,  
}
interface TabConfig {
  key: TabType;   
  label: string;  
  count: number;   
}

const Notifications: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<TabType>(TabType.UNREAD);
    const [USER_ID, setUserId] = useState<string | null>(null);
  
  const [processingNotifications, setProcessingNotifications] = useState<Set<string>>(new Set());
  const client = useApolloClient();
const dispatch=useDispatch()
  useEffect(() => {
    (async () => {
      try {
        const id = await getUserId();
        console.log(id, "userid");
        setUserId(id);
      } catch (error) {
        console.log("Error fetching user ID:", error);
      }
    })();
  }, []);


  

 const getQueryVariables = useCallback(
    (tab: TabType) => {
      if (!USER_ID) return null;

      const baseVars = {
        userId: USER_ID,
        pagination: { limit: LIMIT },
      };

      switch (tab) {
        case TabType.UNREAD:
          return { ...baseVars, filter: { is_read: false } };
        case TabType.READ:
          return { ...baseVars, filter: { is_read: true } };
        case TabType.ALL:
        default:
          return baseVars;
      }
    },
    [USER_ID]
  );

  const {
    data,
    loading,
    error,
    fetchMore,
    refetch: refetchNotifications,
    networkStatus
  } = useQuery<NotificationsResponse>(GET_NOTIFICATIONS, {
    variables: getQueryVariables(selectedTab),
    notifyOnNetworkStatusChange: true,
    fetchPolicy: 'cache-and-network',
    errorPolicy: 'all',
     skip: !USER_ID,
  });
  const isRefreshing = networkStatus === NetworkStatus.refetch;
  const isLoadingMore=networkStatus===NetworkStatus.fetchMore
 console.log
  const {
    data: countsData,
    refetch: refetchCounts,
  } = useQuery<NotificationCounts>(GET_NOTIFICATION_COUNTS, {
    variables: { userId: USER_ID },
    fetchPolicy: 'cache-and-network',
    errorPolicy: 'all',
    skip: !USER_ID,
  });


// const [markAllNotificationsAsRead] = useMutation(MARK_ALL_NOTIFICATIONS_READ);
// const handleMarkAllAsRead = useCallback(
//   async () => {

//     try {
//       const {data}=await markAllNotificationsAsRead({
//         variables: {
//           userId: USER_ID, 
//         },
        
//       });
//       console.log('dgdfsgdas',data)

//       // Refetch counts to ensure consistency
//       await refetchCounts({ userId: USER_ID });
      
//     } catch (err) {
//       console.log('Error in handleMarkAllAsRead:', err);
//       // Optionally show an error alert
//       // Alert.alert('Error', 'Failed to mark all notifications as read');
//     }
//   },
//   [markAllNotificationsAsRead, selectedTab, refetchCounts, getQueryVariables]
// );
  useEffect(() => {
    if (countsData?.getNotificationCounts?.unread !== undefined) {
      dispatch(setUnreadCount(countsData.getNotificationCounts.unread));
    }
  }, [countsData, dispatch]);


  const { data: subscriptionData } = useSubscription(ON_CREATE_NOTIFICATION, {
    variables: { userId: USER_ID ?? "" },
    skip: !USER_ID, 
  });
  // console.log('Subscription Data:', subscriptionData,subscriptionerror);

  useEffect(() => {
    if (subscriptionData?.onCreateNotification) {
      const newNotification = subscriptionData.onCreateNotification;
      
      try {
        [TabType.UNREAD, TabType.ALL].forEach(tabType => {
          try {
            const queryVars = getQueryVariables(tabType);
            const existingData = client.readQuery<NotificationsResponse>({
              query: GET_NOTIFICATIONS,
              variables: queryVars,
            });

            if (existingData?.getNotifications) {
              const notificationExists = existingData.getNotifications.items.some(
                item => item.notificationId === newNotification.notificationId
              );

              if (!notificationExists) {
                client.writeQuery({
                  query: GET_NOTIFICATIONS,
                  variables: queryVars,
                  data: {
                    getNotifications: {
                      ...existingData.getNotifications,
                      items: [newNotification, ...existingData.getNotifications.items],
                    },
                  },
                });
              }
            }
          } catch (cacheError) {
            console.log(`Cache not available for ${tabType} tab, skipping update`);
          }
        });

        refetchCounts({ userId: USER_ID });
        
      } catch (error) {
        console.log('Cache update failed, falling back to refetch:', error);
        refetchNotifications(getQueryVariables(selectedTab));
        refetchCounts({ userId: USER_ID });
      }
    }
  }, [subscriptionData, client, getQueryVariables,USER_ID, refetchCounts, refetchNotifications,selectedTab]);

  const [markNotification, { loading: markingNotification }] = useMutation<MarkNotificationResponse>(
    MARK_NOTIFICATION_AS_READ_OR_UNREAD,
    {
      onCompleted: (resp) => {
        const returnedNotification = resp?.markNotificationAsReadorUnread?.notification;
        console.log('Mutation completed:', resp);
        if (returnedNotification?.notificationId) {
          setProcessingNotifications((prev) => {
            const newSet = new Set(prev);
            newSet.delete(returnedNotification.notificationId);
            return newSet;
          });
        } else {
          setProcessingNotifications(new Set());
        }
      },
      onError: (error) => {
        console.log('Mutation error:', error);
        setProcessingNotifications(new Set());
        const errorMessage =
          error.graphQLErrors?.[0]?.message ||
          error.networkError?.message ||
          'Failed to update notification. Please try again.';
        // Alert.alert('Update Failed', errorMessage, [{ text: 'OK' }]);
      },
      errorPolicy: 'all',
    }
  );

  const getTimeAgo = useCallback((timestamp: string): string => {
    try {
      const now = new Date();
      const past = new Date(timestamp);
      if (isNaN(past.getTime())) {
        return 'Unknown';
      }
      
      const diffMs = now.getTime() - past.getTime();
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (diffDays > 0) return `${diffDays}d`;
      if (diffHours > 0) return `${diffHours}h`;
      if (diffMinutes > 0) return `${diffMinutes}m`;
      return 'now';
    } catch (e) {
      return 'Unknown';
    }
  }, []);

  const badgeCounts = {
    [TabType.UNREAD]: countsData?.getNotificationCounts?.unread || 0,
    [TabType.READ]: countsData?.getNotificationCounts?.read || 0,
    [TabType.ALL]: countsData?.getNotificationCounts?.total || 0,
  };

  const handleTabChange = useCallback(
    async (tabIndex: TabType) => {
      if (tabIndex === selectedTab) return;
      
      setSelectedTab(tabIndex);
      await refetchNotifications(getQueryVariables(tabIndex));
    },
    [selectedTab, refetchNotifications, getQueryVariables]
  );

  const handleLoadMore = useCallback(async () => {
    const nextToken = data?.getNotifications?.nextToken;
    if (!nextToken || loading) {
      return;
    }

    try {
      await fetchMore({
        variables: {
          ...getQueryVariables(selectedTab),
          pagination: { limit: LIMIT, nextToken },
        },
        updateQuery: (prev, { fetchMoreResult }) => {
          if (!fetchMoreResult?.getNotifications) {
            return prev;
          }
          console.log('fdgsdfgsdf',getQueryVariables(selectedTab))

          return {
            getNotifications: {
              ...fetchMoreResult.getNotifications,
              items: [
                ...prev.getNotifications.items,
                ...fetchMoreResult.getNotifications.items.filter(
                  (newItem) =>
                    !prev.getNotifications.items.some(
                      (existingItem) => existingItem.notificationId === newItem.notificationId
                    )
                ),
              ],
            },
          };
        },
      });
    } catch (error) {
      console.log('Error loading more notifications:', error);
    }
  }, [data, loading, fetchMore, selectedTab, getQueryVariables]);

  const handleRefresh = useCallback(async () => {
    try {
      await refetchNotifications(getQueryVariables(selectedTab));
      refetchCounts({ userId: USER_ID });
    } catch (error) {
      console.log('Error refreshing:', error);
    }
  }, [refetchNotifications, selectedTab,USER_ID, getQueryVariables,refetchCounts]);

const handleToggleReadStatus = useCallback(
  async (notification: Notification) => {
    const notificationId = notification.notificationId;

    if (processingNotifications.has(notificationId) || markingNotification) return;

    if (!notificationId || !notification.timestamp) {
      console.log('Invalid notification data:', notification);
      // Alert.alert('Error', 'Invalid notification data');
      return;
    }

    setProcessingNotifications((prev) => {
      const newSet = new Set(prev);
      newSet.add(notificationId);
      return newSet;
    });

    const newReadStatus = !notification.is_read;

    try {
      await markNotification({
        variables: {
          notificationId,
          is_read: newReadStatus,
          timestamp: notification.timestamp,
        },
        optimisticResponse: {
          markNotificationAsReadorUnread: {
            notification: {
              __typename: 'Notification',
              notificationId,
              is_read: newReadStatus,
              timestamp: notification.timestamp,
            },
          },
        },
        update: (cache) => {
          const existing = cache.readQuery<NotificationsResponse>({
            query: GET_NOTIFICATIONS,
            variables: getQueryVariables(selectedTab),
          });

          if (existing?.getNotifications?.items) {
            const updatedItems = existing.getNotifications.items
              .map((item) =>
                item.notificationId === notificationId
                  ? { ...item, is_read: newReadStatus }
                  : item
              )
              .filter((item) =>
                selectedTab === TabType.UNREAD ? !item.is_read : true
              );

            cache.writeQuery({
              query: GET_NOTIFICATIONS,
              variables: getQueryVariables(selectedTab),
              data: {
                getNotifications: {
                  ...existing.getNotifications,
                  items: updatedItems,
                },
              },
            });
          }
        },
      });

      await refetchCounts({ userId: USER_ID });
    } catch (err) {
      console.log('Error in handleToggleReadStatus:', err);
    } finally {
      setProcessingNotifications((prev) => {
        const newSet = new Set(prev);
        newSet.delete(notificationId);
        return newSet;
      });
    }
  },
  [markNotification, processingNotifications, markingNotification,USER_ID, selectedTab, refetchCounts, getQueryVariables]
);


  const handleNotificationPress = useCallback(
    (item: Notification) => {
      if (!item.is_read) {
        handleToggleReadStatus(item);
      }
    },
    [handleToggleReadStatus]
  );

  const handleNotificationLongPress = useCallback(
    (item: Notification) => {
      const actionText = item.is_read ? 'Mark as Unread' : 'Mark as Read';
      Alert.alert(
        'Notification Options',
        `Do you want to ${actionText.toLowerCase()}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: actionText,
            onPress: () => handleToggleReadStatus(item),
          },
        ]
      );
    },
    [handleToggleReadStatus]
  );
  const renderNotificationItem = useCallback(
    ({ item }: { item: Notification }) => {
      const isUnread = !item.is_read;
      const isProcessing = processingNotifications.has(item.notificationId);

      return (
        <Animated.View 
        entering={FadeInLeft}
        exiting={FadeOutRight}
        layout={LinearTransition.springify()}
        >
        <TouchableOpacity
          style={[
            styles.notificationItem,
            isUnread && styles.unreadItem, 
            isProcessing && styles.processingItem 
          ]}
          onPress={() => handleNotificationPress(item)}
          onLongPress={() => handleNotificationLongPress(item)}
          disabled={isProcessing} 


       
        >

          <View style={{flexDirection: 'row'}}>
            <View style={{alignSelf:'flex-start',marginRight:moderateScale(16)}}>
              <Image
              source={require('../../../assets/ChatImages/1da7968b12d7adfe2cc4abfe4c4e871a3ff9ab89.png')}
                style={{width:moderateScale(48),height:moderateScale(48),borderRadius:moderateScale(50)}}/>
            </View>
          <View style={styles.notificationContent}>
            <View style={styles.headerRow}>
              <Text
                style={[styles.title]}
                variant="bold"
                numberOfLines={2}
               
              >
                {item.title || 'No Title'}
              </Text>
              <View style={styles.timestampContainer}>
                {item.createdAt && (
                  <Text style={styles.timestamp}>{getTimeAgo(item.createdAt)}</Text>
                )}
                {isProcessing && (
                  <ActivityIndicator size="small" color="#007AFF" style={styles.processingIndicator} />
                )}
              </View>
            </View>

            <Text style={[styles.message, isUnread && styles.unreadMessage]} numberOfLines={4} variant="medium">
              {item.message || 'No message'}
            </Text>
          </View>

          </View>
        </TouchableOpacity>
            <View style={{width:'100%',height:moderateScale(1),backgroundColor:'#E2E8F0'}}></View>
     </Animated.View>
      );
    },
    [handleNotificationPress, handleNotificationLongPress, processingNotifications, getTimeAgo]
  );


  const tabsConfig: TabConfig[] = [
    { key: TabType.UNREAD, label: 'Unread', count: badgeCounts[TabType.UNREAD] },
    { key: TabType.READ, label: 'Read', count: badgeCounts[TabType.READ] },
    { key: TabType.ALL, label: 'All', count: badgeCounts[TabType.ALL] },
  ];

  const notifications = data?.getNotifications?.items || [];



  return (
    <View style={styles.container}>
      <AppHeader title="Notifications" hideBackAction={true} />
     
      {/* <AppHeader title="Notifications" hideBackAction={true}  rightElement={<TouchableOpacity  onPress={()=>handleMarkAllAsRead()}><Text variant='bold' style={{color:"#3377ee"}}>Mark All as Read</Text></TouchableOpacity>}/> */}
      {
        (error && !data) ? (
          <EmptyorErrorComponent message='Something went wrong' handleRefresh={handleRefresh}/>
        ) : (
          <>
      <View style={styles.tabContainer}>
        {tabsConfig.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            onPress={() => handleTabChange(tab.key)}
            style={[styles.tab, selectedTab === tab.key && styles.activeTab]}
            activeOpacity={0.7}
            accessible={true}
            accessibilityRole="tab"
            accessibilityLabel={`${tab.label} notifications, ${tab.count} items`}
            accessibilityState={{ selected: selectedTab === tab.key }}
          >
            <View style={[styles.tabContent, selectedTab === tab.key && styles.activeTabContent]}>
              <Text
              variant='semiBold'
                style={[styles.tabText,  selectedTab === tab.key && styles.activeTabText]}
              >
                {tab.label}
              </Text>
              <Text
                style={[styles.badge, selectedTab === tab.key && styles.activeBadge]}
                variant='semiBold'
                // size={18}
              >
                {tab.count}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>


      <View style={styles.content}>
        {loading && notifications.length === 0 ? (
          <AppLoader />
        ) : null}

        {!loading && notifications.length === 0 ? <NoDataComponent /> : null}

 
        {notifications.length > 0 ? (
          <FlatList
            data={notifications}
            keyExtractor={(item) => item.notificationId}
            renderItem={renderNotificationItem}
            onEndReached={handleLoadMore}
            // onEndReachedThreshold={0.3} 
            showsVerticalScrollIndicator={false}
           
            ListFooterComponent={
              isLoadingMore && notifications.length > 0 ? (
                <View style={styles.footerLoader}>
                  <ActivityIndicator size="small" color="#007AFF" />
                </View>
              ) : null
            }
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
          />
        ) : null} 
      </View>
      </>
        )
      }

    </View>
  );
};
export default Notifications;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: scale(16),
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    paddingVertical: moderateScale(8),
    paddingHorizontal: scale(12),
    marginHorizontal: scale(4),
  },
  activeTab: {
    borderColor: '#007AFF',
    borderBottomWidth: moderateScale(3),
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTabContent: {
    // Additional styles for active tab content if needed
  },
  tabText: {
    fontSize: moderateScale(13),
    color: '#64748b',
    marginRight: scale(6),
  },
  activeTabText: {
    fontSize: moderateScale(13),
  },
  badge: {
    backgroundColor: '#e0e0e0',
    paddingHorizontal: moderateScale(8),
    borderRadius: moderateScale(10),
    color: '#334155',
    fontSize: moderateScale(11),
    paddingVertical:moderateScale(1),
  },
  activeBadge: {
    backgroundColor: '#3377FF',
    color: '#fff',
  },
  content: {
    flex: 1,
  },
   loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: moderateScale(40),
  },
  loadingText: {
    marginTop: moderateScale(16),
    fontSize: moderateScale(16),
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: scale(20),
  },
  errorText: {
    fontSize: moderateScale(16),
    color: '#ff3b30',
    textAlign: 'center',
    marginBottom: moderateScale(16),
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: scale(20),
    paddingVertical: moderateScale(10),
    borderRadius: moderateScale(8),
  },
  retryText: {
    color: '#fff',
    fontSize: moderateScale(16),
    fontWeight: '500',
  },
  notificationItem: {
    backgroundColor: '#fff',
    paddingVertical: scale(18),
    paddingHorizontal: scale(16),
    borderBottomWidth: moderateScale(0.4),
    borderColor: '#eeeeee',
    
  },
  unreadItem: {
    borderLeftColor: '#007AFF',
    backgroundColor: '#f8fbff',
    borderLeftWidth: moderateScale(4),
    borderStartColor: '#3377ff',
   
  },
  processingItem: {
    opacity: 0.7,
  },
  notificationContent: {
    flex: 1,
    // minHeight: moderateScale(80),
    
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: moderateScale(8),
  },
  title: {
    flex: 1,
    fontSize: moderateScale(14),
    marginRight: scale(8),
    lineHeight: moderateScale(18),
  },
  // unreadTitle: {
  //   fontWeight: '600',
  //   // color: '#3377ff',
  // },
  timestampContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timestamp: {
    fontSize: moderateScale(12),
  },
  processingIndicator: {
    marginLeft: scale(8),
  },
  message: {
    fontSize: moderateScale(12),
    lineHeight: moderateScale(20),
  },
  unreadMessage: {
    color: '#334155',
  },
  footerLoader: {
    paddingVertical: moderateScale(20),
    alignItems: 'center',
  },
});