import { Package, Truck,Plane,Ship } from 'lucide-react-native';
export const getTypeBadge = (type: string) => {
  switch (type) {
    case 'Face to Face':
      return { label: 'Face to Face', color: '#EBF2FF', textColor: '#3377FF' };
    case 'Online':
      return { label: 'Online', color: '#F3E8FF', textColor: '#9C27B0' };
    case 'Call':
      return { label: 'Call', color: '#FFE8E6', textColor: '#F44336' };
    default:
      return { label: type, color: '#E0E0E0', textColor: '#757575' };
  }
};

export  const getStatusColor = (status) => {
    switch (status) {
      case 'At Port':
        return '#47b6e6';
        case 'Delivered':
        return '#27c087';
      case 'Loading':
        return '#ff6b6b';
        case 'In Transit':
        return '#ffb74d';
      case 'Expired':
        return '#a0a0ab';
      default:
        return '#a0a0ab';
    }
  };
export    const getStatusBgColor = (status) => {
    switch (status) {
      case 'At Port':
        return '#e9f5faff';
      case 'Delivered':
        return '#ecfaf5ff';
      case 'Loading':
        return '#fdeeeeff';
      case 'In Transit':
        return '#fdf8f1ff';
      case 'Expired':
        return 'rgba(253, 241, 241, 1)';
      default:
        return '#f5f5f5';
    }
  };



