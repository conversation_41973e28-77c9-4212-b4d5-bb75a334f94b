import React, {useCallback, useState} from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  TouchableOpacity,

  SafeAreaView,
} from 'react-native';
import {
  Text,
  Searchbar,
  useTheme,
  Checkbox,
  Portal,
  Modal,
  ActivityIndicator,
  Badge,
} from 'react-native-paper';
import SearchBar from '../../../Components/SearchInput';
import { Search, Filter, SlidersHorizontal, Funnel } from 'lucide-react-native';
import {moderateScale,scale,verticalScale} from 'react-native-size-matters';
import {useWindowDimensions} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useLeads} from '../Hooks/CrmLeads';
import LeadCard from './LeadCard/LeadCard';
import OptimizedLeadsList from './LeadCard/OptimizedLeadsList';
import {ref} from 'yup';
interface LeadsListProps {
  leads: Array<any>;
  loading: boolean;
  error: string | null;
  searchQuery: string;
  hasMoreData: boolean;
  refreshing: boolean;
  onSearch: (query: string) => void;
  onRefresh: () => void;
  loadMore: () => void;
  onUpdateFilters: (leadIds: number[]) => void;
}

const LEAD_TYPE_IDS = {
  PROSPECT: 2,
  OPPORTUNITY: 3,
  CUSTOMER: 4,
  ALL: [2, 3, 4],
};

// Define a reusable FilterCheckboxItem component
interface FilterCheckboxItemProps {
  label: string;
  isChecked: boolean;
  onToggle: () => void;
}

const FilterCheckboxItem: React.FC<FilterCheckboxItemProps> = ({
  label,
  isChecked,
  onToggle,
}) => (
  <TouchableOpacity
    onPress={onToggle}
    style={styles.filterCheckboxRow}
    activeOpacity={0.7}>
    <Checkbox status={isChecked ? 'checked' : 'unchecked'} />
    <Text style={styles.filterCheckboxLabel}>{label}</Text>
  </TouchableOpacity>
);

const LeadsList: React.FC<LeadsListProps> = ({
  leads,

  loading,
  error,
  searchQuery,
  hasMoreData,
  refreshing,
  onSearch,
  onRefresh,
  loadMore,
  onUpdateFilters,
}) => {
  const theme = useTheme();

  // Initialize hook with initial values
  // console.log("Leads \n",leads)
  // Local state for filters
  const [leadTypeFilters, setLeadTypeFilters] = useState({
  All: true,
  Prospects: false,
  Opportunity: false,
  Customer: false,
});
  const [filterSheetVisible, setFilterSheetVisible] = useState(false);

const handleToggleFilter = (type: string) => {
  let newFilters = { ...leadTypeFilters };

  if (type === 'All') {
    newFilters = {
      All: true,
      Prospects: false,
      Opportunity: false,
      Customer: false,
    };
  } else {
    newFilters[type] = !newFilters[type];

    const selectedFilters = ['Prospects', 'Opportunity', 'Customer'].filter(
      (key) => newFilters[key]
    );

    if (selectedFilters.length === 0 || selectedFilters.length === 3) {
      newFilters = {
        All: true,
        Prospects: false,
        Opportunity: false,
        Customer: false,
      };
    } else {
      newFilters.All = false;
    }
  }

  setLeadTypeFilters(newFilters);
};

  // Handle end reached for pagination
  const handleEndReached = () => {
    if (!loading && hasMoreData) {
      loadMore();
    }
  };
  // console.log("hasmoredata", hasMoreData, "loading", loading)
  // Define filter options for mapping
  const filterOptions = [
    {key: 'All', label: 'All'},
    {key: 'Prospects', label: 'Prospects'},
    {key: 'Opportunity', label: 'Opportunity'},
    {key: 'Customer', label: 'Customer'},
  ];

  const fileterHandler = () => {
    let leadsIdsToUse: number[] = [];
    if (leadTypeFilters.Prospects) leadsIdsToUse.push(LEAD_TYPE_IDS.PROSPECT);
    if (leadTypeFilters.Opportunity) leadsIdsToUse.push(LEAD_TYPE_IDS.OPPORTUNITY);
    if (leadTypeFilters.Customer) leadsIdsToUse.push(LEAD_TYPE_IDS.CUSTOMER);

    if (leadsIdsToUse.length === 0) {
      leadsIdsToUse = [2, 3, 4];
    }

    onUpdateFilters(leadsIdsToUse);
    setFilterSheetVisible(false)
  }


  const getSelectedCountAndKeys = (filters: Record<string, boolean>) => {
  let selectedKeys: string[] = [];

  if (filters.All) {
    // All selected: select everything except 'All'
    selectedKeys = Object.keys(filters).filter(key => key !== 'All');
  } else {
    // Select only keys with true values
    selectedKeys = Object.keys(filters).filter(key => filters[key] && key !== 'All');
  }

  const count = selectedKeys.length;

  return { count, selectedKeys };
};
const { count, selectedKeys } = getSelectedCountAndKeys(leadTypeFilters);
console.log('selectedKeys',selectedKeys)


// console.log('refreshing',loading , "refreshing", refreshing)
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={[styles.headerRow, { backgroundColor: theme.colors.background }]}>
        <SearchBar
          placeholder="Search Leads..."
          onChangeText={onSearch}
          value={searchQuery}
          style={{ flex: 1 }}
        />
        <TouchableOpacity
            style={[styles.filterIconWrap,{borderColor: count > 0 &&count<3 ?  theme.colors.primary : '#E0E0E0' ,}]}

  activeOpacity={0.7}
  onPress={() => setFilterSheetVisible(true)}
>
{(count > 0 && count < 3) && (
  <Badge
    size={moderateScale(15)}
    style={{
      position: 'absolute',
      top: -moderateScale(8),
      right: -moderateScale(8),
      backgroundColor: theme.colors.primary,
      zIndex: 2,
    }}
  >
    {count}
  </Badge>
)}


  {/* Funnel icon with dynamic color */}
  <Funnel
    size={moderateScale(20)}
    color={count > 0 &&count<3 ? theme.colors.primary : theme.colors.onSurfaceVariant || '#888'}
  />
</TouchableOpacity>

      </View>

      {/* Filter modal */}
      <Portal>
        <Modal
          visible={filterSheetVisible}
          onDismiss={() => setFilterSheetVisible(false)}
          contentContainerStyle={[styles.bottomSheet, { backgroundColor: theme.colors.background }]}
          dismissable
        >
          <Text style={styles.filterSectionTitle} variant="semiBold">
            Types of Leads
          </Text>
          <View style={{ flexDirection: 'column' }}>
            {/* Map through filter options instead of repeating code */}
            {filterOptions.map((option) => (
              <FilterCheckboxItem
                key={option.key}
                label={option.label}
                isChecked={leadTypeFilters[option.key]}
                onToggle={() => handleToggleFilter(option.key)}
              />
            ))}
          </View>
          <Text style={styles.bottomSheetDone} onPress={fileterHandler}>
            Save
          </Text>
        </Modal>
      </Portal>

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
       <ActivityIndicator size={moderateScale(27)} color={theme.colors.primary} />
          <Text style={{ marginTop: 10 }}>Loading leads...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Error: {error}</Text>
        </View>
      ) : (
        <OptimizedLeadsList
          leads={leads}
          handleEndReached={handleEndReached}
          refreshing={refreshing}
          onRefresh={onRefresh}
          hasMoreData={hasMoreData}
          styles={styles}
        />
        // <FlatList
        //   data={leads}
        //   keyExtractor={item => item.id}
        //   renderItem={({item}) => <LeadCard item={item} />}
        //   onEndReached={handleEndReached}
        //   onEndReachedThreshold={0.1}
        //   refreshing={refreshing}
        //   onRefresh={onRefresh}
        //   ItemSeparatorComponent={() => <View style={{height: 10}} />}
        //   contentContainerStyle={[
        //     styles.listContent,
        //     leads.length === 0 && styles.emptyListContent,
        //   ]}
        //   ListFooterComponent={() =>
        //     hasMoreData  ? (
        //       <View style={styles.footerLoader}>
        //         <ActivityIndicator size="small" color={theme.colors.primary} />
        //       </View>
        //     ) : null
        //   }
        //   ListEmptyComponent={
        //     <Text
        //       style={{
        //         textAlign: 'center',
        //         marginTop: 40,
        //         color: theme.colors.onSurfaceVariant,
        //       }}>
        //       No leads found
        //     </Text>
        //   }
        // />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    paddingTop:10,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    // padding: moderateScale(12),
    backgroundColor: 'white',
    gap: moderateScale(8),
    marginHorizontal: 16,
    marginBottom: verticalScale(5),
    // marginBottom: 8,
  },
  searchbar: {
    flex: 1,
    borderRadius: 5,
    padding: 0,
    height: verticalScale(28),
  },
  searchInput: {
    fontSize: moderateScale(12),
    // height:40,
    fontFamily: 'Geist-Medium',
    alignSelf: 'center',
  },
  filterIconWrap: {
    height: verticalScale(32),
    width: verticalScale(32),
    borderRadius: 5,
    backgroundColor: '#FFF',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
  },
  listContent: {
    flexGrow: 1,
    paddingBottom: moderateScale(60),
    marginHorizontal: moderateScale(14),
  },
  emptyListContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterCheckboxLabel: {
    fontSize: moderateScale(14),
    color: '#222',
  },
  filterCheckboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 2,
    paddingHorizontal: 2,
    borderRadius: 8,
    marginBottom: 2,
  },
  bottomSheet: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    borderTopLeftRadius: 18,
    borderTopRightRadius: 18,
    padding: 20,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
  },
  bottomSheetDone: {
    textAlign: 'right',
    color: '#3B6FF6',
    fontWeight: 'bold',
    fontSize: 16,
    marginTop: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
  },
  filterSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  footerLoader: {
    paddingVertical: 20,
    alignItems: 'center',
  },
});

export default LeadsList;
