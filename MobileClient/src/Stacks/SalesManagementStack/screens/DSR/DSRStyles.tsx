import { StyleSheet } from "react-native"
import { moderateScale, verticalScale } from "react-native-size-matters";
export default StyleSheet.create({
  container: {
    flex: 1,
    paddingTop:10,
    // backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    // color: '#F44336',
    textAlign: 'center',
    marginBottom: 10,
  },
  listContent: {
    padding: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  sectionContainer: {
    // marginVertical: 8,
    // backgroundColor: 'white',
    borderRadius: 8,
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    // gap:10,
    justifyContent:'space-between',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: verticalScale(5),
    borderTopLeftRadius:8,
    borderTopRightRadius:8,
  },
  todayHeader: {
   
  },
  pastHeader: {
   
  },
  sectionTitle: {
    fontSize:moderateScale(13),
  },
  todayTitle: {
  
  },
  pastTitle: {
   
  },
  sectionHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionStatus: {
    fontSize: moderateScale(11),
   
    
  },
  badgeStyle : {
      paddingHorizontal: 10,
      paddingVertical: 5,
      borderRadius: 5,
      flexDirection:"row",
      alignItems:"center"
    },
  todayStatus: {
    // color: 'rgba(255,255,255,0.9)',
    // backgroundColor: 'rgba(255,255,255,0.2)',
    // paddingHorizontal: 8,
    // paddingVertical: 2,
    // borderRadius: 4,
  },
  pastStatus: {
    // color: '#444',
    // backgroundColor: '#e0f2f1',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  sectionContent: {
    // paddingVertical: 8,
  },
  activityItemContainer: {
    // marginBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    paddingBottom: 12,
    // backgroundColor: '#f9f9f9',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius:8,
  },
  activityItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
    paddingHorizontal: 12,
    paddingTop: 12,
  },
  activityHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  circleCompleted: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  circleIncomplete: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#ccc',
    marginRight: 10,
  },
  checkmark: {
    color: 'white',
    fontSize: 14,
    
  },
  companyName: {
    fontSize: moderateScale(11),
  
    color: '#333',
  },
  timeAgo: {
    fontSize: 12,
    color: '#999',
  },
  activityDetails: {
    paddingLeft: 46,
    paddingRight: 12,
    marginBottom: 8,
  },
  categoryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  categoryBadge: {
    alignSelf: 'flex-start',
    backgroundColor: '#e8eaf6',
    paddingHorizontal: 10,
    paddingVertical: 2,
    borderRadius: 12,
    marginRight: 8,
  },
  categoryText: {
    color: '#3F51B5',
    fontSize: moderateScale(12),
   
  },
  activityType: {
    fontSize: moderateScale(14),
    color: '#666',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginBottom: 4,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  locationText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  timeText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  onlineTag: {
    fontSize: 14,
    color: '#4CAF50',
  },
  locationTag: {
    fontSize: 14,
    color: '#FF9800',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  tag: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 12,
    color: '#666',
  },
  completedNotesContainer: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginHorizontal: 12,
    marginTop: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#4CAF50',
    backgroundColor: '#fff',
    borderRadius: 4,
  },
  noteHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  noteLabel: {
    fontSize: 14,
   
    color: '#333',
    marginRight: 8,
  },
  outcomeTag: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  outcomeTagText: {
    fontSize: 12,
    
  },
  noOutcomeTag: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  noOutcomeText: {
    fontSize: 12,
    color: '#999',
  },
  notesText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  activityActions: {
    alignItems: 'flex-end',
    paddingRight: 12,
  },
  commentButton: {
    padding: 4,
  },
  expandedContent: {
    backgroundColor: '#f9f9f9',
    padding: 16,
    marginTop: 8,
  },
  outcomeSection: {
   backgroundColor: '#fff',
    marginBottom: 16,
    padding:20,
  
  },
  sectionLabel: {
    fontSize: 14,
 
    marginBottom: 10,
    color: '#333',
  },
  outcomeOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  outcomeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    backgroundColor: '#fff',
  },
  outcomeButtonText: {
    fontSize: 12,
    color: '#777',
  },
  notesSection: {
    marginVertical: 16,
  },
  notesInput: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 4,
    padding: 8,
    minHeight: 60,
    textAlignVertical: 'top',
  },
  completeButtonContainer: {
    alignItems: 'flex-end',
    marginTop: 8,
  },
  completeButton: {
    borderRadius: 4,
    backgroundColor: '#4CAF50',
  },
  completeButtonContent: {
    paddingHorizontal: 16,
    paddingVertical: 4,
  },
  completeButtonLabel: {
    fontSize: 14,
  },
  pendingActivitiesText: {
    fontSize: 12,
    marginLeft:4,
  },
  pendingActivitiesView:{
backgroundColor: '#FFAB00',
padding:1,
paddingHorizontal:6,
borderRadius:20,
  },
  pendingActivitiesCount: {
    color:"white",
    fontSize:14,
  },
  completedActivitiesText: {
    color: '#388E3C',
    fontSize: 12,
  },
  scrollIndicator: {
    position: 'absolute',
    right: 16,
    bottom: 70,
    backgroundColor: 'rgba(60, 60, 60, 0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  scrollIndicatorText: {
    color: 'white',
    fontSize: 12,

    marginLeft: 4,
  },
  dateTimeSection: {
    marginTop: 16,
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  dateTimeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent:"space-between",
    marginBottom: 12,
  },
  dateTimeLabel: {
    fontSize: 14,
    color: '#333',
    marginRight: 8,

  },
  dateTimePickerContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 8,
  },
  datePickerButton: {
    flex: 1,
    padding: 10,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ccc',
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
  },
  timePickerButton: {
    padding: 10,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ccc',
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    width: 100,
  },
  activityItemView:{
    backgroundColor:"#FAFAFA",
    flexDirection:"row",
    justifyContent:"space-between",
    alignItems:"center",
    padding:16,
    borderRadius:8,
    marginVertical:8
  },
  activityItemViewTitle:{
    fontSize:14,
  },
  activityStatusView:{
    backgroundColor:"#FEF5E5",
    borderWidth:1,
    paddingVertical:4,
    paddingHorizontal:12,
    borderColor:"#FFAB00",
    borderRadius:15
  },
  activityStatusViewText:{
color:"#FFAB00",
fontSize:12,
  }

});
