import { StyleSheet, Text, View } from 'react-native';
import AppHeader from '../../../Components/AppHeader';
import { useTheme } from 'react-native-paper';
import React, { useState } from 'react';
import { createLeadStyles } from './Leads/LeadsScreen.styles';
import { useControlUnitsData } from '../Hooks/GlobalControlData';
import { useSelector } from 'react-redux';
import MenuTabs from '../components/MenuTabs';
import LeadsScreen from './Leads/LeadsScreen';
import DSRScreen from './DSR/DSRScreen';
import CalendarScreen from './Calendar/CalendarScreen';
import SalesAreaMapScreen from './MapArea/SalesAreaMap';
import { ControlUnitModal } from './Leads/LeadsScreen.modals';
import { usePeopleHirerachy } from '../Hooks/GlobalControlData';
import { UserSelectionModal } from './Leads/LeadsScreen.modals';
import Tabs from '../../../Components/Tabs';
import { verticalScale } from 'react-native-size-matters';

const Sales = ({ route }) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState('leads');

  const tabs = [
    { label: 'Leads', value: 'leads' },
    { label: 'DSR', value: 'dsr' },
    { label: 'Calendar', value: 'calendar' },
    { label: 'Area', value: 'area' },
  ];
  const styles = createLeadStyles(theme);
  const appData = route.params?.appData;
  const controlUnitsResult = useControlUnitsData(appData?.controlUnitLevelId);
  const {
    controlUnits,
    showControlUnitsModal,
    error: controlUnitsError,
    handleControlUnits,
    setShowControlUnitsModal,
    setSelectControlUnit,
  } = controlUnitsResult;
  const {
    handlePeopleHirerachy,
    showPeopleHirerachyModal,
    peopleHirerachyData,
    setShowPeopleHirerachyModal,
  } = usePeopleHirerachy();
  const selectedControlUnitData = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data
  );
  const handleControlUnitSelect = (controlUnit: any) => {
    console.log('Control unit selected:', controlUnit);
    setSelectControlUnit(controlUnit);
  };
  const peopleHirerachySelectedData = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedPeopleHirerachy?.data
  );
  // console.log("peopleHirerachySelectedData in dsr screen", peopleHirerachySelectedData)

  const selectedUserId = peopleHirerachySelectedData?.account_user_id || '';
  const renderComponent = () => {
    switch (activeTab) {
      case 'leads':
        return (
          <LeadsScreen
            appData={appData}
            controlUnitsResult={controlUnitsResult}
            selectedControlUnitData={selectedControlUnitData}
          />
        );
      case 'dsr':
        return (
          <DSRScreen
            appData={appData}
            controlUnitsResult={controlUnitsResult}
            selectedControlUnitData={selectedControlUnitData}
          />
        );
      case 'calendar':
        return (
          <CalendarScreen
            appData={appData}
            controlUnitsResult={controlUnitsResult}
            selectedControlUnitData={selectedControlUnitData}
          />
        );
      case 'area':
        return (
          <SalesAreaMapScreen
            appData={appData}
            controlUnitsResult={controlUnitsResult}
            selectedControlUnitData={selectedControlUnitData}
          />
        );
      default:
        return (
          <View style={styles.defaultContainer}>
            <Text style={styles.defaultText}>Select a tab to view content</Text>
          </View>
        );
    }
  };
  return (
    <View style={{ flex: 1, backgroundColor: '#fff' }}>
      <AppHeader
        title="Sales"
        onControlUnitsPress={handleControlUnits}
        selectedControlUnit={selectedControlUnitData}
        showControlUnit={true}
        showPeopleHirerachy={true}
        onPeopleHirerachyPress={handlePeopleHirerachy}
        selectedPeopleHirerachy={peopleHirerachySelectedData}
        // appData={appData}
      />
      {/* <MenuTabs tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} /> */}
      <View style={{borderBottomWidth:1,borderColor:"#E4E4E7"}}><Tabs tabs={tabs} activeTab={activeTab} onTabPress={setActiveTab} labelKey='label' valueKey='value'/></View>
      <View style={{ flex: 1 }}> {renderComponent()}</View>

      <ControlUnitModal
        visible={showControlUnitsModal}
        onDismiss={() => setShowControlUnitsModal(false)}
        controlUnits={controlUnits || []}
        onSelect={handleControlUnitSelect}
      />
      <UserSelectionModal
        visible={showPeopleHirerachyModal}
        onDismiss={() => setShowPeopleHirerachyModal(false)}
        peopleHirerachyData={peopleHirerachyData || []}
        onSelect={handlePeopleHirerachy}
      />
    </View>
  );
};

export default Sales;

const styles = StyleSheet.create({});
