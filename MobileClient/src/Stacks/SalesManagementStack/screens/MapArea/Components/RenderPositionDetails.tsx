import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { fontFamily } from '../../../../../Common/Theme/typography';
import FastImage from 'react-native-fast-image';
interface PositionDetailsProps {
  selectedMarker: any;
  formatTime: (time: string) => string;
}

const PositionDetailsComponent: React.FC<PositionDetailsProps> = ({
  selectedMarker,
  formatTime,
}) => {
  if (!selectedMarker) return null;

  const {
    sales_activity_type_name,
    checkin_image_url,
    checkin_timestamp,
    checkin_address,
    checkout_image_url,
    checkout_timestamp,
    checkout_address,
  } = selectedMarker;

  const fallbackImage = require('../../../../../../assets/ChatImages/UserProfile.png');
  // console.log('selectedMarker data', JSON.stringify(selectedMarker, null, 2));
  return (
    <View style={styles.positionCard}>
      <View style={[styles.sectionTitle, { flexDirection: 'row', padding: (2), gap: scale(6) }]}>

        <Text style={styles.checkIn}  >Check In</Text>
        <Text style={styles.badge} >{sales_activity_type_name}</Text>
      </View>

      <View style={styles.entryContainer}>
        {checkin_image_url?.[0] && (
          <FastImage
            source={checkin_image_url?.[0] ? { uri: checkin_image_url?.[0] } : fallbackImage}
            style={styles.avatar}
          />
        )}

        <View style={styles.details}>
          <View style={styles.chip}>
            <Text style={styles.chipText}>{formatTime(checkin_timestamp)}</Text>
            <Text style={styles.addressText} numberOfLines={3}>
              {checkin_address}
            </Text>
          </View>
        </View>
      </View>
      {checkout_address && checkout_timestamp &&(
        <>
          <Text style={[styles.sectionTitle]}>Check Out</Text>

          <View style={styles.entryContainer}>
            {checkout_image_url?.[0] && (
              <FastImage
                source={checkout_image_url?.[0] ? { uri: checkout_image_url[0] } : fallbackImage}
                style={styles.avatar}
              />
            )}

            <View style={styles.details}>
              <View style={styles.chip}>
                <Text style={styles.chipText}>
                  {checkout_timestamp ? formatTime(checkout_timestamp) : 'Not yet checked out'}
                </Text>
                <Text style={styles.addressText} numberOfLines={3}>
                  {checkout_address || 'No checkout address'}
                </Text>
              </View>
            </View>
          </View>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  positionCard: {
    position: 'absolute',
    bottom: 10,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    // marginHorizontal : moderateScale(20),
    borderRadius: 12,
    padding: 16,
    // margin: 12,
    marginHorizontal: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  checkIn: {
    fontSize: moderateScale(14),
    fontFamily: fontFamily.semiBold,
  },
  sectionTitle: {
    fontSize: moderateScale(14),
    // fontWeight: 'bold',
    color: '#333333',
    marginBottom: 12,
    fontFamily: fontFamily.semiBold,
  },
  badge: { fontSize: moderateScale(11),
     backgroundColor: '#EBF2FF', 
     borderColor: '#5C90FF', 
     color: '#5C90FF',
      borderRadius: moderateScale(12),
       paddingLeft: moderateScale(12),
        paddingRight: moderateScale(12) 
      },
  entryContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  avatar: {
    width: moderateScale(50),
    height: moderateScale(50),
    borderRadius: moderateScale(25),
    marginRight: 12,
  },
  details: {
    flex: 1,
  },
  chip: {
    // backgroundColor: '#f0f0f0',
    borderRadius: 20,
    // paddingHorizontal: 12,
    // paddingVertical: 6,
    alignSelf: 'flex-start',
    // marginBottom: 8,
  },
  chipText: {
    fontSize: moderateScale(12),
    color: '#666666',
    fontFamily: fontFamily.semiBold,
    marginBottom: verticalScale(4),
  },
  addressText: {
    fontSize: moderateScale(11),
    color: '#444444',
    lineHeight: 18,
    fontFamily: fontFamily.regular,
  },
});

export default PositionDetailsComponent;
