import { View, StyleSheet, TouchableOpacity, ScrollView, Image, Platform } from 'react-native';
import React, { useState, useEffect, useRef, useMemo } from 'react';
import MapView, { <PERSON><PERSON>, <PERSON>yl<PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchSalesAreaRequest,
  resetSalesAreaState,
} from '../../../../State/Slices/SalesManagement/SalesAreaMapSlice';
import { ActivityIndicator, Button, Text, useTheme } from 'react-native-paper';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { DatePickerModal } from 'react-native-paper-dates';
import { moderateScale } from 'react-native-size-matters';
import EmptyorErrorComponent from '../../../../Components/EmptyOrError/EmptyOrErrorComponent';
import PositionDetailsComponent from './Components/RenderPositionDetails'; // Import the new component
import { RootState } from '../../../../State/Store';
import moment from 'moment';

import { styles } from './SaleAreaMapStyles';
import { Calendar } from 'lucide-react-native';

const SalesAreaMapScreen: React.FC = () => {
  const dispatch = useDispatch();

  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedMarker, setSelectedMarker] = useState(null);
  const theme = useTheme();

  const peopleHirerachySelectedData = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedPeopleHirerachy?.data
  );

  const restrictPeopleHirerachyData = useSelector(
    (state: any) => state.GlobalAppStateData?.restrictPeopleHirerachyData
  );
  const userId = useSelector((state: any) => state.userId);

  const selectedUserId = peopleHirerachySelectedData?.account_user_id || '';

  const isRestricted = useMemo(() => {
    return restrictPeopleHirerachyData?.data?.includes(selectedUserId);
  }, [selectedUserId, restrictPeopleHirerachyData?.data]);

  const mapRef = useRef<MapView>(null);
  const timezone = useSelector((state: RootState) => state.selectedTimezone.timezone);

  const formatTime = (time: string) => {
    return time && timezone && moment(time).isValid()
      ? moment.utc(time).tz(timezone).format('hh:mm A - DD/MM/YYYY')
      : 'N/A';
  };
  const formatDate = (date: string | DateTimePickerModalate) => {
    return date && timezone && moment(date).isValid()
      ? moment.utc(date).tz(timezone).format('DD/MM/YYYY')
      : 'N/A';
  };

  useEffect(() => {
    return () => {
      dispatch(resetSalesAreaState());
    };
  }, []);

  const handleSalesAreaData = () => {
    const formattedDate = selectedDate.toISOString().split('T')[0];
    dispatch(
      fetchSalesAreaRequest({
        date: formattedDate,
        userId: peopleHirerachySelectedData?.account_user_id,
      })
    );
  };

  useEffect(() => {
    const formattedDate = selectedDate.toLocaleDateString('en-CA');
    
    // if (peopleHirerachySelectedData?.account_user_id) {
      dispatch(
        fetchSalesAreaRequest({
          date: formattedDate,
          userId: peopleHirerachySelectedData?.account_user_id ,
        })
      );
    // }
  }, [selectedDate, peopleHirerachySelectedData?.account_user_id]);

  const { salesAreaData, loading, error } = useSelector((state: any) => state.salesMapArea);
console.log("sales area data", salesAreaData,loading,error);
  const handleCalendarPress = () => {
    setShowDatePicker(true);
  };

  useEffect(() => {
    if (salesAreaData && salesAreaData.length > 0 && !selectedMarker) {
      setSelectedMarker(salesAreaData[0]);
    }
  }, [salesAreaData]);

  const handleMarkerPress = (marker: any) => {
    setSelectedMarker(marker);
  };

  const onDateConfirm = (date: any) => {
    // console.log("date data", date)
    setShowDatePicker(false);
    if (date) {
      setSelectedDate(date);
      setSelectedMarker(null);
    }
  };

  return (
    <View style={styles.container}>
      {!isRestricted ? (
        <View style={styles.calendarButtonContainer}>
          <TouchableOpacity
            onPress={() => handleCalendarPress()}
            style={styles.calendarButton}
            activeOpacity={0.8}
          >
            <Calendar size={moderateScale(14)} color="#ffffff" style={styles.calendarIcon} />
            <Text style={styles.calendarText}>
              {formatDate(selectedDate)}
              {/* {selectedDate.toLocaleDateString()} */}
            </Text>
          </TouchableOpacity>
        </View>
      ) : null}

      {error ? (
        <EmptyorErrorComponent
          message="Unable to Load Data."
          handleRefresh={() => handleSalesAreaData()}
        />
      ) : isRestricted ? (
        <EmptyorErrorComponent message="You don't have access to view data for this user. Only users at the same or lower hierarchy can be viewed." />
      ) : loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size={moderateScale(27)} color={theme.colors.primary} />
          <Text variant="bodyMedium" style={styles.loadingText}>
            Loading sales area data...
          </Text>
        </View>
      ) : !salesAreaData || salesAreaData.length <= 0 ? (
        <EmptyorErrorComponent message="There are no sales activities recorded for the selected date. Try selecting a different date." />
      ) : (
        <>
          <View style={styles.contentContainer}>
            <View style={styles.mapContainer}>
              {salesAreaData && salesAreaData.length > 0 && (
                <MapView
                  ref={mapRef}
                  // customMapStyle={lightMapStyle}
                  userInterfaceStyle="light"
                  mapType={'standard'}
                  initialRegion={{
                    latitude: parseFloat(salesAreaData[0].checkin_latitude),
                    longitude: parseFloat(salesAreaData[0].checkin_longitude),
                    latitudeDelta: 0.01,
                    longitudeDelta: 0.01,
                  }}
                  style={styles.map}
                  onMapReady={() => {
                    setTimeout(() => {
                      const coords = salesAreaData
                        .map((item: any) => ({
                          latitude: parseFloat(item.checkin_latitude ?? ''),
                          longitude: parseFloat(item.checkin_longitude ?? ''),
                        }))
                        .filter((c: any) => !isNaN(c.latitude) && !isNaN(c.longitude));

                      if (mapRef.current && coords.length) {
                        mapRef.current.fitToCoordinates(coords, {
                          edgePadding: { top: 10, right: 10, bottom: 10, left: 10 },
                          animated: true,
                        });
                      }
                    }, 100);
                  }}
                >
                  {salesAreaData.map((item: any, index: number) => (
                    <Marker
                      key={item.checkin_checkout_logs_id}
                      coordinate={{
                        latitude: parseFloat(item.checkin_latitude),
                        longitude: parseFloat(item.checkin_longitude),
                      }}
                      title={`${index + 1}. ${item.checkin_area}`}
                      description={item.checkin_address}
                      onPress={() => handleMarkerPress(item)}
                    >
                      <View style={[styles.markerContainer]}>
                        <Text style={styles.markerText}>{index + 1}</Text>
                      </View>
                    </Marker>
                  ))}

                  {salesAreaData.length >= 2 && (
                    <Polyline
                      coordinates={salesAreaData.map((item: any) => ({
                        latitude: parseFloat(item.checkin_latitude),
                        longitude: parseFloat(item.checkin_longitude),
                      }))}
                      strokeColor="#3377FF"
                      strokeWidth={3}
                    />
                  )}
                </MapView>
              )}
            </View>

            <PositionDetailsComponent selectedMarker={selectedMarker} formatTime={formatTime} />
          </View>
        </>
      )}

      {/* <DatePickerModal
        locale="en-CA"
        mode="multiple"
        visible={showDatePicker}
        onDismiss={() => setShowDatePicker(false)}
        date={selectedDate}
        onConfirm={onDateConfirm}
      /> */}

      <DateTimePickerModal
        isVisible={showDatePicker}
        mode="date"
        onConfirm={onDateConfirm}
        onCancel={() => setShowDatePicker(false)}
        date={selectedDate}
        // minimumDate={activeField === 'start' ? fiveYearsAgo : startDate}
        // maximumDate={activeField === 'end' ? startDate : undefined}
        accentColor="#3377ff"
        themeVariant="light"
        // textColor="#3377ff"
        locale="en-IN"
        // display={Platform.select({ ios: 'spinner', android: 'default' })}
        pickerContainerStyleIOS={
          Platform.OS === 'ios'
            ? {
                // alignSelf: 'center',
                // justifyContent: 'center',
                // backgroundColor: '#fff',
                // borderRadius: 16,
                // padding: 10,
                // width: '90%',
              }
            : undefined
        }
      />
    </View>
  );
};

export default SalesAreaMapScreen;