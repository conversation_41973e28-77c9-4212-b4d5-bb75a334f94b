import {TouchableOpacity, View } from 'react-native'
import {Text} from "react-native-paper"
import React from 'react'
import styles from "../DSRDetailViewStyles"
import moment from 'moment-timezone'  
import { getLeadStateChipStyleWeb,getLeadStateChipTextStyleWeb } from '../DSRDetailViewUtils'
import { Clock,MapPin,Building2 } from 'lucide-react-native'
import { useSelector } from 'react-redux'
import { RootState } from '../../../../../State/Store'
const DSRDetailsCard = ({activity,navigation}:any) => {
    // console.log("acitivty",activity)
    const organizationName =
      activity?.organization_name && activity?.organization_name?.length > 30
        ? activity?.organization_name?.slice(0, 30) + '...'
        : activity?.organization_name || '';
    const timezone = useSelector((state: RootState) => state.selectedTimezone.timezone);
    console.log("timezone",timezone)
    const onPressOrgName = () => {
      const leadDetails = {
        company: activity.organization_name,
        contactName: '',
        category: '',
        location: activity.area_name ?? '',
        type: activity.sales_activity_type_name ?? '',
        lead: {
          email: '',
          leadStateName: activity.lead_state_name,
          prospectTypeName: activity.prospect_type_name,
          contactName: '',
          orgId: activity.org_id,
        },
      };

      navigation.navigate('ProspectDetails', {
        leadId: activity.org_id,
        leadDetails,
        showThreeTabs:true,
      });
    };
  return (
    <View style={styles.dsrCardContainer}>
      <View style={styles.activityItemHeader}>
        <View style={styles.activityHeaderLeft}>
          <View
            style={
              activity?.sales_activity_state_name?.toUpperCase() === 'COMPLETED'
                ? styles.circleCompleted
                : styles.circleIncomplete
            }
          >
            {activity?.sales_activity_state_name?.toUpperCase() === 'COMPLETED' && (
              <Text style={styles.checkmark}>✓</Text>
            )}
          </View>
          <Text style={styles.companyName} variant={'medium'}>
            {activity?.sales_activity_title}
          </Text>
        </View>
        <Text style={styles.timeAgo}>
          {activity?.start_date_time ? moment(activity?.start_date_time).fromNow() : 'N/A'}
        </Text>
      </View>
      {organizationName && (
        <TouchableOpacity onPress={onPressOrgName} style={styles.companyLinkContainer}>
          <View style={styles.companyLinkInner}>
            <Building2 size={14} color="#2563EB" style={{ marginRight: 6 }} />
            <Text numberOfLines={1} style={styles.companyLinkText}>
              {organizationName}
            </Text>
          </View>
        </TouchableOpacity>
      )}

      <View style={styles.activityDetails}>
        {activity?.lead_state_name && (
          <View style={styles.categoryRow}>
            <View
              style={[styles.categoryBadge, getLeadStateChipStyleWeb(activity?.lead_state_name)]}
            >
              <Text
                style={[
                  styles.categoryText,
                  getLeadStateChipTextStyleWeb(activity?.lead_state_name),
                ]}
              >
                {activity?.lead_state_name}
              </Text>
            </View>
            {activity?.prospect_type_name && (
              <View style={styles.activityTypeView}>
                <Text style={styles.activityType}>{activity?.prospect_type_name}</Text>
              </View>
            )}

            {activity?.isOnline && <Text style={styles.onlineTag}>Online</Text>}

            {!activity?.isOnline && activity?.type === 'In person' && (
              <Text style={styles.locationTag}>In person</Text>
            )}
          </View>
        )}
        {activity?.area_name && (
          <View style={styles.locationContainer}>
            <View style={styles.infoRow}>
              <MapPin size={16} />
              <Text style={styles.locationText}>{activity?.area_name}</Text>
            </View>
          </View>
        )}

        <View style={styles.infoRow}>
          <Clock size={16} />
          <Text style={styles.timeText}>
            {activity?.start_date_time
              ? moment(activity.start_date_time)
                  .tz(timezone || 'UTC')
                  .format('hh:mm A')
              : 'N/A'}{' '}
            -{' '}
            {activity?.end_date_time
              ? moment(activity.end_date_time)
                  .tz(timezone || 'UTC')
                  .format('hh:mm A')
              : 'N/A'}
          </Text>
        </View>

        {activity?.tags && activity?.tags.length > 0 && (
          <View style={styles.tagsContainer}>
            {activity?.tags.map((tag: string, index: number) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>
        )}
      </View>
    </View>
  );
}

export default DSRDetailsCard;
