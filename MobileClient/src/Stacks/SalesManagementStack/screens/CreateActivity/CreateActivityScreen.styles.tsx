import {StyleSheet} from 'react-native';
import { verticalScale, scale, moderateScale } from 'react-native-size-matters';
export default StyleSheet.create({
  radioGroup: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: verticalScale(10),
  },
  scrollContent: {
    paddingHorizontal: scale(16),
  },
  suggestionContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginTop: 4,
    elevation: 5,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 },
    zIndex: 1000,
    height: 200,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 80,
    paddingTop: 0,
  },

  row: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    // marginBottom: 8,
  },
  chip: {
    marginRight: scale(5),
    marginBottom: verticalScale(5),
    borderColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'space-around',
    borderWidth: 2,
    backgroundColor: 'rgba(240, 247, 255, 1)',
  },
  input: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    height: 48,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ccc',
    backgroundColor: 'white',
    marginBottom: 12,
    color: '#000',
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    height: 48,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ccc',
    backgroundColor: 'white',
    marginBottom: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 8,
    minWidth: 220,
    maxHeight: 320,
  },
  modalHeader: {
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingBottom: 8,
    marginBottom: 8,
  },
  modalTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center',
  },
  modalItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
  successSnackbar: {
    backgroundColor: '#4CAF50',
    color: '#fff',
  },
  errorSnackbar: {
    backgroundColor: '#F44336',
  },
  dateText: {
    // color: 'rgba(109, 121, 142, 1)',
    color: '#000',
    fontSize: moderateScale(12),
    // fontFamily: fontFamily.semiBold,
  },
  chip_date: {
    backgroundColor: 'rgba(255, 255, 255, 1)',
    borderColor: 'rgba(203, 213, 225, 1)',
    alignItems: 'center',
    justifyContent: 'center',
    // height: verticalScale(31),
    height: verticalScale(28),
    borderRadius: 4,
    marginBottom: verticalScale(5),
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    // padding: 16,
    gap: scale(10),
    marginTop : moderateScale(10),
    marginBottom : moderateScale(20),
    // marginBottom: Platform.OS === 'ios' ? verticalScale(10) : 0,
    // backgroundColor : 'red'
    // marginTop:verticalScale(10)
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginRight: moderateScale(20),
    // backgroundColor:"red",
    gap: scale(10),
    width:"35%",
    marginBottom:verticalScale(5)
  },
  outerCircle: {
    width: moderateScale(16),
    height: moderateScale(16),
    borderRadius: 30,
    borderWidth: moderateScale(2),
    borderColor: '#3377ff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  innerCircle: {
    width: moderateScale(8),
    height: moderateScale(8),
    borderRadius: 30,
    backgroundColor: '#3377FF',
  },
  label: {
    // marginLeft: 8,
    // fontSize: moderateScale(14),
    // color: '#000',
    fontSize: moderateScale(12),
    marginVertical: verticalScale(5),
    color: '#1A1A1A',
  },
  radioContainer:{
    borderWidth:0,
    backgroundColor:"",
    paddingHorizontal:scale(2.5),
    marginHorizontal:0,
    marginBottom:0,
    paddingVertical:0,
    paddingTop:verticalScale(5),
  }
});