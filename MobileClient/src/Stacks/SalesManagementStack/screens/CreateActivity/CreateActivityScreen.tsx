import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  KeyboardAvoidingView,
  Keyboard,
  TouchableWithoutFeedback,
  FlatList,
  TextInput as InputFeild,
  BackHandler,
  SafeAreaView,
  Modal,
  Alert
} from 'react-native';
import {
  TextInput,
  Button,
  Text,
  Chip,
  useTheme,
  HelperText,
  ActivityIndicator,
  Snackbar,
  Card,
} from 'react-native-paper';
import { ScrollView } from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { v4 as uuidv4 } from 'uuid';
import ApiClient from '../../../../Common/API/ApiClient';
import { DSREndPoints } from '../../../../Common/API/ApiEndpoints';
import { handleApiResponse } from '../../../../Common/API/ApiResponseHandler';
import { getUserId } from '../../../../Common/Utils/Storage';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import AppHeader from '../../../../Components/AppHeader';
import { fontFamily } from '../../../../Common/Theme/typography';
import moment from 'moment';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { DatePickerModal } from 'react-native-paper-dates';
import { DropdownField } from '../../../../Components/UI/Menu/DropdownModal';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { DropdownMulti } from '../../../../Components/UI/Menu/Dropdown';
import { CalendarDate } from 'react-native-paper-dates/lib/typescript/Date/Calendar';
import { encryptPayload } from '../../../../Utils/payloadEncryption';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import CustomAlert from '../../../../Components/CustomAlert/CustomAlert';
import { Building2 } from 'lucide-react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../../../../State/Store';
import { convertToTimeZoneUTC } from '../../../../Utils';
import { select } from 'redux-saga/effects';
import CustomInput from '../../../../Components/UI/TextInput';
import DateTimePicker from '@react-native-community/datetimepicker';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { Calendar } from 'react-native-calendars';
import { CustomStringDropdown } from '../../../../Components/UI/Menu/SearchDropdown';
import { debounce } from 'lodash';
import { showToast } from '../../../../Components/AppToaster/AppToaster';
dayjs.extend(customParseFormat);
interface ObjectType {
  Text: string;
  Value: string;
}

export interface CustomerType {
  contact_id: string;
  contact_name: string;
  email: string;
}
export interface InternalUserType {
  account_user_id: string;
  first_name: string;
  email: string;
}

const IconComponent = MaterialCommunityIcons as unknown as React.ComponentType<any>;
const CreateActivityScreen = ({ navigation, route }: any) => {
  const { handleRefresh } = route?.params;
  const [activityType, setActivityType] = useState('in_person');
  const [activityTypes, setActivityTypes] = useState([]);
  const [title, setTitle] = useState('');
  const [startDate, setStartDate] = useState<Date | CalendarDate>(new Date());
  const [endDate, setEndDate] = useState<Date | CalendarDate>(new Date());
  const [showPicker, setShowPicker] = useState(false);
  const [activeField, setActiveField] = useState<'start' | 'end'>('start');
  const [isStartDateSelected, setIsStartDateSelected] = useState(false);
  const [startTime, setStartTime] = useState<string>('9:00 AM');
  const [endTime, setEndTime] = useState<string>('10:00 AM');
  const [priority, setPriority] = useState<ObjectType | null>(null);
  const [status, setStatus] = useState<ObjectType | null>(null);
  const [estimatedTime, setEstimatedTime] = useState<ObjectType | null>(null);
  const [tags, setTags] = useState<string[]>(['Meeting']);
  const [tagInput, setTagInput] = useState('');
  const [areas, setAreas] = useState([]);
  const [statuses, setStatuses] = useState([]);
  const [priorities, setPriorities] = useState([]);
  const [estimatedTimes, setEstimatedTimes] = useState([]);
  const [externalContacts, setExternalContacts] = useState([]);
  const [internalUsers, setInternalUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [area, setArea] = useState<ObjectType | null>(null);
  const [customerConnect, setCustomerConnect] = useState<CustomerType[]>([]);
  const [assignedTo, setAssignedTo] = useState<InternalUserType[]>([]);
  const [timeSlots, setTimeSlots] = useState<ObjectType[]>([]);
  const [endTimeSlots, setEndTimeSlots] = useState<ObjectType[]>([]);
  const [showWarning, setShowWarning] = useState<boolean>(false);
  const [pendingDate, setPendingDate] = useState<CalendarDate | null>(null);
  // Form validation
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarType, setSnackbarType] = useState<'success' | 'error'>('success');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isFocused, setIsFocused] = useState(false);
  const [asyncStorageValues, setAsyncStorageValues] = useState({
    schemaName: '',
    accountId: '',
    controlUnitId: '',
    workspaceId: '',
    userId: '',
    isLoaded: false,
  });
  const [activityTypeId, setActivityTypeId] = useState<string>('1');
  const [showAlert, setShowAlert] = useState<boolean>(false);
  const [leads, setLeads] = useState([]);
  const [selectedLead, setSelectedLead] = useState(null);
  // console.log('selectedLead', selectedLead);
  const [searchText, setSearchText] = useState('');
  const [pageNumber, setPageNumber] = useState(1);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const [radioValue, setRadioValue] = useState('External');
  const timezone = useSelector((state: RootState) => state.selectedTimezone.timezone);
    const selectedControlUnit = useSelector(
      (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data?.id
    );
  const userDetails = useSelector(
      (state: RootState) => state.UserInfo.userDetailsInRedux,
    );
    const {bottom} = useSafeAreaInsets();

    const [input, setInput] = useState('');
    const [showOptions,setShowOptions] = useState(false)
      const theme = useTheme();
      const onSelectHandler = (item: string) => {
                const isSelected = tags.includes(item);
                if (isSelected) {
                  setTags(tags.filter((tag) => tag !== item));
                } else {
                  setTags([item, ...tags]);
                }
              }
    
      const debouncedSearch = useMemo(() => {
        return debounce((text: string) => {
          // debounced external search, if needed
        }, 300);
      }, []);
    
      useEffect(() => {
        return () => {
          debouncedSearch.cancel();
        };
      }, []);
    
      const handleAdd = () => {
        if (input.trim()) {
          onSelectHandler(input.trim());
          setInput('');
          // onSelectClose?.();
        }
      };
        const filteredData =
          input.trim().length === 0
            ? suggestions
            : suggestions.filter((item: string) =>
                item.toLowerCase().includes(input.trim().toLowerCase())
              );
    
      const showCreateOption =
        input.trim().length > 0 && !suggestions.includes(input.trim());



  useEffect(() => {
    fetchExternalContacts();
  }, [selectedLead]);
  const fetchExternalContacts = async () => {
    const { schemaName, workspaceId, accountId, controlUnitId } = asyncStorageValues;
    if (!selectedLead) return;
    try {
      const response = await ApiClient.post(DSREndPoints.GET_EXTERNAL_CONTACTS, {
        schemaName,
        orgId: selectedLead?.orgId || '',
        sendOrgId: selectedLead?.orgId || '',
      });
      // Handle the response as needed
      console.log('API Response:', response.data.data);
      setExternalContacts(response.data.data);
      setCustomerConnect([]);
      return response;
    } catch (error) {
      // Handle error as needed
      console.log('API Error:', error);
      throw error;
    }
  };
  const formatTime = (time: string) => {
    return time && timezone && moment(time).isValid()
      ? moment.utc(time).tz(timezone).format('hh:mm A')
      : 'N/A';
  };
  // Helper function to get string value from AsyncStorage
  const getStringValueFromAsyncStorage = (value: any) => {
    if (value === null || value === undefined) return '';

    if (typeof value === 'object' && value !== null) {
      if (typeof value.toString === 'function') {
        return value.toString();
      }

      try {
        return JSON.stringify(value);
      } catch (e) {
        console.log('Error stringifying AsyncStorage value:', e);
        return '';
      }
    }

    return String(value);
  };
  const parseWorkspaceFromAsyncStorage = (workspaceData: string | null) => {
    if (!workspaceData) return null;

    try {
      return JSON.parse(workspaceData);
    } catch (e) {
      console.log('Error parsing workspace data:', e);
      return null;
    }
  };

  const showSnackbar = (message: string, type: 'success' | 'error' = 'success') => {
    setSnackbarMessage(message);
    setSnackbarType(type);
    setSnackbarVisible(true);
  };
  const generateTimeSlots = (startFrom?: Date): ObjectType[] => {
    const slots: ObjectType[] = [];
    const start = startFrom ? new Date(startFrom) : new Date();
    if (!startFrom) start.setHours(8, 0, 0, 0);
    const end = new Date(start);
    end.setHours(22, 45, 0, 0);
    let valueCounter = 1;

    while (start <= end) {
      const hours = start.getHours();
      const minutes = start.getMinutes();
      const suffix = hours >= 12 ? 'PM' : 'AM';
      const formattedHours = ((hours + 11) % 12) + 1;
      const formattedTime = `${formattedHours}:${minutes.toString().padStart(2, '0')} ${suffix}`;

      slots.push({
        Text: formattedTime,
        Value: String(valueCounter++),
      });

      start.setMinutes(start.getMinutes() + 15);
    }

    return slots;
  };
  // Load async storage values
  useEffect(() => {
    const getAsyncStorageValues = async () => {
      try {
        const [schemaName, storedAccountId, workspaceData, accountCode, userId] = await Promise.all(
          [
            AsyncStorage.getItem('schemaName'),
            AsyncStorage.getItem('accountId'),
            AsyncStorage.getItem('selectedWorkspace'),
            AsyncStorage.getItem('accountCode'),
            getUserId(),
          ]
        );
        const processedSchemaName = getStringValueFromAsyncStorage(schemaName);
        const processedControlUnitId = getStringValueFromAsyncStorage(accountCode);

        const workspaceObj = parseWorkspaceFromAsyncStorage(workspaceData);

        const workspaceId = workspaceObj?.id || '';
        const accountId =
          workspaceObj?.accountId || getStringValueFromAsyncStorage(storedAccountId);

        setAsyncStorageValues({
          schemaName: processedSchemaName,
          accountId: accountId,
          controlUnitId: selectedControlUnit,
          workspaceId: workspaceId,
          userId: userId || '',
          isLoaded: true,
        });

        const slots = generateTimeSlots();
        setTimeSlots(slots);
      } catch (error) {
        console.log('Error retrieving data from AsyncStorage:', error);
        showSnackbar('Failed to load user data', 'error');
      }
    };

    getAsyncStorageValues();
  }, [selectedControlUnit]);

  useEffect(() => {
    const keyboardHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setIsFocused(false);
    });
    return () => {
      keyboardHideListener.remove();
    };
  }, []);

  const fetchLeadsList = async ({
    pageNumber = 1,
    searchText = '',
  }: {
    pageNumber: number;
    searchText: string;
  }) => {
    try {
      const { schemaName, workspaceId, accountId, controlUnitId } = asyncStorageValues;

      const payload = {
        schemaName,
        accountId,
        workspaceId,
        controlUnitId,
        leadsIds: [],
        searchText,
        pageNumber,
        limit: 10,
      };

      const encryptedPayload = await encryptPayload(payload);
      const response = await ApiClient.post(DSREndPoints.CRM_LEADS, encryptedPayload);

      if (response.data?.success) {
        const result = response.data.data || [];

        if (pageNumber === 1) {
          setLeads(result);
        } else {
          setLeads((prev) => [...prev, ...result]);
        }

        setHasMoreData(result.length === 10);
      } else {
        if (pageNumber === 1) setLeads([]);
        setHasMoreData(false);
      }
    } catch (error) {
      console.log('Error fetching leads list:', error);
      if (pageNumber === 1) setLeads([]);
      setHasMoreData(false);
    }
  };

  const handleLoadMore = () => {
    if (isFetchingMore || !hasMoreData) return;

    const nextPage = pageNumber + 1;
    setIsFetchingMore(true);
    setPageNumber(nextPage);
    fetchLeadsList({ pageNumber: nextPage, searchText }).finally(() => setIsFetchingMore(false));
  };

  useEffect(() => {
    const fetchOptions = async () => {
      const allTags = [
        'Work',
        'Personal',
        'Urgent',
        'Low Priority',
        'Follow-up',
        'Sales',
        'Client Meeting',
        'Important',
      ];
      setLoading(true);
      try {
        // Use dynamic values from AsyncStorage instead of hardcoded values
        const { schemaName, controlUnitId, userId } = asyncStorageValues;
        if (!schemaName) {
          console.log("Schema name not available yet, will try again when it's loaded");
          return;
        }
        const [
          activityTypeRes,
          contactsRes,
          areasRes,
          statusRes,
          priorityRes,
          estTimeRes,
          internalUsersRes,
        ] = await Promise.all([
          // Use ApiClient with dynamic values
          ApiClient.get(
            `${DSREndPoints.GET_MASTERS_DROPDOWN}/${schemaName}/crm_sales_activity_type?columns=sales_activity_type_id,sales_activity_type_name`
          ),
          ApiClient.post(DSREndPoints.GET_EXTERNAL_CONTACTS, {
            schemaName,

            orgId: route.params.leadDetails?.lead.orgId || route.params.selectedEvent?.org_id || '',
            sendOrgId:
              route.params.leadDetails?.lead.orgId || route.params.selectedEvent?.org_id || '',
          }),
          ApiClient.get(
            `${DSREndPoints.GET_MASTERS_DROPDOWN}/${schemaName}/crm_area?columns=area_id,area_name`
          ),
          ApiClient.get(
            `${DSREndPoints.GET_MASTERS_DROPDOWN}/${schemaName}/crm_sales_activity_states?columns=sales_activity_state_id,%20sales_activity_state_name`
          ),
          ApiClient.get(
            `${DSREndPoints.GET_MASTERS_DROPDOWN}/${schemaName}/crm_sales_activity_priority?columns=sales_activity_priority_id,sales_activity_priority_name`
          ),
          ApiClient.get(
            `${DSREndPoints.GET_MASTERS_DROPDOWN}/${schemaName}/crm_sales_activity_estimated_time?columns=sales_activity_estimated_time_id,sales_activity_estimated_time_name`
          ),
          // Fix the internal_users endpoint by using external contacts again temporarily
          ApiClient.post(DSREndPoints.SUBORDINATES, {
            schemaName,
            userId: asyncStorageValues.userId,
          }),
        ]);

        // Process responses with handleApiResponse
        const activityTypeData = handleApiResponse(activityTypeRes);
        const contactsData = contactsRes?.data;
        const areasData = handleApiResponse(areasRes);
        const statusesData = handleApiResponse(statusRes);
        const prioritiesData = handleApiResponse(priorityRes);
        const estimatedTimesData = handleApiResponse(estTimeRes);
        const internalUsersData = handleApiResponse(internalUsersRes);
        const filterInternalUsers = internalUsersData.data.filter(
          (user: InternalUserType) =>
            user.account_user_id !== null && user.first_name !== null && user.email !== null
        );
        setActivityTypes(activityTypeData || []);
        setExternalContacts(contactsData?.data || []);
        setAreas(areasData || []);
        setStatuses(statusesData || []);
        setPriorities(prioritiesData || []);
          setEstimatedTimes(
  (estimatedTimesData || []).filter(
    (item: any) => item.Text !== "1:30 Hours"
  )
);
        setInternalUsers(filterInternalUsers || []);
        if (route.params.selectedEvent) {
          const startDateTime = extractDateTime(route.params.selectedEvent?.start_date_time);
          startDateTime?.date && setStartDate(new Date(startDateTime.date));
          const formatedStartTime = formatTime(route.params.selectedEvent?.start_date_time);
          setStartTime(formatedStartTime);
          const parsedStart = dayjs(formatedStartTime, 'h:mm A');
          const getEndTime = parsedStart.add(15, 'minute');
          const slots = generateTimeSlots(getEndTime.toDate());
          setEndTimeSlots(slots);
          const endDateTime = extractDateTime(route.params.selectedEvent?.end_date_time);
          endDateTime?.date && setEndDate(() => new Date(endDateTime.date));
          setEndTime(formatTime(route.params.selectedEvent?.end_date_time));
          setActivityType(route.params.selectedEvent?.sales_activity_type_name);
          setTitle(route.params.selectedEvent?.sales_activity_title);
          setActivityTypeId(route.params.selectedEvent?.sales_activity_type_id);
          setArea({
            Text: route.params.selectedEvent?.area_name,
            Value: route.params.selectedEvent?.area_id,
          });
          setPriority({
            Text: route.params.selectedEvent?.sales_activity_priority_name,
            Value: route.params.selectedEvent?.sales_activity_priority_id,
          });
          setEstimatedTime({
            Text: route.params.selectedEvent?.sales_activity_estimated_time_name,
            Value: route.params.selectedEvent?.sales_activity_estimated_time_id,
          });
          setStatus({
            Text: route.params.selectedEvent?.sales_activity_state_name,
            Value: route.params.selectedEvent?.sales_activity_state_id,
          });
          const selectedList = route.params.selectedEvent?.assigned_users;
          const allList = filterInternalUsers;
          const filteredUsers = allList.filter((user: InternalUserType) =>
            selectedList.some(
              (selected: InternalUserType) => selected.account_user_id === user.account_user_id
            )
          );
          setAssignedTo(filteredUsers);
          setTags(route.params.selectedEvent?.tags);
          const newTags = route.params.selectedEvent?.tags?.filter(
            (tag: string) => !allTags.includes(tag)
          );
          const commonContacts = contactsData?.data?.filter((fc) =>
            route.params?.selectedEvent?.contact_profiles?.some(
              (ec) => ec.contact_id === fc.contact_id
            )
          );
          setCustomerConnect(commonContacts);
          setSuggestions([...newTags, ...allTags]);
        } else {
          setActivityType(activityTypeData[0]?.Text);
          setTitle('In-person Meeting');
          setPriority(prioritiesData[3]);
          setEstimatedTime(estimatedTimesData[1]);
          setStatus(statusesData[0]);
          setActivityTypeId('1');
          setAssignedTo(
            filterInternalUsers.filter((data: InternalUserType) => data.account_user_id === userId)
          );
          setSuggestions(allTags);
          const startFrom = new Date();
          startFrom.setHours(9, 15, 0, 0);
          const timeSlots = generateTimeSlots(startFrom);
          setEndTimeSlots(timeSlots);
        }
      } catch (e) {
        console.log('Dropdown fetch error', e);
        showSnackbar('Failed to load dropdown data', 'error');
      }
      setLoading(false);
    };

    // Only fetch options once asyncStorageValues are loaded
    if (asyncStorageValues.isLoaded) {
      fetchOptions();
      if (!route.params?.leadDetails) {
        fetchLeadsList({ pageNumber: 1, searchText: '' });
      }
    }
  }, [asyncStorageValues]);

  const onAddTag = () => {
    if (tagInput && !tags.includes(tagInput)) {
      setTags([...tags, tagInput]);
      setSuggestions([tagInput, ...suggestions]);
      setTagInput('');
    }
  };

  function extractDateTime(isoString: Date) {
    const date = new Date(isoString);

    // Format date as YYYY-MM-DD
    const formattedDate = date.toISOString().split('T')[0];

    // Format time as HH:MM AM/PM
    let hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    const formattedMinutes = minutes.toString().padStart(2, '0');
    const formattedTime = `${hours}:${formattedMinutes} ${ampm}`;

    return {
      date: formattedDate,
      time: formattedTime,
    };
  }

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    const isExternal = route.params?.handleRefresh && radioValue !== 'Internal';
    const hasOrgName = !!route.params?.leadDetails?.lead?.organizationName;
    if (isExternal && !selectedLead) {
      newErrors.lead = 'Please select an organization';
    }
    if (!title) newErrors.title = 'Activity title is required';
    if (!startTime) newErrors.startTime = 'Start time is required';
    if (!endTime) newErrors.endTime = 'End time is required';

    if (activityType === 'Face to Face' && (isExternal || hasOrgName)) {
      if (!area || !area.Value) {
        newErrors.area = 'Area is required';
      }
    }
    if (!status?.Value) newErrors.status = 'Status is required';
    if (!priority?.Value) newErrors.priority = 'Priority is required';
    if (!estimatedTime?.Value) newErrors.estimatedTime = 'Estimated time is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Helper function to convert time string like "8:00 AM" to Date object
  const timeStringToDate = (timeStr: string, baseDate: Date | CalendarDate) => {
    const date = baseDate ? new Date(baseDate) : new Date();
    const [time, period] = timeStr.split(' ');
    let [hours, minutes] = time.split(':').map(Number);

    if (period === 'PM' && hours !== 12) {
      hours += 12;
    } else if (period === 'AM' && hours === 12) {
      hours = 0;
    }

    date.setHours(hours, minutes, 0, 0);
    return date;
  };

  const handleCreateActivity = async () => {
    setIsSubmitting(true);
    setErrors({});

    if (!validateForm()) {
      setIsSubmitting(false);
      return;
    }

    try {
      let startDateTime = timeStringToDate(startTime, startDate);
      let endDateTime = timeStringToDate(endTime, endDate);
      const { schemaName, accountId, controlUnitId, workspaceId, userId } = asyncStorageValues;

      if (!schemaName || !accountId || !workspaceId || !userId) {
        throw new Error('Missing required data. Please try again.');
      }

      const isExternal = radioValue === 'External' && selectedLead;
      const leadData = route.params.leadDetails?.lead;
      console.log('leadData ???', leadData);
      const event = route.params.selectedEvent;
      startDateTime = convertToTimeZoneUTC(startDateTime, timezone);
      endDateTime = convertToTimeZoneUTC(endDateTime, timezone);

      // console.log("convertoUtc time",startDateTime)
      console.log('startDateTime', startDateTime);
      const payload = {
        workspace_id: isExternal ? selectedLead.workspaceId : workspaceId,
        control_unit_id: isExternal
          ? selectedLead.controlUnitId
          : route.params.controlUnitId || leadData?.controlUnitId || '',
        account_id: isExternal ? selectedLead.accountId : accountId,
        sales_activity_id: event?.sales_activity_id || uuidv4(),
        sales_activity_title: title,
        sales_activity_description: isExternal
          ? selectedLead.industryName
          : event?.sales_activity_description || route.params.leadDetails?.category || '',
        sales_activity_date: startDateTime.toISOString().split('T')[0],
        sales_activity_type_id: Number(activityTypeId),
        sales_activity_state_id: Number(status?.Value),
        sales_activity_priority_id: Number(priority?.Value),
        sales_activity_estimated_time_id: Number(estimatedTime?.Value),
        sales_activity_owner_id: leadData?.createdBy || userId,
        start_date_time: startDateTime.toISOString(),
        end_date_time: endDateTime.toISOString(),
        status_id: Number(status?.Value),
        tags: JSON.stringify(tags.length > 0 ? tags : ['Meeting']),
        external_users: JSON.stringify(
          customerConnect?.length > 0 ? customerConnect.map((c: CustomerType) => c.contact_id) : []
        ),
        internal_users: JSON.stringify(
          assignedTo?.length > 0
          ? assignedTo.map((u: InternalUserType) => u.account_user_id)
          : [userId]
        ),
        attachment: JSON.stringify(''),
        updated_by: userId,
        selectedTimeZone: timezone,
        orgName: route.params?.leadDetails?.lead?.organizationName ? route.params?.leadDetails?.lead?.organizationName : selectedLead?.organizationName || "",
        areaName: area?.Text || '',
        area_id : radioValue === 'Internal' ? '' : Number(area?.Value || ''),
        leadAttendees: JSON.stringify(
          assignedTo?.length > 0
            ? assignedTo.map((u: InternalUserType) => u.email)
            : [userId]
        ),
        loggedInUserEmail: userDetails?.email,
        sendActivityToextranalContacts: [],
      };
      if(radioValue === "External") {
        payload.organization_id = isExternal ? selectedLead.orgId : event?.org_id || leadData?.orgId || ''
      }
      const encryptedPayload = await encryptPayload(JSON.stringify(payload));

      if (event) {
        await ApiClient.put(
          DSREndPoints.UPDATE_SALES_ACTIVITY,
          JSON.stringify({ schemaName, ...encryptedPayload })
        );
        showToast.success('Activity edited successfully');
      } else {
        await ApiClient.post(
          DSREndPoints.CREATE_EVENT,
          JSON.stringify({ schemaName, ...encryptedPayload })
        );
        showToast.success('Activity Created successfully');
      }
      handleRefresh?.();

      // const apiCall = event
      //   ?
      //   : ApiClient.post(
      //       DSREndPoints.CREATE_EVENT,
      //       JSON.stringify({ schemaName, ...encryptedPayload })
      //     );
      // const response = await apiCall;
      // const data = response?.data;
      // console.log('data', data);

      // // if (data?.status === 201 || data?.status === 200) {
      // if(event){

      // }
      // if (data?.status === 'Event created in successfully, but calendar not connected') {
      //   showSnackbar('Activity created successfully', 'success');
      //   handleRefresh?.();
      // } else if (data?.message === 'Sales activity Edited successfully') {
      //   showSnackbar('Activity edited successfully', 'success');
      // } else {
      //   throw new Error(data?.message || 'Failed to create activity');
      // }

      setTimeout(() => {
        route.params?.refreshForActivityTabs && route.params?.refreshForActivityTabs();
        navigation.goBack();
      }, 1500);
    } catch (error) {
      console.log('Error creating activity:', error);
      showSnackbar('Failed to create activity', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteActivity = async () => {
    const { schemaName } = asyncStorageValues;
    const payload = {
      schemaName,
      salesActivityId: route.params.selectedEvent?.sales_activity_id,
    };
    const encrptedPayload = await encryptPayload(payload);
    const response = await ApiClient.delete(DSREndPoints.DELETE_ACTIVITY, {
      data: encrptedPayload,
    });
    if (response.data.status === 201 || response.data.status === 200 ) {
      showSnackbar('Activity deleted successfully', 'success');
      setTimeout(() => {
        route.params?.refreshForActivityTab && route.params?.refreshForActivityTabs();
        navigation.goBack();
      }, 1500);
    } else {
      throw new Error(response.data?.message || 'Failed to delete activity');
    }
  };

  if (loading) {
    return <ActivityIndicator style={{ flex: 1, alignSelf: 'center' }} />;
  }
  const formatted = (date: Date | number) => {
    return String(moment(date).format('MMM DD, YYYY').toUpperCase());
  };
  const onSelectStartTimer = (item: ObjectType) => {
    setStartTime(() => item.Text);
    const parsedStart = dayjs(item.Text, 'h:mm A');
    let updatedEnd: dayjs.Dayjs;
    if (String(activityTypeId) === '1') {
      updatedEnd = parsedStart.add(60, 'minute');
    } else if (String(activityTypeId) === '2') {
      updatedEnd = parsedStart.add(30, 'minute');
    } else if (String(activityTypeId) === '3') {
      updatedEnd = parsedStart.add(15, 'minute');
    } else {
      updatedEnd = parsedStart;
    }
    const getEndTime = parsedStart.add(15, 'minute');
    const time = updatedEnd.format('h:mm A');
    setEndTime(time);
    const filteredSlots = generateTimeSlots(getEndTime.toDate());
    setEndTimeSlots(filteredSlots);
  };
  const onSelectEndTimer = (item: ObjectType) => {
    setEndTime(item.Text);
  };

  const activityTypeHanlder = (type: ObjectType) => {
    setActivityType(type.Text);

    const title =
      type.Value === '1' ? 'In-person Meeting' : type.Value === '2' ? 'Online Meeting' : type.Text;

    setTitle(title);
    setActivityTypeId(type.Value);

    const parsedStart = dayjs(startTime, 'h:mm A');

    let updatedEnd: dayjs.Dayjs;

    if (type.Value === '1') {
      updatedEnd = parsedStart.add(60, 'minute');
    } else if (type.Value === '2') {
      updatedEnd = parsedStart.add(30, 'minute');
    } else if (type.Value === '3') {
      updatedEnd = parsedStart.add(15, 'minute');
    } else {
      updatedEnd = parsedStart;
    }

    const newEndTime = updatedEnd.format('h:mm A');

    setEndTime(newEndTime);
  };

  const onDateSelect = (item : any) => {
    console.log("item selected in date", item)
    const selected = dayjs(item);
    const today = dayjs().startOf('day');

    if (selected.isBefore(today)) {
      setPendingDate(item);
      Platform.OS === 'ios' ? Alert.alert(
  "Past Date Warning",
  "You are trying to schedule an event in the past. Are you sure you want to proceed?\n\nCreating events in the past can cause confusion. Make sure this is intentional.",
  [
    {
      text: "Cancel",
      style: "cancel",
      onPress: () => {
        setShowWarning(false);
        setPendingDate(null);
        // setShowPicker(false);
      },
    },
    {
      text: "Yes, Schedule in Past",
      style: "destructive", // (closest to your orange/yellow warning button)
      onPress: () => {
        console.log("pendingDate>>",pendingDate)
        applySelectedDate(item)

        setShowWarning(false);
        setPendingDate(null);
        setShowPicker(false);
      },
    },
  ]
) :  setShowWarning(true);
     
      return;
    }
    setShowPicker(false)
    applySelectedDate(item);
  };

  const applySelectedDate = (date: CalendarDate) => {
    if (activeField === 'end') {
      setEndDate(date);
    } else {
      setStartDate(date);
      setEndDate(date);
      setIsStartDateSelected(true);
    }
    setShowPicker(false);
  };

  const calendarIcon = () => (
    <IconComponent
      name="calendar-blank-outline"
      size={moderateScale(18)}
      color={'rgba(109, 121, 142, 1)'}
    />
  );

  const today = new Date();
  const fiveYearsAgo = new Date();
  fiveYearsAgo.setFullYear(today.getFullYear() - 5);
  const showAsterisk =
    route.params?.selectedEvent?.organization_name ||
    route.params?.leadDetails?.lead?.organizationName
      ? activityType === 'Face to Face'
      : route.params?.handleRefresh && radioValue !== 'Internal' && activityType === 'Face to Face';
  const shouldShowCustomerConnect =
    (route.params?.handleRefresh && radioValue !== 'Internal') ||
    !!route.params?.selectedEvent?.organization_name ||
    !!route.params?.leadDetails?.lead?.organizationName;
  return (
    <TouchableWithoutFeedback
      onPress={() => {
        Keyboard.dismiss();
      }}
    >
      <View style={{ flex: 1, backgroundColor: '#fff' }}>
        <AppHeader
          customHeaderStyles={{ backgroundColor: '#fff' }}
          title={route.params.selectedEvent ? 'Update Activity' : 'Create Activity'}
          rightElement={() =>
            route.params.selectedEvent ? (
              <TouchableOpacity onPress={() => setShowAlert(true)}>
                <MaterialCommunityIcons
                  name={'trash-can-outline'}
                  size={24}
                  color={'red'}
                  style={[{ marginRight: 12 }]}
                />
              </TouchableOpacity>
            ) : null
          }
        />
        {/* <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={{ flex: 1 }}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 0}
        > */}
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <ScrollView
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            bounces={false}
          >
            {/* <ScrollView
          contentContainerStyle={[styles.content]}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        > */}
            {route.params?.handleRefresh ? (
              <View>
                <View style={{ flexDirection: 'row' }}>
                  {['Internal', 'External'].map((item, index) => {
                    const isSelected = item === radioValue;
                    return (
                      <TouchableOpacity
                        key={index}
                        style={[styles.option]}
                        onPress={() => setRadioValue(item)}
                        activeOpacity={0.7}
                      >
                        <View
                          style={[
                            styles.outerCircle,
                            {
                              borderColor: isSelected ? '#3377FF' : '#E0E0E0',
                              backgroundColor: !isSelected ? '#FFFFFF' : '#F7FAFF',
                              borderWidth: isSelected ? 2 : 1,
                            },
                          ]}
                        >
                          {isSelected && <View style={styles.innerCircle} />}
                        </View>
                        <Text
                          style={[styles.label, { color: isSelected ? '#3377ff' : '#000' }]}
                          variant="semiBold"
                        >
                          {item}
                        </Text>
                      </TouchableOpacity>
                    );
                  })}
                </View>
                {radioValue !== 'Internal' && (
                  <>
                    <DropdownField
                      label="Select Organization Name"
                      value={selectedLead}
                      data={leads}
                      keyField="orgId"
                      valueField="organizationName"
                      onSelect={(item) => setSelectedLead(item)}
                      placeholder="Select an organization"
                      enableSearch
                      enablePagination
                      onSearch={(text) => {
                        setSearchText(text);
                        setPageNumber(1);
                        fetchLeadsList({ pageNumber: 1, searchText: text });
                      }}
                      onEndReached={handleLoadMore}
                      isLoading={isFetchingMore}
                      hasMoreData={hasMoreData}
                    />
                    {errors.lead && (
                      <HelperText
                        type="error"
                        visible={!!errors.lead}
                        padding="none"
                        style={{
                          padding: 0,
                          margin: 0,
                          fontSize: 12,
                          backgroundColor: 'transparent',
                        }}
                      >
                        {errors.lead}
                      </HelperText>
                    )}
                  </>
                )}
              </View>
            ) : (
              (route.params?.selectedEvent?.organization_name ||
                route.params?.leadDetails?.lead?.organizationName) && (
                <Card
                  style={{
                    borderRadius: 12,
                    backgroundColor: '#f9fafb',
                    padding: moderateScale(14),
                    marginBottom: verticalScale(5),
                  }}
                >
                  <View
                    style={{
                      paddingLeft: scale(10),
                      justifyContent: 'space-between',
                      borderLeftColor: '#3377ff',
                      borderLeftWidth: scale(3),
                    }}
                  >
                    <Text
                      style={{
                        fontSize: moderateScale(12),
                        color: '#3377ff',
                        fontFamily: fontFamily.semiBold,
                      }}
                    >
                      Organization
                    </Text>
                    <Text
                      style={{
                        fontSize: moderateScale(15),
                        fontFamily: fontFamily.bold,
                        color: '#000',
                        textTransform: 'uppercase',
                      }}
                    >
                      {route.params?.leadDetails?.lead?.organizationName ||
                        route.params?.selectedEvent?.organization_name ||
                        ''}
                    </Text>
                  </View>
                </Card>
              )
            )}

            <Text variant="semiBold" style={styles.label}>
              Activity Type
            </Text>
            <View style={[styles.row, { alignItems: 'flex-start' }]}>
              {activityTypes.map((type: ObjectType) => (
                <Chip
                  key={type.Value}
                  textStyle={{
                    color: 'rgba(51, 119, 255, 1)',
                    fontFamily: fontFamily.semiBold,
                    fontSize: moderateScale(12),
                  }}
                  selectedColor="rgba(51, 119, 255, 1)"
                  selected={activityType === type.Text}
                  onPress={() => activityTypeHanlder(type)}
                  mode="flat"
                  style={styles.chip}
                >
                  {type.Text}
                </Chip>
              ))}
            </View>
            {/* <TextInput
            value={title}
            onChangeText={setTitle}
            style={styles.input}
            error={!!errors.title}
            mode="flat"
            underlineColor="transparent"
            theme={{ colors: { primary: 'transparent' } }}
            multiline
            numberOfLines={2}
            selectionColor="black"
          /> */}
            {/* <InputFeild
            value={title}
            onChangeText={setTitle}
            style={{
              borderWidth: 1,
              borderColor: '#ccc',
              borderRadius: 8,
              backgroundColor: 'white',
              fontSize: moderateScale(14),
              padding: verticalScale(13),
              margin: 0,
              minHeight: 48,
              textAlignVertical: 'center',
            }}
            selectionColor="#3377FF"
            /> */}
            <CustomInput
              label="Activity Title"
              value={title}
              onChangeText={setTitle}
              placeholder="Enter activity title"
              errorText={errors.title}
              maxLength={60}
            />
            {/* {errors.title && <HelperText type="error">{errors.title}</HelperText>} */}
            {/* Date Pickers */}
            <TouchableOpacity onPress={() => {}} style={{ flex: 1 }}>
              <Text variant="semiBold" style={styles.label}>
                Meeting Date
              </Text>
              <TouchableOpacity
                onPress={() => {
                  setShowPicker(true);
                }}
              >
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingHorizontal: scale(12),
                  borderRadius: 4,
                  borderWidth: 1,
                  borderColor: '#ccc',
                  backgroundColor: 'white',
                  marginBottom: verticalScale(5),
                  height: verticalScale(28),
                }}>
                {calendarIcon()}
                <Text style={{
                  fontSize: moderateScale(12),
                  color: "#000",
                  marginLeft: scale(5)
                }} variant='regular' >{formatted(startDate || new Date())}</Text>
                </View>
              </TouchableOpacity>
            </TouchableOpacity>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 12,
              }}
            >
              <DropdownField
                label="Start Time"
                value={startTime}
                data={timeSlots}
                keyField="Value"
                valueField="Text"
                onSelect={onSelectStartTimer}
                error={!!errors.customerConnect}
                icon={'clock-time-four-outline'}
                showAsterisk={false}
                searchPlaceholderText={"Search start time"}
              />
              <DropdownField
                label="End Time"
                value={endTime}
                data={endTimeSlots}
                keyField="Value"
                valueField="Text"
                onSelect={onSelectEndTimer}
                error={!!errors.customerConnect}
                disable={startTime === ''}
                icon={'clock-time-four-outline'}
                showAsterisk={false}
                searchPlaceholderText={"Search end time"}
              />
              {errors.endTime && <HelperText type="error">{errors.endTime}</HelperText>}
            </View>

            {/* Assigned To Dropdown with DropdownMenu */}
            <DropdownMulti<InternalUserType>
              label={radioValue !== 'Internal' ? 'Assigned To' : 'Internal Attendees'}
              value={assignedTo}
              data={internalUsers}
              keyField="account_user_id"
              valueField="first_name"
              multiSelect
              initialSelected={[]}
              onSelect={(items) => setAssignedTo(items as InternalUserType[])}
              isMandatory={false}
            />
            {errors.assignedTo && <HelperText type="error">{errors.assignedTo}</HelperText>}
           
            {/* Customer Connect Dropdown with DropdownMenu */}
            {(!route.params?.handleRefresh || radioValue !== 'Internal') && (
              <DropdownMulti<CustomerType>
                label="Customer Connects"
                value={customerConnect}
                data={externalContacts}
                keyField="contact_id"
                valueField="contact_name"
                multiSelect
                initialSelected={[]}
                onSelect={(items) => setCustomerConnect(items as CustomerType[])}
                error={!!errors.customerConnect}
                isMandatory={false}
              />
            )}

            {errors.customerConnect && (
              <HelperText type="error">{errors.customerConnect}</HelperText>
            )}

             {/* Area Dropdown with DropdownMenu */}
            {(!route.params?.handleRefresh || radioValue !== 'Internal') && (
              <DropdownField
                label="Area"
                value={area?.Text || ''}
                data={areas}
                keyField="Value"
                valueField="Text"
                onSelect={(item) => setArea(item)}
                error={!!errors.customerConnect}
                showAsterisk={showAsterisk}
                labelField={''}
                errorText={errors.area}
                searchPlaceholderText={"Search area"}
              />
            )}

            {/* Status Dropdown with DropdownMenu */}
            <DropdownField
              label="Status"
              value={status?.Text || ''}
              data={statuses}
              keyField="Value"
              valueField="Text"
              onSelect={(item) => setStatus(item)}
              error={!!errors.customerConnect}
              showAsterisk={false}
              type='radio'
              radioOptionContainerStyle={styles.radioContainer}
            />
            {errors.status && <HelperText type="error">{errors.status}</HelperText>}

            {/* Priority Dropdown with DropdownMenu */}
            <DropdownField
              label="Priority"
              value={priority?.Text || ''}
              data={priorities}
              keyField="Value"
              valueField="Text"
              onSelect={(item) => setPriority(item)}
              error={!!errors.customerConnect}
              showAsterisk={false}
              type='radio'
              radioOptionContainerStyle={styles.radioContainer}
            />
            {errors.priority && <HelperText type="error">{errors.priority}</HelperText>}
            {/* Estimated Time Dropdown with DropdownMenu */}
            <DropdownField
              label="Estimated Time"
              value={estimatedTime?.Text || ''}
              data={estimatedTimes}
              keyField="Value"
              valueField="Text"
              onSelect={(item) => setEstimatedTime(item)}
              error={!!errors.customerConnect}
              showAsterisk={false}
              type='radio'
              radioOptionContainerStyle={styles.radioContainer}
            />
            {errors.estimatedTime && <HelperText type="error">{errors.estimatedTime}</HelperText>}

            {/* Tags */}
            <CustomStringDropdown
              label={'Tags'}
              data={suggestions}
              value={tags}
              onSelect={onSelectHandler}
            />
            <View
              style={{
                flexWrap: 'wrap',
                flexDirection: 'row',
              }}
            >
              {tags.map((item, index) => (
                <Chip
                  key={`${item}-${index}`}
                  style={[
                    styles.chip,
                    {
                      height: 36,
                      backgroundColor: 'rgba(240, 247, 255, 1)',
                    },
                  ]}
                  icon={'tag'}
                  textStyle={{ color: 'rgba(0,0,0,1)' }}
                  // onPress={() => {
                  //     setTags(tags.filter((tag) => tag !== item));
                  // }}
                  onClose={() => setTags(tags.filter((tag) => item !== tag))}
                >
                  {item}
                </Chip>
              ))}
            </View>
            {/* <View>
              <CustomInput
                label="Tags"
                value={input}
                onChangeText={(text: string) => {
                  setInput(text);
                  debouncedSearch(text);
                }}
                placeholder="Enter activity title"
                maxLength={30}
                onFocus={() => {
                  setShowOptions(true)
                }}
                onBlur={() => {
                  setShowOptions(false)
                }}
              />
            {showOptions && <View>
                {showCreateOption && (
                  <TouchableOpacity
                    onPress={handleAdd}
                    style={{
                      paddingVertical: 10,
                      paddingHorizontal: 20,
                      borderBottomWidth: 1,
                      borderColor: '#ccc',
                    }}
                  >
                    <Text style={{ color: '#3377FF' }}>{`+ create "${input.trim()}"`}</Text>
                  </TouchableOpacity>
                )}
                <FlatList
                  data={filteredData}
                  keyExtractor={(item, idx) => `${item}-${idx}`}
                  contentContainerStyle={{ paddingBottom: verticalScale(50) }}
                  keyboardShouldPersistTaps="handled"
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={{
                        padding: 20,
                        borderBottomWidth: 1,
                        borderColor: '#ccc',
                      }}
                      onPress={() => {
                        onSelectHandler(item);
                      }}
                    >
                      <Text
                        style={{
                          fontSize: moderateScale(12),
                          color: '#1a1a1a',
                        }}
                      >
                        {item}
                      </Text>
                    </TouchableOpacity>
                  )}
                />
              </View>}
            </View> */}
            {/* <CustomInput
              label="Tags"
              value={tagInput}
              onChangeText={setTagInput}
              placeholder="Type custom tag"
              maxLength={60}
              rightIcon={'plus'}
              onRightIconPress={onAddTag}
              rightIconColor="#000"
            /> */}
            {/* <View style={styles.row}>
              {suggestions.map((tag) => {
                const isSelected = tags.includes(tag);
                return (
                  <Chip
                    key={`${tag}-${isSelected}`}
                    style={[
                      styles.chip,
                      {
                        height: 36,
                        backgroundColor: isSelected ? 'rgba(240, 247, 255, 1)' : '#fff',
                      },
                    ]}
                    icon={isSelected ? 'tag' : 'tag-outline'}
                    textStyle={{ color: 'rgba(0,0,0,1)' }}
                    onPress={() => {
                      if (isSelected) {
                        setTags(tags.filter((item) => item !== tag));
                      } else {
                        setTags([tag, ...tags]);
                      }
                    }}
                    onClose={() => setSuggestions(suggestions.filter((item) => item !== tag))}
                  >
                    {tag}
                  </Chip>
                );
              })}
            </View> */}
            <SafeAreaView>
              <View style={[styles.actionRow]}>
                <Button
                  mode="contained"
                  style={{
                    flex: 1,
                    borderRadius: 4,
                    backgroundColor: 'rgba(239, 241, 246, 1)',
                  }}
                  textColor="rgba(0, 0, 0, 1)"
                  onPress={() => navigation.goBack()}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  mode="contained"
                  style={{
                    flex: 1,
                    borderRadius: 4,
                    backgroundColor: 'rgba(51, 119, 255, 1)',
                  }}
                  textColor="rgba(255, 255, 255, 1)"
                  onPress={handleCreateActivity}
                  loading={isSubmitting}
                  disabled={isSubmitting}
                >
                  {route.params.selectedEvent ? 'Update Activity' : 'Create Activity'}
                </Button>
              </View>
            </SafeAreaView>
          </ScrollView>
        </KeyboardAvoidingView>
        <Snackbar
          visible={snackbarVisible}
          onDismiss={() => setSnackbarVisible(false)}
          duration={3000}
          style={snackbarType === 'error' ? styles.errorSnackbar : styles.successSnackbar}
          action={{
            label: 'Close',
            onPress: () => setSnackbarVisible(false),
            textColor: '#fff',
          }}
        >
          <Text
            style={{ color: '#fff', fontFamily: fontFamily.medium, fontSize: moderateScale(15) }}
          >
            {snackbarMessage}
          </Text>
        </Snackbar>
        <CustomAlert
          visible={showAlert}
          title="Confirm Deletion"
          message="Are you sure you want to delete this item? This action cannot be undone."
          onConfirm={() => {
            handleDeleteActivity();
            setShowAlert(false);
          }}
          onCancel={() => {
            setShowAlert(false);
          }}
          showCancel={true}
          confirmText="Delete"
          cancelText="Cancel"
          showCloseIcon={false}
        />
        {showWarning && pendingDate && (
          <CustomAlert
            visible={true}
            title="Past Date Warning"
            message={
              'You are trying to schedule an event in the past. Are you sure you want to proceed?\n\nCreating events in the past can cause confusion. Make sure this is intentional.'
            }
            titleColor="#D97706"
            confirmText="Yes, Schedule in Past"
            cancelText="Cancel"
            showCancel={true}
            onConfirm={() => {
              if (pendingDate) applySelectedDate(pendingDate);
              setShowWarning(false);
              setPendingDate(null);
              setShowPicker(false);
            }}
            onCancel={() => {
              setShowWarning(false);
              setPendingDate(null);
              setShowPicker(false);
            }}
            button2Styles={{ backgroundColor: '#D97706' }}
          />
         
        )}

        {/* {showPicker && <Calendar
  onDayPress={(day) => console.log(day.dateString)}
  theme={{
    selectedDayBackgroundColor: '#3377ff',
    todayTextColor: '#3377ff',
    arrowColor: '#3377ff',
    textMonthFontWeight: 'bold',
    textDayFontWeight: '500',
    textMonthFontSize: 18,
    textDayFontSize: 14,
  }}
/>} */}
        <SafeAreaView>
          <DateTimePickerModal
            isVisible={showPicker}
            mode="date"
            onConfirm={onDateSelect}
            onCancel={() => setShowPicker(false)}
            date={activeField === 'start' ? startDate : endDate || startDate}
            minimumDate={activeField === 'start' ? fiveYearsAgo : startDate}
            maximumDate={activeField === 'end' ? startDate : undefined}
            accentColor="#3377ff"
            isDarkModeEnabled={false}
            // textColor="#3377ff"
            locale="en-IN"
            // display={Platform.select({ ios: 'spinner', android: 'default' })}
            pickerContainerStyleIOS={
              Platform.OS === 'ios'
                ? {
                    // alignSelf: 'center',
                    // justifyContent: 'center',
                    // backgroundColor: '#fff',
                    // borderRadius: 16,
                    // padding: 10,
                    // width: '90%',
                  }
                : undefined
            }
          />
        </SafeAreaView>
        {/* {showPicker && (
          // <DateTimePicker
          //   value={startDate || new Date()}
          //   mode="date"
          //   // display="calendar"  // Force Material calendar UI
          //   onChange={(event, selectedDate) => {
          //     if (event.type === 'set' && selectedDate) {
          //       setStartDate(selectedDate);
          //       setEndDate(selectedDate);
          //       // setTimeout(() => setShowStartTimePicker(true), 300);
          //     }
          //     setShowPicker(false); // Always close picker after interaction
          //   }}
          //   textColor="red"
          // />
          <DateTimePicker
          value={startDate || new Date()}
          mode="date"
          display={Platform.select({ ios: 'spinner', android: 'calendar' })}
          // onChange={...}
          textColor={Platform.OS === 'ios' ? 'red' : undefined}
        />
        )} */}

        {/* <Modal visible={showPicker} transparent animationType="fade">
          <View
            style={{
              flex: 1,
              backgroundColor: 'rgba(0,0,0,0.4)',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <View
              style={
                {
                  // backgroundColor: 'white',
                  // borderRadius: 20,
                  // padding: 20,
                  // width: 300,
                  // alignItems: 'center',
                  // backgroundColor: 'red',
                }
              }
            >
              <DateTimePicker
                value={startDate || new Date()}
                mode="date"
                display="default"
                // design="material"
                onChange={(event, selectedDate) => {
                  if (event.type === 'set' && selectedDate) {
                    setStartDate(selectedDate);
                    setEndDate(selectedDate);
                    // setTimeout(() => setShowStartTimePicker(true), 300);
                  }
                  // setShowPicker(false);
                }}
                style={{ backgroundColor: 'white', borderTopRightRadius:20,borderTopLeftRadius:20}}
              />
              <Button
                onPress={() => {
                  console.log('dateee');
                  setShowPicker(false);
                }}
                children={'SAVE'}
                style={{backgroundColor:"white",borderTopRightRadius:0,borderTopLeftRadius:0}}
              />
            </View>
          </View>
        </Modal> */}
        {/* <DatePickerModal
              locale="en"
              mode="single"
              visible={showPicker}
              onDismiss={() => {
                setShowPicker(false);
              }}
              validRange={
                activeField === 'end'
                  ? {
                      startDate: startDate,
                      endDate: startDate,
                    }
                  : {
                      startDate: fiveYearsAgo,
                    }
              }
              date={startDate}
              onConfirm={onDateSelect}
            /> */}
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  radioGroup: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: verticalScale(10),
  },
  scrollContent: {
    paddingHorizontal: scale(16),
  },
  suggestionContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    marginTop: 4,
    elevation: 5,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 },
    zIndex: 1000,
    height: 200,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 80,
    paddingTop: 0,
  },
  sectionTitle: {
    fontFamily: fontFamily.semiBold,
    fontSize: moderateScale(14),
    marginTop: verticalScale(10),
    marginBottom: verticalScale(6.5),
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    // marginBottom: 8,
  },
  chip: {
    marginRight: scale(5),
    marginBottom: verticalScale(5),
    borderColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'space-around',
    borderWidth: 2,
    backgroundColor: 'rgba(240, 247, 255, 1)',
  },
  input: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    height: 48,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ccc',
    backgroundColor: 'white',
    marginBottom: 12,
    color: '#000',
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    height: 48,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#ccc',
    backgroundColor: 'white',
    marginBottom: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 8,
    minWidth: 220,
    maxHeight: 320,
  },
  modalHeader: {
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingBottom: 8,
    marginBottom: 8,
  },
  modalTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center',
  },
  modalItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
  },
  successSnackbar: {
    backgroundColor: '#4CAF50',
    color: '#fff',
  },
  errorSnackbar: {
    backgroundColor: '#F44336',
  },
  dateText: {
    // color: 'rgba(109, 121, 142, 1)',
    color: '#000',
    fontSize: moderateScale(12),
    // fontFamily: fontFamily.semiBold,
  },
  chip_date: {
    backgroundColor: 'rgba(255, 255, 255, 1)',
    borderColor: 'rgba(203, 213, 225, 1)',
    alignItems: 'center',
    justifyContent: 'center',
    // height: verticalScale(31),
    height: verticalScale(28),
    borderRadius: 4,
    marginBottom: verticalScale(5),
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    // padding: 16,
    gap: scale(10),
    marginTop : moderateScale(10),
    marginBottom : moderateScale(20),
    // marginBottom: Platform.OS === 'ios' ? verticalScale(10) : 0,
    // backgroundColor : 'red'
    // marginTop:verticalScale(10)
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginRight: moderateScale(20),
    // backgroundColor:"red",
    gap: scale(10),
    width:"35%",
    marginBottom:verticalScale(5)
  },
  outerCircle: {
    width: moderateScale(16),
    height: moderateScale(16),
    borderRadius: 30,
    borderWidth: moderateScale(2),
    borderColor: '#3377ff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  innerCircle: {
    width: moderateScale(8),
    height: moderateScale(8),
    borderRadius: 30,
    backgroundColor: '#3377FF',
  },
  label: {
    // marginLeft: 8,
    // fontSize: moderateScale(14),
    // color: '#000',
    fontSize: moderateScale(12),
    marginVertical: verticalScale(5),
    color: '#1A1A1A',
  },
  radioContainer:{
    borderWidth:0,
    backgroundColor:"",
    paddingHorizontal:scale(2.5),
    marginHorizontal:0,
    marginBottom:0,
    paddingVertical:0,
    paddingTop:verticalScale(5),
  }
});

export default CreateActivityScreen;