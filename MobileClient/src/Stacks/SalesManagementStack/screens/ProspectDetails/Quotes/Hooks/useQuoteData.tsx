import { useState, useEffect, useRef, useCallback } from 'react';
import ApiClient from '../../../../../../Common/API/ApiClient';
import { ENDPOINTS } from '../../../../../../Common/API/ApiEndpoints';
import { encryptPayload } from '../../../../../../Utils/payloadEncryption';
import {
  getAccountId,
  getSchemaName,
  getSelectedWorkspaceId,
  getUserId,
} from '../../../../../../Common/Utils/Storage';
import { useSelector } from 'react-redux';
import { useRoute } from '@react-navigation/native';
import { Quote, UseQuotesListReturn } from '../QuotesList.types';
const PAGE_SIZE = 10;
const useQuotesList = (): UseQuotesListReturn => {
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [searchText, setSearchText] = useState<string | null>(null);

  const debounceTimer = useRef<NodeJS.Timeout | null>(null);

  const route = useRoute();
  const lead = route.params?.leadDetails?.lead || {};
  const { orgId = '' } = lead;
  const selectedControlUnitID = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data?.id
  );

  const fetchQuotes = useCallback(
    async (params?: { offset?: number; search_text?: string | null; isRefresh?: boolean }) => {
      const { offset = 0, search_text = searchText, isRefresh = false } = params || {};

      try {
        if (isRefresh) {
          setRefreshing(true);
        } else {
          setLoading(true);
        }
        setError(null);

        const schemaName = await getSchemaName();
        const account_id = await getAccountId();
        const userId = await getUserId();
        const workspace_id = await getSelectedWorkspaceId();

        const payloadJson = JSON.stringify({
          contextJson: {
            account_id,
            workspace_id,
            control_unit_id: selectedControlUnitID,
            user_id: userId,
          },
          inputParamsJson: {
            customer_id: orgId,
            price_sheet_state_ids: [2, 3],
            search_text,
            limit: PAGE_SIZE,
            offset,
          },
        });

        const encryptedPayload = await encryptPayload(payloadJson);
        const finalPayload = {
          payloadJson: encryptedPayload.encryptedPayload,
          schemaName,
        };
        const response = await ApiClient.post(ENDPOINTS.LEADS.ALL_QUOTES, finalPayload);

        const responseData = response?.data?.data?.[0];
        const newQuotes: Quote[] = responseData?.result?.data || [];

        setHasMore(newQuotes.length === PAGE_SIZE);

        if (isRefresh || offset === 0) {
          setQuotes(newQuotes);
        } else {
          setQuotes((prev) => [...prev, ...newQuotes]);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An error occurred';
        setError(errorMessage);
        console.error('Failed to fetch quotes:', err);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [orgId, selectedControlUnitID, searchText]
  );

  const refreshQuotes = async () => {
    await fetchQuotes({ offset: 0, isRefresh: true });
  };

  const loadMoreQuotes = async () => {
    if (!hasMore || loading) return;
    await fetchQuotes({ offset: quotes.length });
  };

  // Debounced search
  const searchQuotes = (text: string) => {
    setSearchText(text || null);

    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    debounceTimer.current = setTimeout(() => {
      fetchQuotes({ offset: 0, search_text: text || null });
    }, 200);
  };

  useEffect(() => {
    fetchQuotes({ offset: 0 });
  }, [selectedControlUnitID]);

  return {
    quotes,
    loading,
    error,
    refreshing,
    hasMore,
    fetchQuotes,
    refreshQuotes,
    loadMoreQuotes,
    searchQuotes,
  };
};

export default useQuotesList;
