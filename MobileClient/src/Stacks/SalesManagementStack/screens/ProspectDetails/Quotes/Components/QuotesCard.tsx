import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { fontFamily } from '../../../../../../Common/Theme/typography';
import { moderateScale, verticalScale, scale } from 'react-native-size-matters';
interface QuoteCardMiniProps {
  quote : any;
  onPress?: () => void;
}

const QuoteCardMini: React.FC<QuoteCardMiniProps> = ({ quote, onPress }) => {
  // Formatting helpers
  const formatCurrency = (amount: number, symbol: string) => `${symbol} ${amount.toFixed(2)}`;

  function formatStatus(price_sheet_state_id: number) {
    return price_sheet_state_id === 1 ? 'Draft' : price_sheet_state_id === 2 ? 'Quoted' : 'Booked';
  }
  const formatDate = (val: string) => {
    if (!val) return '';
    const d = new Date(val);
    return d.toLocaleDateString('en-GB');
  };
 const getStatusColor = (price_sheet_state_id: number) => {
    switch (price_sheet_state_id) {
      case 1: return '#ffc107'; 
      case 2: return '#1e40af'; 
      case 3: return '#28a745'; 
      default: return '#6c757d'; 
    }
  };
  return (
    <TouchableOpacity style={styles.card} onPress={onPress} activeOpacity={0.85}>
      <View style={styles.cardHeader}>
        <Text style={styles.cardTitle} numberOfLines={1}>
          {quote.price_sheet_name}
        </Text>
        <Text style={styles.quoteNumber}>
          Quote No:{' '}
          <Text style={styles.quoteNoStrong}>{quote?.reference_number || 'Not Available'}</Text>
        </Text>
         <Text style={styles.quoteNumber}>
          Enquiry No:{' '}
          <Text style={styles.quoteNoStrong}>{quote?.enq_reference_number || 'Not Available'}</Text>
        </Text>
        <View style={styles.statusRow}>
          <View style={[styles.bookedBadge, { backgroundColor: getStatusColor(quote?.price_sheet_state_id) }]}>
            <Text style={[styles.bookedText, ]}>{formatStatus(quote.price_sheet_state_id)}</Text>
          </View>
          <Text style={styles.validityText}>{formatDate(quote?.created_at)}</Text>
        </View>
      </View>

      <View style={styles.totalsRow}>
        <View style={styles.totalBoxLeft}>
          <Text style={styles.labelSmall}>Total Cost</Text>
          <Text style={styles.totalCost}>
            {formatCurrency(quote.total_cost_price, quote.total_cost_price_currency_symbol)}
          </Text>
        </View>
        <View style={styles.totalBoxRight}>
          <Text style={styles.labelSmall}>Total Selling</Text>
          <Text style={styles.totalSelling}>
            {formatCurrency(quote.total_selling_price, quote.total_selling_price_currency_symbol)}
          </Text>
        </View>
      </View>

      <View style={styles.middleRow}>
   
        <View style={styles.midBox}>
          <Text style={styles.midLabel}>Quantity</Text>
          <Text style={styles.midValue}>{quote?.price_sheet_items?.length}</Text>
        </View>
        <View style={styles.midBox}>
          <Text style={styles.midLabel}>Margin</Text>
          <Text style={styles.midValue}>{quote.margin_percentage}%</Text>
        </View>
      </View>

      {/* PROFIT */}
      <View style={styles.profitBox}>
        <Text style={styles.profitLabel}>Profit</Text>
        <Text style={styles.profitValue}>
          {formatCurrency(quote.profit, quote.total_selling_price_currency_symbol)}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 15,
    paddingTop: 16,
    paddingBottom: 12,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.09,
    shadowRadius: 6,
    elevation: 4,
    // margin: 12,
    // for border
    borderWidth: 0.8,
    borderColor: '#f1f3f4',
    marginBottom: moderateScale(4),
  },
  cardHeader: {
    marginBottom: 8,
  },
  cardTitle: {
    fontSize: 17,
    color: '#202124',
    fontFamily: fontFamily.semiBold,
    marginBottom: 2,
  },
  quoteNumber: {
    color: '#65717a',
    fontSize: 13,
    marginBottom: 6,
    fontFamily: fontFamily.regular,
  },
  quoteNoStrong: {
    color: '#222',
    fontFamily: fontFamily.semiBold,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
    gap: 10,
  },
  bookedBadge: {
    backgroundColor: '#c9f5df',
    borderRadius: 16,
    paddingHorizontal: 8,
    paddingVertical: 2.5,
    marginRight: 12,
  },
  bookedText: {
    color: '#fff',
    fontSize: 11,
    fontFamily: fontFamily.semiBold,
  },
  validityText: {
    fontSize: 12,
    color: '#747981',
    fontFamily: fontFamily.regular,
  },
  totalsRow: {
    flexDirection: 'row',
    marginTop: 12,
    marginBottom: 14,
  },
  totalBoxLeft: {
    backgroundColor: '#eef6ff',
    flex: 1,
    borderRadius: 9,
    alignItems: 'center',
    paddingVertical: 12,
    marginRight: 6,
  },
  totalBoxRight: {
    backgroundColor: '#dff8e7',
    flex: 1,
    borderRadius: 9,
    alignItems: 'center',
    paddingVertical: 12,
    marginLeft: 6,
  },
  labelSmall: {
    fontSize: 13,
    color: '#6d7c95',
    marginBottom: 4,
    fontFamily: fontFamily.regular,
  },
  totalCost: {
    fontSize: 17,
    color: '#2471e2',
    fontFamily: fontFamily.semiBold,
  },
  totalSelling: {
    fontSize: 17,
    color: '#21ab53',
    fontFamily: fontFamily.semiBold,
  },
  middleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  midBox: {
    flex: 1,
    backgroundColor: '#f5f7fa',
    borderRadius: 8,
    alignItems: 'center',
    paddingVertical: 7,
    marginHorizontal: 2,
  },
  midLabel: {
    fontSize: 12,
    color: '#9ca6b7',
    fontFamily: fontFamily.regular,
    marginBottom: 2,
  },
  midValue: {
    fontSize: 15,
    color: '#27304b',
    fontFamily: fontFamily.semiBold,
  },
  profitBox: {
    backgroundColor: '#fefae2',
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 13,
    paddingHorizontal: 12,
    marginTop: 12,
    justifyContent: 'space-between',
  },
  profitLabel: {
    color: '#ebbb19',
    fontSize: 16,
    fontFamily: fontFamily.semiBold,
  },
  profitValue: {
    color: '#ff9b00',
    fontSize: 18,
    fontFamily: fontFamily.semiBold,
  },
});

export default QuoteCardMini;
