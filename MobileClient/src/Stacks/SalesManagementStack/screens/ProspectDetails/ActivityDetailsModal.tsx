import React from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Text, Avatar, Chip } from 'react-native-paper';
import { Calendar, Clock, MapPin, Users, Phone } from 'lucide-react-native';
import dayjs from 'dayjs';
import { scale, moderateScale, verticalScale } from 'react-native-size-matters';
import { fontFamily } from '../../../../Common/Theme/typography';
import AppHeader from '../../../../Components/AppHeader';

const InfoRow = ({
  icon,
  label,
  value,
  isVertical = false,
  isHorizontal = false,
}: {
  icon: React.ReactNode;
  label: string;
  value?: string;
  isVertical?: boolean;
  isHorizontal?: boolean;
}) => (
  <View style={[styles.infoRow, isVertical && styles.infoRowVertical]}>
    {icon}
    <View style={styles.infoTextBlock}>
      <Text variant='medium' style={styles.infoLabel}>{label}</Text>
      {isHorizontal && value && <Text variant='regular' style={styles.subText}>{value}</Text>}
    </View>
    {!isHorizontal && value && <Text variant='regular' style={styles.subText}>{value}</Text>}
  </View>
);

const InfoText = ({ label, value }: { label: string; value?: string }) => (
  <View style={styles.infoTextRow}>
    <Text variant='medium' style={styles.infoLabel}>{label}:</Text>
    <Text variant='regular' style={styles.subText}>{value || '--'}</Text>
  </View>
);


const ActivityDetailsModal = ({route}) => {

  const  {activity}=route.params
  if (!activity) return null;

  const getInitial = (str: string) => str?.[0]?.toUpperCase() || '?';

  return (
<View style={{flex:1,backgroundColor: '#fff'}}>
<AppHeader title="ActivityDetail" />
      <View style={styles.overlay}>
        
        <TouchableOpacity style={StyleSheet.absoluteFill} />
        <View style={styles.container}>
          <View style={styles.headerContainer}>
          </View>

          <ScrollView contentContainerStyle={styles.content} bounces={false}>
            <View style={styles.header}>
              <View style={styles.iconCircle}>
                <Phone size={16} color="#fff" />
              </View>
              <View style={styles.headerTextContainer}>
                <Text variant='semiBold' style={styles.activityTitle}>{activity?.sales_activity_title || '--'}</Text>
                <Text style={styles.orgName}>{activity?.organization_name || '--'}</Text>
              </View>
            </View>

            <InfoRow
              icon={<Calendar size={16} color="#2563EB" />}
              label="Date"
              value={`${dayjs(activity.sales_activity_date).format('MMM DD, YYYY')} / ${dayjs(
                activity.start_date_time
              ).format('hh:mm A')} - ${dayjs(activity.end_date_time).format('hh:mm A')}`}
            />
            <InfoRow
              icon={<Clock size={16} color="#2563EB" />}
              label="Duration"
              value={activity.sales_activity_estimated_time_name || '--'}
            />
            <InfoRow
              icon={<MapPin size={16} color="#2563EB" />}
              label="Location"
              value={activity.area_name || '--'}
            />
            <InfoRow
              icon={<Users size={16} color="#2563EB" />}
              label="Attendees"
              value={`${activity.assigned_users?.length ? activity.assigned_users?.length + 1 : 0}`}
            />

            <Text variant='medium' style={styles.section}>Activity Status</Text>
            <InfoText label="Type" value={activity.sales_activity_type_name} />
            <InfoText label="Priority" value={activity.sales_activity_priority_name} />
            <InfoText label="Status" value={activity.sales_activity_state_name} />

            {Array.isArray(activity.assigned_users) && activity.assigned_users.length > 0 && (
              <>
                <Text variant='medium' style={styles.section}>Assigned Users</Text>
                {activity.assigned_users.map((user: any) => (
                  <InfoRow
                    key={user.account_user_id}
                    icon={
                      <Avatar.Text size={moderateScale(24)} label={getInitial(user.first_name)} />
                    }
                    label={user.first_name}
                    value={user.email}
                    isVertical
                    isHorizontal
                  />
                ))}
              </>
            )}

            {Array.isArray(activity.contact_profiles) && activity.contact_profiles.length > 0 && (
              <>
                <Text variant='medium' style={styles.section}>Contacts</Text>
                {activity.contact_profiles.map((contact: any) => (
                  <InfoRow
                    key={contact.contact_id}
                    icon={
                      <Avatar.Text
                        size={moderateScale(24)}
                        label={getInitial(contact.contact_name)}
                        style={styles.greenAvatar}
                      />
                    }
                    label={contact.contact_name}
                    value={activity.organization_name}
                    isVertical
                    isHorizontal
                  />
                ))}
              </>
            )}

            {Array.isArray(activity.tags) && activity.tags.length > 0 && (
              <>
                <Text variant='medium' style={styles.section}>Tags</Text>
                <View style={styles.row}>
                  {activity.tags.map((tag: string, idx: number) => (
                    <Chip
                      key={`tag-${idx}`}
                      style={styles.tagChip}
                      textStyle={styles.tagText}
                      icon="tag"
                    >
                      {tag}
                    </Chip>
                  ))}
                </View>
              </>
            )}
          </ScrollView>
        </View>
      </View>
</View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
  },
  container: {
    flex:1,
    backgroundColor: '#fff',
    paddingHorizontal: moderateScale(14),
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontFamily: fontFamily.medium,
    color: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(10),
  },
  iconCircle: {
    backgroundColor: '#F97316',
    height: moderateScale(32),
    width: moderateScale(32),
    borderRadius: moderateScale(17),
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTextContainer: {
    marginLeft: scale(8),
    flex: 1,
  },
  activityTitle: {
    fontSize: moderateScale(13),
    color: '#000',
  },
  orgName: {
    fontSize: moderateScale(11),
    color: '#647488',
    marginTop: verticalScale(2),
  },
  content: {
    paddingBottom: verticalScale(20),
  },
  infoTextBlock: {
    marginLeft: scale(8),
  },
  infoLabel: {
    fontSize: moderateScale(12),
    color: '#111827',
    marginRight: scale(6),
  },
  subText: {
    fontSize: moderateScale(12),
    color: '#647488',
  },
  greenAvatar: {
    backgroundColor: '#22c55e',
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tagChip: {
    marginRight: scale(5),
    marginBottom: scale(5),
    borderColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'space-around',
    borderWidth: 2,
    backgroundColor: 'rgba(240, 247, 255, 1)',
  },
  tagText: {
    color: 'rgba(0,0,0,1)',
    fontSize:moderateScale(12)
  },
  section: {
    fontSize: moderateScale(12),
    color: '#0F172A',
    marginTop: verticalScale(5),
    marginBottom: verticalScale(5),
  },

  infoTextRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    marginBottom: verticalScale(8),
    paddingLeft: scale(6),
  },

  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(5),
    paddingLeft: scale(6),
  },

  infoRowVertical: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: scale(6),
  },
});

export default ActivityDetailsModal;
