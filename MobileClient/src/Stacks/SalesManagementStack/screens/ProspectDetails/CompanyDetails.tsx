import React from 'react';
import { ScrollView, View, StyleSheet } from 'react-native';
import { Text, Card } from 'react-native-paper';
import { toPascalCase } from '../../../../Components/UI/Menu/DropdownModal';

interface Props {
  companyDetails: any;
}

const CompanyDetails: React.FC<Props> = ({ companyDetails }) => {
  const org = companyDetails?.org_details || {};

  const InfoCard = ({
    title,
    children,
  }: {
    title: string;
    children: React.ReactNode;
  }) => (
    <Card style={styles.infoCard} mode="outlined">
      <Card.Title title={title} titleVariant="titleMedium" />
      <Card.Content>{children}</Card.Content>
    </Card>
  );

  const InfoRow = ({ label, value }: { label: string; value: any }) => (
    <View style={styles.infoRow}>
      <Text style={styles.infoRowLabel}>{label}</Text>
      <Text style={styles.infoRowValue}>{value || '---'}</Text>
    </View>
  );

  return (
    <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
      <InfoCard title="Basic Information">
        <InfoRow label="Company Name" value={org.company_name} />
        <InfoRow label="Industry" value={org.industry_name} />
        <InfoRow label="Company Size" value={org.company_size} />
        <InfoRow label="Year Founded" value={org.year_founded} />
        {/* <InfoRow label="Org Type" value={org.org_type_name} /> */}
      </InfoCard>

      <InfoCard title="Contact Information">
        {(org.phone_numbers || []).map((phone: any, index: number) => (
          <View key={index}>
            {/* <InfoRow label="Phone Type" value={phone.phone_type_name} /> */}
            <InfoRow label="Contact Number" value={phone.phone_number} />
            <InfoRow label="Is On WhatsApp" value={phone.is_on_whatsapp ? 'Yes' : 'No'} />
            <InfoRow label="Is On Telegram" value={phone.is_on_telegram ? 'Yes' : 'No'} />
          </View>
        ))}
      </InfoCard>

      <InfoCard title="Email Contacts">
        {(org.emails || []).map((email: any, index: number) => (
          <View key={index}>
            <InfoRow label="Email Type" value={email.email_type_name} />
            <InfoRow label="Email" value={email.email} />
          </View>
        ))}
      </InfoCard>

      <InfoCard title="Social Media">
        {(org.social_medias || []).map((social: any, index: number) => (
          <View key={index}>
            <InfoRow label="Platform" value={social.social_media_type_name} />
            <InfoRow label="Handle" value={social.social_media_handle} />
          </View>
        ))}
      </InfoCard>

      <InfoCard title="Company Addresses">
        {(org.addresses || []).map((addr: any, index: number) => (
          <View key={index}>
            <InfoRow label="Address Type" value={addr.address_type_name} />
            <InfoRow label="Address" value={addr.address} />
            <InfoRow label="Country" value={addr.country_name} />
            <InfoRow label="City" value={addr.city_name} />
            <InfoRow label="Postal Code" value={addr.postal_code} />
          </View>
        ))}
      </InfoCard>

      <InfoCard title="Financial Information">
        <InfoRow label="Annual Revenue" value={String(org.annual_revenue)} />
        <InfoRow label="Annual Growth Rate" value={String(org.annual_growth_rate)} />
        <InfoRow label="Currency" value={org.currency_name} />
      </InfoCard>

      {/* <InfoCard title="Online Presence">
        <InfoRow label="Facebook" value="<EMAIL>" />
      </InfoCard> */}

      <InfoCard title="Additional Information">
        <InfoRow label="Area" value={org.area_name} />
        <InfoRow label="Source" value={org.source_name} />
        <InfoRow label="Category" value={toPascalCase(org.category)} />
        <InfoRow label="Customer Type" value={org.customer_type} />
      </InfoCard>

      <InfoCard title="Business Information">
        <InfoRow label="Expected Business Value" value={String(org.expected_business_value)} />
        <InfoRow label="Decision Timeline" value={String(org.decision_timeline)} />
        <InfoRow label="Pain Points" value={org.pain_points} />
        <InfoRow label="Person Category" value={org.person_category_text} />
      </InfoCard>

      <InfoCard title="Decision Making Process">
        <InfoRow label="Key Decision Makers" value={org.key_decision_makers} />
        <InfoRow label="Decision Criteria" value={org.decision_criteria} />
      </InfoCard>

      <View style={{ height: 80 }} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  content: {
    flex: 1,
    padding: 16,
  },
  infoCard: {
    marginBottom: 16,
    borderRadius: 8,
    borderColor: '#E5E7EB',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  infoRowLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  infoRowValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
    maxWidth: '60%',
    textAlign: 'right',
  },
});

export default CompanyDetails;
