
import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import {
  MapPin,
  ArrowUpRight,
  Calendar,
  Clock,
  DollarSign,
  PackageCheck,
  Pencil,
  Copy,
  EllipsisVertical,
} from 'lucide-react-native';
import { formatDate, getStatusColor, getTransportIcon } from '../EquiryListUtils';
import { createStyles } from '../EnquiryDetail.styles';
import { moderateScale, scale } from 'react-native-size-matters';
import { Menu } from 'react-native-paper';
import { fontFamily } from '../../../../../../Common/Theme/typography';
const EnquiryCard = ({ item, index, onPressEnquiry, handleEnquiryCardNavigation, type }: any) => {
  const styles = createStyles();
  const [menuVisible, setMenuVisible] = useState(false);

  const openMenu = () => setMenuVisible(true);
  const closeMenu = () => setMenuVisible(false);

  const handleMenuAction = (action: string) => {
    closeMenu();
    handleEnquiryCardNavigation(item, action);
  };

  return (
    <TouchableOpacity
      onPress={() => {
        onPressEnquiry({
          enquiryId: item.enquiry_id,
          transportationMode: item.enquiry_type?.toLowerCase(),
        });
      }}
      style={styles.card}
      activeOpacity={0.7}
    >
      <View style={styles.cardHeader}>
        <View style={styles.cardHeaderLeft}>
          {getTransportIcon(item?.enquiry_type)}
          <View style={styles.cardHeaderText}>
            <Text style={styles.title} numberOfLines={1}>
              {item.organization_name || 'Unknown Organization'}
            </Text>
            <Text style={styles.subtitle}>
              {item.transportation_type} • {item.cargo_type_name}
            </Text>
          </View>
        </View>

        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item?.status_name) }]}>
          <Text style={styles.statusText}>{item?.status_name}</Text>
        </View>
        {
          type !== 2 && (

        <Menu
          visible={menuVisible}
          onDismiss={closeMenu}
          anchor={
            <TouchableOpacity
              style={{
                borderRadius: moderateScale(30),
                backgroundColor: '#EFF6FF',
                padding: scale(7),
                justifyContent: 'center',
                alignItems: 'center',
              }}
              onPress={openMenu}
            >
              <EllipsisVertical size={moderateScale(16)} color="#3377ff" />
            </TouchableOpacity>
          }
          contentStyle={{
            backgroundColor: 'white',
            borderRadius: moderateScale(8),
            elevation: 5,
            minWidth: moderateScale(70),
            // maxWidth: moderateScale(120),
            paddingVertical: moderateScale(4),
          }}
        >
          {
            item?.status_name?.toLowerCase() === 'new' && (

              <Menu.Item
                onPress={() => handleMenuAction('Detail')}
                title="Edit"
                leadingIcon={() => (
                  <Pencil
                    size={moderateScale(16)}
                    color="#4B5563" // slightly darker gray for visibility
                    style={{ marginTop: moderateScale(4) }}
                  />
                )}
                titleStyle={{
                  fontSize: moderateScale(13),
                  fontFamily: fontFamily.semiBold,
                  color: '#4B5563',
                }}
                style={{
                  minHeight: moderateScale(36),
                  // paddingHorizontal: moderateScale(12),
                  flexDirection: 'row',
                  alignItems: 'center',
                }}
              />
            )
          }
          <Menu.Item
            onPress={() => handleMenuAction('Copy')}
            title="Copy"
            leadingIcon={() => (
              <Copy
                size={moderateScale(16)}
                color="#4B5563"
                style={{ marginTop: moderateScale(4) }}
              />
            )}
            titleStyle={{
              fontSize: moderateScale(13),
              fontFamily: fontFamily.semiBold,
              color: '#4B5563',
            }}
            style={{
              minHeight: moderateScale(36),
              // paddingHorizontal: moderateScale(12),
              flexDirection: 'row',
              alignItems: 'center',
            }}
          />
        </Menu>
          )
        }

      </View>

      <View style={styles.routeContainer}>
        <View style={styles.routePoint}>
          <MapPin size={16} color="#10b981" />
          <View style={styles.routeText}>
            <Text style={styles.routeLabel}>From</Text>
            <Text style={styles.routeValue} numberOfLines={2}>
              {item.from_port_name || item.from_city_name || 'N/A'}
            </Text>
          </View>
        </View>

        <View style={styles.routePoint}>
          <MapPin size={16} color="#dc2626" />
          <View style={styles.routeText}>
            <Text style={styles.routeLabel}>To</Text>
            <Text style={styles.routeValue} numberOfLines={2}>
              {item.to_city_name || item.to_port_name || 'N/A'}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.detailsGrid}>
        <View style={styles.detailItem}>
          <Calendar size={16} color="#6b7280" />
          <View style={{ marginLeft: moderateScale(8) }}>
            <Text style={styles.detailLabel}>Ready Date</Text>
            <Text style={styles.detailValue}>{formatDate(item.ready_to_load)}</Text>
          </View>
        </View>

        {/* <View style={styles.detailItem}>
          <Clock size={16} color="#6b7280" />
          <View style={{ marginLeft: moderateScale(8) }}>
            <Text style={styles.detailLabel}>Transit</Text>
            <Text style={styles.detailValue}>{item.transit_time || 'N/A'} days</Text>
          </View>
        </View> */}

        {/* <View style={styles.detailItem}>
          <DollarSign size={16} color="#6b7280" />
          <View style={{ marginLeft: moderateScale(8) }}>
            <Text style={styles.detailLabel}>Value</Text>
            <Text style={styles.detailValue}>${item.cargo_value || 'N/A'}</Text>
          </View>
        </View> */}

        <View style={[styles.detailItem]}>
          <PackageCheck size={16} color="#6b7280" />
          <View style={{ marginLeft: moderateScale(8) }}>
            <Text style={styles.detailLabel}>Incoterm</Text>
            <Text style={styles.detailValue}>{item.incoterm_code || 'N/A'}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default EnquiryCard;
