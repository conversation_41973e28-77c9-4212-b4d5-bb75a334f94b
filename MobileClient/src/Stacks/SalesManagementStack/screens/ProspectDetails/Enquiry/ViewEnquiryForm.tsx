// ViewEnquiryForm.tsx
import React, { useEffect, useState } from 'react';
import { View, ScrollView, StyleSheet, ActivityIndicator, Text } from 'react-native';
import { useRoute } from '@react-navigation/native';
import { verticalScale, scale, moderateScale } from 'react-native-size-matters';
import { Info, MapPin, Package, FileText, List, Search } from 'lucide-react-native';
import { getSchemaName } from '../../../../../Common/Utils/Storage';
import ApiClient from '../../../../../Common/API/ApiClient';
import AppHeader from '../../../../../Components/AppHeader';
import { LEADS_ENDPOINTS } from '../../../../../Common/API/ApiEndpoints';
import { showToast } from '../../../../../Components/AppToaster/AppToaster';
import { fontFamily } from '../../../../../Common/Theme/typography';

export default function ViewEnquiryForm() {
  const route = useRoute();
  const { enquiryId, transportationMode } = route.params as {
    enquiryId: string;
    transportationMode: 'air' | 'road' | 'sea';
  };

  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchEnquiryDetails();
  }, []);

  const fetchEnquiryDetails = async () => {
    try {
      const schemaName = await getSchemaName();
      const payload = { schemaName, enquiryId };

      let endpoint = '';
      let key = '';

      switch (transportationMode) {
        case 'air':
          endpoint = LEADS_ENDPOINTS.GET_AIR_ENQUIRY;
          key = 'air_enquiry_details';
          break;
        case 'road':
          endpoint = LEADS_ENDPOINTS.GET_ROAD_ENQUIRY;
          key = 'road_enquiry_details';
          break;
        case 'sea':
        default:
          endpoint = LEADS_ENDPOINTS.GET_SEA_ENQUIRY;
          key = 'sea_enquiry_details';
          break;
      }

      const res = await ApiClient.post(endpoint, payload);
      setData(res.data?.data?.[0]?.[key] || null);
    } catch (err) {
      console.log('Error fetching enquiry details:', err);
      showToast.error('Error fetching enquiry details');
    } finally {
      setLoading(false);
    }
  };

  const combineCityAndCode = (name?: string, code?: string) => {
    if (!name && !code) return '--';
    if (name && code) return `${name}\n${code}`;
    return name || code || '--';
  };

  const getIconForTitle = (title: string) => {
    switch (title) {
      case 'Basic Information':
        return <Info size={moderateScale(16)} color="#FFFFFF" />;
      case 'Route Information':
        return <MapPin size={moderateScale(16)} color="#FFFFFF" />;
      case 'HS Code Category':
        return <Package size={moderateScale(16)} color="#FFFFFF" />;
      case 'Options & Line Items':
        return <List size={moderateScale(16)} color="#FFFFFF" />;
      case 'Additional Information':
        return <FileText size={moderateScale(16)} color="#FFFFFF" />;
      default:
        return <Info size={moderateScale(16)} color="#FFFFFF" />;
    }
  };

  const getBackgroundColorForTitle = (title: string) => {
    switch (title) {
      case 'Basic Information':
        return '#3B82F6';
      case 'Route Information':
        return '#10B981';
      case 'HS Code Category':
        return '#F59E0B';
      case 'Options & Line Items':
        return '#8B5CF6';
      case 'Additional Information':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  const InfoCard = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <View style={styles.infoCard}>
      <View style={[styles.cardHeader, {
        backgroundColor: getBackgroundColorForTitle(title)
      }]}>
        {getIconForTitle(title)}
        <Text style={styles.cardTitle}>{title}</Text>
      </View>
      <View style={styles.cardContent}>
        <View style={styles.table}>{children}</View>
      </View>
    </View>
  );

  const InfoRow = ({ label, value }: { label: string; value: string }) =>
    value && value !== 'null' && value !== 'undefined' ? (
      <View style={styles.tableRow}>
        <Text style={styles.cellLabel}>{label}</Text>
        <Text style={styles.cellValue} numberOfLines={0}>
          {value || '--'}
        </Text>
      </View>
    ) : null;

  if (loading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#3377FF" />
        <Text style={styles.loadingText}>Loading enquiry details...</Text>
      </View>
    );
  }

  if (!data) {
    return (
      <View style={styles.noDataContainer}>
        <AppHeader title="Enquiry Details" />
        <View style={styles.noDataContent}>
          <Search size={moderateScale(48)} color="#9CA3AF" />
          <Text style={styles.noDataText}>No data available.</Text>
        </View>
      </View>
    );
  }
  function pickFirstNonEmptyJson(...fields: (string | null | undefined)[]) {
    for (const f of fields) {
      if (f && f !== '[]' && f !== '{}') {
        try {
          return JSON.parse(f);
        } catch {
          return [];
        }
      }
    }
    return [];
  }
  const fieldLabels: Record<string, string> = {
  width: "Width",
  height: "Height",
  length: "Length",
  volume: "Volume",
  weight: "Weight",
  quantity: "Quantity",
  volumetric: "Volumetric",
  is_by_units: "Is By Units",
  gross_weight: "Gross Weight",
  package_number: "Package Number",
  per_unit_weight: "Per Unit Weight",
  package_type_name: "Package Type",
  stackability_name: "Stackability",
  container_type_name: "Container Type",
  truck_type_name: "Truck Type",
  qty_of_trucks: "Qty of Trucks",
  ship_type_name: "Ship Type",
  loading_rate: "Loading Rate",
  discharge_rate: "Discharge Rate",
};



  const optionsData = pickFirstNonEmptyJson(
    data?.crm_fcl_enquiry_items,
    data?.crm_lcl_enquiry_items,
    data?.crm_bulk_enquiry_items,
    data?.crm_uld_enquiry_items,
    data?.crm_standard_cargo_enquiry_items,
    data?.crm_ftl_enquiry_items,
    data?.crm_ltl_enquiry_items,
  );

  function getItemsFromOption(option: any) {
  return (
    option.fcl_enquiry_items ||
    option.lcl_enquiry_items ||
    option.bulk_enquiry_items ||
    option.uld_enquiry_items ||
    option.standard_cargo_enquiry_items ||
    option.ftl_enquiry_items ||
    option.ltl_enquiry_items ||
    []
  );
}


  const associatedServices = data?.associated_services ? JSON.parse(data?.associated_services) : [];

  // console.log("optionsData",optionsData[0]?.standard_cargo_enquiry_items)

  return (
    <View style={{ flex: 1, backgroundColor: '#F8FAFC' }}>
      <AppHeader title="Enquiry Details" />
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <InfoCard title="Basic Information">
          <InfoRow label="Organization" value={data.organization_name} />
          <InfoRow label="Direction" value={data.direction_name} />
          <InfoRow label="Transport By" value={data.transportation_type_name} />
          <InfoRow label="Carrier" value={data.carrier_name} />
          <InfoRow label="Cargo Type" value={data.cargo_type_name} />
          <InfoRow
            label="Cargo Value"
            value={`${data.cargo_value || '--'} ${data.currency_code}`}
          />
          <InfoRow label="Incoterm" value={`${data.incoterm_code} - ${data.incoterm_name}`} />
          <InfoRow label="Free Time" value={`${data.free_time} days`} />
          <InfoRow label="Unit System" value={data.unit_system_name} />
          <InfoRow label="UN Number" value={String(data.un_number || '--')} />
        </InfoCard>

        <InfoCard title="Route Information">
          <InfoRow label="From City" value={data.from_city_name} />
          <InfoRow label="From" value={combineCityAndCode(data.from_name, data.from_port_code)} />
          <InfoRow label="From Address" value={data.from_address} />
          <InfoRow label="To City" value={data.to_city_name} />
          <InfoRow label="To" value={combineCityAndCode(data.to_port_name, data.to_port_code)} />
          <InfoRow label="To Address" value={data.to_address} />
          <InfoRow label="Ready to Load" value={data.ready_to_load} />
          <InfoRow label="Transit Time" value={`${data.transit_time} days`} />
        </InfoCard>

        <InfoCard title="HS Code Category">
          <InfoRow label="Category Code" value={data.hc_code_category_code} />
          <InfoRow label="Category Name" value={data.hc_code_category_name} />
        </InfoCard>

          {/* <InfoCard title="Options & Line Items">
            {optionsData.map((opt: any, idx: number) => (
              <View key={idx} style={styles.optionBox}>
                <Text style={styles.optionTitle}>{opt.enquiry_option_name}</Text>
                {opt.enquiry_option_desc ? (
                  <Text style={styles.optionDesc}>{opt.enquiry_option_desc}</Text>
                ) : null}
                {(
                  opt.fcl_enquiry_items ||
                  opt.standard_cargo_enquiry_items ||
                  opt.ftl_enquiry_items ||
                  opt.lcl_enquiry_items
                )?.map((item: any, index: number) => (
                  <View key={index} style={styles.itemCard}>
                    <View style={styles.itemHeader}>
                      <Package size={moderateScale(14)} color="#6B7280" />
                      <Text style={styles.itemHeading}>Item {index + 1}</Text>
                    </View>
                    <View style={styles.divider} />

                    {item.width && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Width:</Text>
                        <Text style={styles.itemValue}>{item.width}</Text>
                      </View>
                    )}
                    {item.height && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Height:</Text>
                        <Text style={styles.itemValue}>{item.height}</Text>
                      </View>
                    )}
                    {item.length && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Length:</Text>
                        <Text style={styles.itemValue}>{item.length}</Text>
                      </View>
                    )}
                    {item.volume && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Volume:</Text>
                        <Text style={styles.itemValue}>{item.volume}</Text>
                      </View>
                    )}
                    {item.weight && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Weight:</Text>
                        <Text style={styles.itemValue}>{item.weight}</Text>
                      </View>
                    )}
                    {item.quantity && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Quantity:</Text>
                        <Text style={styles.itemValue}>{item.quantity}</Text>
                      </View>
                    )}
                    {item.volumetric && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Volumetric:</Text>
                        <Text style={styles.itemValue}>{item.volumetric}</Text>
                      </View>
                    )}
                    {item.is_by_units !== undefined && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Is By Units:</Text>
                        <Text style={styles.itemValue}>{item.is_by_units ? 'Yes' : 'No'}</Text>
                      </View>
                    )}
                    {item.gross_weight && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Gross Weight:</Text>
                        <Text style={styles.itemValue}>{item.gross_weight}</Text>
                      </View>
                    )}
                    {item.package_number && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Package Number:</Text>
                        <Text style={styles.itemValue}>{item.package_number}</Text>
                      </View>
                    )}
                    {item.per_unit_weight && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Per Unit Weight:</Text>
                        <Text style={styles.itemValue}>{item.per_unit_weight}</Text>
                      </View>
                    )}
                    {item.package_type_name && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Package Type:</Text>
                        <Text style={styles.itemValue}>{item.package_type_name}</Text>
                      </View>
                    )}
                    {item.stackability_name && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Stackability:</Text>
                        <Text style={styles.itemValue}>{item.stackability_name}</Text>
                      </View>
                    )}
                    {item.container_type_name && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Container Type:</Text>
                        <Text style={styles.itemValue}>{item.container_type_name}</Text>
                      </View>
                    )}
                    {item.truck_type_name && (
                      <View style={styles.itemDetailRow}>
                        <Text style={styles.itemLabel}>Truck Type:</Text>
                        <Text style={styles.itemValue}>{item.truck_type_name}</Text>
                      </View>
                    )}
                  </View>
                ))}
              </View>
            ))}
          </InfoCard> */}
          <InfoCard title="Options & Line Items">
  {optionsData.map((opt: any, idx: number) => (
    <View key={idx} style={styles.optionBox}>
      <Text style={styles.optionTitle}>{opt.enquiry_option_name}</Text>
      {opt.enquiry_option_desc ? (
        <Text style={styles.optionDesc}>{opt.enquiry_option_desc}</Text>
      ) : null}

      {getItemsFromOption(opt).map((item: any, index: number) => (
        <View key={index} style={styles.itemCard}>
          <View style={styles.itemHeader}>
            <Package size={moderateScale(14)} color="#6B7280" />
            <Text style={styles.itemHeading}>Item {index + 1}</Text>
          </View>
          <View style={styles.divider} />

          {Object.keys(fieldLabels).map((fieldKey) => {
            const value = item[fieldKey];
            if (value !== null && value !== undefined) {
              return (
                <View key={fieldKey} style={styles.itemDetailRow}>
                  <Text style={styles.itemLabel}>{fieldLabels[fieldKey]}:</Text>
                  <Text style={styles.itemValue}>
                    {fieldKey === "is_by_units"
                      ? value
                        ? "Yes"
                        : "No"
                      : value}
                  </Text>
                </View>
              );
            }
            return null;
          })}
        </View>
      ))}
    </View>
  ))}
</InfoCard>

          {associatedServices?.length > 0 ? (
            <InfoCard title="Associated Services">
              {associatedServices?.map((service, index) => (
                // <View key={index}></View>
                <>
                  <Text style={styles.itemLabel}>{service?.associated_services_name}</Text>
                </>
                // <InfoRow value={service?.associated_services_name} />
              ))}
            </InfoCard>
          ) : null}

          {data?.additional_information ? (
            <InfoCard title="Additional Information">
              <InfoRow label="Notes" value={data.additional_information} />
            </InfoCard>
          ) : null}

        <View style={{ height: verticalScale(80) }} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  loaderContainer: { 
    flex: 1, 
    justifyContent: 'center', 
    alignItems: 'center',
    backgroundColor: '#F8FAFC'
  },
  loadingText: {
    marginTop: verticalScale(16),
    fontSize: moderateScale(14),
    color: '#6B7280',
    fontFamily: fontFamily.medium
  },
  container: { 
    padding: scale(16),
    backgroundColor: '#F8FAFC'
  },
  infoCard: {
    marginBottom: verticalScale(16),
    borderRadius: scale(12),
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    paddingVertical: verticalScale(12),
    borderTopLeftRadius: scale(12),
    borderTopRightRadius: scale(12),
  },
  cardTitle: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: scale(8),
    fontFamily: fontFamily.semiBold
  },
  cardContent: {
    padding: scale(16),
  },
  table: {
    flexDirection: 'column',
    width: '100%',
  },
  tableRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: verticalScale(12),
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    alignItems: 'flex-start',
  },
  cellLabel: {
    fontSize: moderateScale(12),
    color: '#6B7280',
    width: '48%',
    flexWrap: 'wrap',
    fontFamily: fontFamily.medium,
    lineHeight: moderateScale(16),
  },
  cellValue: {
    fontSize: moderateScale(12),
    fontWeight: '500',
    color: '#1F2937',
    textAlign: 'right',
    width: '48%',
    flexWrap: 'wrap',
    fontFamily: fontFamily.medium,
    lineHeight: moderateScale(16),
  },
  optionBox: {
    marginBottom: verticalScale(16),
    padding: scale(16),
    backgroundColor: '#F9FAFB',
    borderRadius: scale(8),
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  optionTitle: {
    fontSize: moderateScale(14),
    fontWeight: '600',
    marginBottom: verticalScale(8),
    color: '#1F2937',
    fontFamily: fontFamily.semiBold
  },
  optionDesc: {
    fontSize: moderateScale(12),
    color: '#4B5563',
    marginBottom: verticalScale(12),
    fontFamily: fontFamily.medium,
    lineHeight: moderateScale(16),
  },
  itemCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: scale(8),
    borderColor: '#E5E7EB',
    borderWidth: 1,
    padding: scale(12),
    marginBottom: verticalScale(8),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: verticalScale(8),
  },
  itemHeading: {
    fontSize: moderateScale(13),
    fontWeight: '600',
    color: '#374151',
    marginLeft: scale(6),
    fontFamily: fontFamily.semiBold
  },
  divider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    marginVertical: verticalScale(8),
  },
  itemDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: verticalScale(6),
    alignItems: 'flex-start',
  },
  itemLabel: {
    fontSize: moderateScale(12),
    color: '#6B7280',
    width: '48%',
    flexWrap: 'wrap',
    fontFamily: fontFamily.medium,
  },
  itemValue: {
    fontSize: moderateScale(12),
    fontWeight: '500',
    color: '#1F2937',
    width: '48%',
    textAlign: 'right',
    flexWrap: 'wrap',
    fontFamily: fontFamily.medium,
  },
  noDataContainer: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  noDataContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: scale(16),
  },
  noDataText: {
    fontSize: moderateScale(16),
    color: '#6B7280',
    textAlign: 'center',
    marginTop: verticalScale(16),
    fontFamily: fontFamily.medium
  },
});