import React, { useMemo } from 'react';
import { View, StyleSheet, ScrollView, FlatList, TouchableOpacity } from 'react-native';
import { Text, TouchableRipple, Avatar, ActivityIndicator, Button } from 'react-native-paper';
import { Mail, Users, Briefcase, Pencil, CalendarDays } from 'lucide-react-native';
import {
  NavigationProp,
  ParamListBase,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { type ProspectDetailsParams, TabId, useProspectDetails } from './useProspectDetails';
import { ContactList } from './ContactList';
import ActivityList from './ActivityList';
import CompanyDetails from './CompanyDetails';
import { styles } from './Styles/ProspectDetailStyles';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import AppHeader from '../../../../Components/AppHeader';
import EnquiryDetail from './Enquiry/EnquiryDetail';
import JobsList from './Jobs/JobsList';
import Services from './Services/Services';
import AccountDetailsScreen from '../../../LeadsAppStack/Screens/AccountDetails/AccountDetailsScreen';
import QuotesList from './Quotes/QuotesList';
import Tabs from '../../../../Components/Tabs';
import CustomerOverView from '../../../CustomerStack/CustomerOverview/CustomerOverview';

export interface NavigationTypes {
  navigation: NavigationProp<ParamListBase>;
  route: RouteProp<ProspectDetailsParams, 'ProspectDetails'>;
  showThreeTabs?: boolean;
}

const ProspectDetails = () => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const route = useRoute<RouteProp<ProspectDetailsParams, 'ProspectDetails'>>();
  const { type: leadType,lead } = route.params?.leadDetails;
  let tabsList = ['activities', 'Company Details', 'contacts'];

  if(route.params?.showThreeTabs){
    tabsList = ['activities', 'Company Details', 'contacts'];
  } else {
    switch (leadType) {
      case 'Prospect':
        tabsList = ['activities', 'Company Details', 'contacts', 'Services'];
        break;
      case 'Opportunity':
        tabsList = [
          'Enquiries',
          'Quotes',
          'activities',
          'Company Details',
          'contacts',
          'Services',
          'Account Details',
        ];
        break;
      case 'Customer':
        tabsList = [
          'Overview',
          'Enquiries',
          'Quotes',
          'Jobs',
          'activities',
          'Company Details',
          'contacts',
          'Services',
          'Account Details',
          
        ];
        break;
      default:
        tabsList = ['activities', 'Company Details', 'contacts'];
    }
  }
  let newTabsList = tabsList.map(tab=>({"label":tab}))

  const {
    contacts,
    activities,
    handleTabChange,
    activeTab,
    getValue,
    details,
    loading,
    ageGroups,
    newContact,
    handleDeletePress,
  handleConfirmDelete,
  showDeleteModal,
  setShowDeleteModal,
  contactToDelete,
  setContactToDelete,
  companyDetails,
  fetchProspectDetails,
  fetchActivities
  } = useProspectDetails({ navigation, route, tabsList });

  const renderContent = () => {
    switch (activeTab) {
      case 'activities':
        return <ActivityList activities={activities} fetchActivities={fetchActivities} />;
      case 'contacts':
        return (
          <ContactList
            newContact={newContact}
            contactsData={contacts}
            ageGroups={ageGroups}
            onDelete={handleDeletePress}
            showDeleteModal={showDeleteModal}
            onConfirmDelete={handleConfirmDelete}
            onCancelDelete={() => {
              setShowDeleteModal(false);
              setContactToDelete(null);
            }}
          />
        );
      case 'Company Details':
        return (
          <View style={{ flex: 1 }}>
            <View style={styles.companyInfoRow}>
              <Text style={styles.companyInfo}>Company Information</Text>
              <TouchableOpacity
                onPress={() =>
                  navigation.navigate('CompanyDetailsForm', {
                    companyDetails,
                    org_id: lead?.orgId,
                    fetchProspectDetails,
                  })
                }
                style={styles.editButton}
              >
                <Pencil size={moderateScale(16)} color="#2563EB" />
              </TouchableOpacity>
            </View>
            <CompanyDetails getValue={getValue} companyDetails={companyDetails} />
          </View>
        );
      case 'Services':
        return <Services orgId={lead.orgId || ''} navigation={navigation} />;
      case 'Enquiries':
        return <EnquiryDetail navigation={navigation} type={1} />;
        case 'Quotes':
          return <QuotesList navigation={navigation} type={2} />;
        // case 'Quotes':
        //   return <EnquiryDetail navigation={navigation} type={2} />;
        case 'Jobs':
        return <JobsList navigation={navigation} />;
      case 'Account Details':
        return <AccountDetailsScreen org_id={lead.orgId || ''} navigation={navigation} />;
        case 'Overview':
          return <CustomerOverView org_id={lead.orgId} />
      default:
        return <CompanyDetails getValue={getValue} companyDetails={companyDetails?.org_details} />;
    }
  };

  const leadStateChipTextStyle = useMemo(() => {
    const getLeadStateChipTextStyleWeb = (type: string) => {
      if (type === 'Opportunity') return { color: '#9B6200', backgroundColor: '#FCFAD9' };
      if (type === 'Customer') return { color: '#15803d', backgroundColor: '#f0fdf4' };
      if (type === 'Prospect') return { color: '#7B2FD0', backgroundColor: '#faf5ff' };
      return { color: '#333', backgroundColor: '#e0e0e0' };
    };
    return getLeadStateChipTextStyleWeb(route.params.leadDetails.lead.leadStateName || '');
  }, [route.params.leadDetails.lead.leadStateName]);

  return (
    <View style={styles.container}>
      <AppHeader
        title="Lead Details"
        menuVisible={false}
        showMenu={false}
        showPeopleHirerachy={false}
        showControlUnit={false}
        customHeaderStyles={{ backgroundColor: '#FFFFFF' }}
      />
      <View style={styles.companyHeader}>
        {leadType && <View style={styles.actionIcons}>
          <TouchableOpacity
            onPress={() =>
              navigation.navigate('CreateActivityScreen', {
                leadId: route.params?.leadId,
                leadDetails: { lead },
                refreshForActivityTabs:fetchActivities
              })
            }
            style={styles.iconButton}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <CalendarDays size={moderateScale(18)} color="#2563EB" />
          </TouchableOpacity>
        </View>}
        <Avatar.Text
          label={(
            String(route.params?.leadDetails?.company)?.[0] ??
            String(details?.company_name?.trim())?.[0] ??
            ''
          ).toUpperCase()}
          size={moderateScale(35)}
          style={styles.avatar}
          labelStyle={{ color: '#2563EB' }}
        />
        <View style={{ flex: 1, marginLeft: scale(10) }}>
          <Text style={styles.companyName}>
            {details?.company_name || route.params?.leadDetails?.company || '--'}
          </Text>
          {/* {!!route.params?.leadDetails?.lead?.email && (
            <View style={styles.chipsRow}>
              <View style={styles.inlineItem}>
                <Mail size={14} color="#6B7280" />
                <Text style={styles.inlineText}>{route.params.leadDetails.lead.email}</Text>
              </View>
            </View>
          )} */}
          <View style={styles.chipsRow}>
            {!!route.params?.leadDetails?.lead?.leadStateName && (
              <Text style={[styles.opportunityChip, leadStateChipTextStyle]}>
                {route.params.leadDetails.lead.leadStateName}
              </Text>
            )}
            {!!route.params?.leadDetails?.lead?.prospectTypeName && (
              <Text style={styles.typeChip}>{route.params.leadDetails.lead.prospectTypeName}</Text>
            )}
          </View>
          {!!route.params?.leadDetails?.category && (
            <View style={styles.chipsRow}>
              <View style={styles.inlineItem}>
                <Briefcase size={14} color="#6B7280" />
                <Text style={styles.inlineText}>{route.params.leadDetails.category}</Text>
              </View>
            </View>
          )}

          <View style={styles.chipsRow}>
            {!!route.params?.leadDetails?.lead?.contactName && (
              <View style={styles.inlineItem}>
                <Users size={14} color="#6B7280" />
                <Text style={styles.inlineText}>{route.params.leadDetails.lead.contactName}</Text>
              </View>
            )}
            {/* {!!route.params?.leadDetails?.category && (
              <View style={styles.inlineItem}>
                <Briefcase size={14} color="#6B7280" />
                <Text style={styles.inlineText}>{route.params.leadDetails.category}</Text>
              </View>
            )} */}
          </View>
          {!!route.params?.leadDetails?.lead?.email && (
            // <View style={styles.chipsRow}>
            <View style={styles.inlineItem}>
              <Mail size={14} color="#6B7280" />
              <Text style={styles.inlineText}>{route.params.leadDetails.lead.email}</Text>
            </View>
            // </View>
          )}
        </View>
      </View>

      {/* <View style={styles.tabContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabBar}
        >
        {(tabsList as TabId[]).map((tab) => (
          <TouchableRipple
            key={tab}
            onPress={() => handleTabChange(tab)}
            style={[styles.tabButton, activeTab === tab && styles.activeTab]}
          >
            <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
              {tab.charAt(0).toUpperCase() + tab?.slice(1)}
            </Text>
          </TouchableRipple>
        ))}
        </ScrollView>
      </View> */}
      {/* {tabsList.length < 5 ? (
        <View
          style={[
            {
              justifyContent: 'space-between',
              marginHorizontal: scale(16),
              flexDirection: 'row',
              alignItems: 'center',
              borderColor: '#E4E4E7',
              borderBottomWidth: 1,
            },
          ]}
        >
          {(tabsList as TabId[]).map((tab) => (
            <TouchableRipple
              key={tab}
              onPress={() => handleTabChange(tab)}
              style={[
                styles.tabButtonFor3,
                activeTab === tab && styles.activeTab,
                // { height: verticalScale(25), justifyContent: 'center', width: scale(50) },
              ]}
            >
              <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </Text>
            </TouchableRipple>
          ))}
        </View>
      ) : (
        <View style={{}}>
          <FlatList
            data={tabsList as TabId[]}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item) => item}
            style={{
              marginHorizontal: scale(16),
              height: verticalScale(30),
              borderColor: '#E4E4E7',
              borderBottomWidth: 1,
            }}
            renderItem={({ item }: { item: TabId }) => (
              <TouchableRipple
                key={item}
                onPress={() => handleTabChange(item)}
                style={[
                  styles.tabButton,
                  activeTab === item && styles.activeTab,
                  // { height: verticalScale(25), justifyContent: 'center' },
                ]}
              >
                <Text style={[styles.tabText, activeTab === item && styles.activeTabText]}>
                  {item.charAt(0).toUpperCase() + item.slice(1)}
                </Text>
              </TouchableRipple>
            )}
          />
        </View>
      )} */}
      <Tabs tabs={newTabsList} onTabPress={handleTabChange} activeTab={activeTab} labelKey='label' valueKey='label'/>
      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator animating={true} size="large" color="#2563EB" />
        </View>
      ) : (
        renderContent()
      )}
    </View>
  );
};

export default ProspectDetails;
