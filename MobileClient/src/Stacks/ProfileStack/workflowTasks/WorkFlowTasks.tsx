import React, { useEffect, useState,useMemo } from 'react';
import { View, FlatList, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import ApiClient from '../../../Common/API/ApiClient';
import { getAccountId, getSchemaName, getSelectedWorkspaceId } from '../../../Common/Utils/Storage';
import { useSelector } from 'react-redux';
import AppHeader from '../../../Components/AppHeader';
import { RootState } from '../../../State/Store';
import DynamicUI from '../../../Utils/WorkFlowRender';
import { File, ChevronDown, ChevronUp, FileText } from 'lucide-react-native';
import { moderateScale } from 'react-native-size-matters';
import SearchBar from '../../../Components/SearchInput';
import { WORKFLOW_ENDPOINTS } from '../../../Common/API/ApiEndpoints';

const WorkFlowTasks = () => {
  const [tasks, setTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedId, setExpandedId] = useState<string | null>(null);
  const [dropdownData, setDropdownData] = useState<{ [key: string]: any }>({});
  const userID = useSelector((state: RootState) => state.userId);

  useEffect(() => {
    fetchTasks();
  }, []);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      const schemaName = await getSchemaName();
      const workspace_id = await getSelectedWorkspaceId();
      const account_id = await getAccountId();

      const payload = {
        context: {
          account_id,
          workspace_id: null,
          contriol_unit_id: null,
          user_id: userID,
          schemaName: 'c1s1_billing',
        },
      };

      const response = await ApiClient.post(WORKFLOW_ENDPOINTS.WORKFLOW_TASKS, payload);
      console.log("response",response)
      setTasks(response.data?.data || []);
    } catch (error) {
      console.error('Error fetching tasks:', error);
      setTasks([]);
    } finally {
      setLoading(false);
    }
  };



const handleCardPress = async (item: any) => {
  const itemKey = item.workflowTaskId;
  if (expandedId === itemKey) {
    setExpandedId(null);
    return;
  }

  setExpandedId(itemKey);

  if (!dropdownData[itemKey]) {
    try {
      const schemaName = 'Editor_Schema_dev';
      const url = `/getMastersDropDown/${schemaName}/page_ref?columns=json_data&page_id=${item.screenId}`;
      const response = await ApiClient.get(url);
      const parsedData = JSON.parse(response.data[0]?.json_data || '[]');
      setDropdownData(prev => ({ ...prev, [itemKey]: parsedData }));
    } catch (error) {
      console.error('Error fetching dropdown:', error);
    }
  }
};

  const getBadgeStyle = (status: string) => {
    switch ((status || '').toLowerCase()) {
      case 'approved':
        return { backgroundColor: '#e0f7e9', color: 'green' };
      case 'rejected':
        return { backgroundColor: '#fde2e1', color: 'red' };
      case 'pending':
      default:
        return { backgroundColor: '#fff4e0', color: 'orange' };
    }
  };

  const renderItem = ({ item }: { item: any }) => {
    const badgeStyle = getBadgeStyle(item.currentStatus);
    const isExpanded = expandedId === item.workflowTaskId;

    return (
      <View style={styles.card}>
        <TouchableOpacity style={styles.row} onPress={() => handleCardPress(item)}>
          <View style={styles.left}>
            <FileText size={moderateScale(18)} color="#3377ff" />
            <View style={{ marginLeft: 8 }}>
              <Text variant="medium" style={styles.title}>
                {item.workflowTaskName || 'Untitled Task'}
              </Text>
              <Text variant="regular" style={styles.subtitle}>
                {item.createdAt} •<Text style={{ color: '#3377ff' }}> {item.screenType}</Text>
              </Text>
            </View>
          </View>

          <View style={styles.right}>
            <View style={[styles.badge, { backgroundColor: badgeStyle.backgroundColor }]}>
              <Text
                variant="medium"
                style={{ color: badgeStyle.color, fontSize: moderateScale(11) }}
              >
                {item.currentStatus || 'pending'}
              </Text>
            </View>
            {!isExpanded ? (
              <ChevronDown size={22} color="#555" />
            ) : (
              <ChevronUp size={22} color="#555" />
            )}
          </View>
        </TouchableOpacity>
        {isExpanded && (
          <View style={styles.expandArea}>
            {dropdownData[item.workflowTaskId] ? (
              <DynamicUI
                data={dropdownData[item.workflowTaskId]}
                schemaId = {item.schemaId} 
              />
            ) : (
              <ActivityIndicator size="small" style={{ marginTop: 8 }} />
            )}
          </View>
        )}
      </View>
    );
  };
const [searchText, setSearchText] = useState('');

// Add filtered tasks based on search
const filteredTasks = useMemo(() => {
  if (!searchText.trim()) {
    return tasks;
  }

  return tasks.filter((task) =>
    task.workflowTaskName?.toLowerCase().includes(searchText.toLowerCase())
  );
}, [tasks, searchText]);
  
const theme = useTheme()
return (
  <>
    <AppHeader title="Workflow Tasks" />
    <View style={{ flex: 1, backgroundColor: theme.colors.background, paddingTop: 10 }}>
      <SearchBar
        style={{ marginHorizontal: moderateScale(10) }}
        placeholder="Search tasks..."
        onChangeText={setSearchText}
        value={searchText}
      />
      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator animating size="large" />
        </View>
      ) : filteredTasks.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text variant="medium" style={styles.emptyText}>
            {searchText.trim() ? 'No tasks found matching your search' : 'No tasks available'}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredTasks}
          renderItem={renderItem}
          keyExtractor={(item) => item.workflowTaskId?.toString()}
          contentContainerStyle={styles.container}
        />
      )}
    </View>
  </>
);
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
  },
  card: {
    marginBottom: 10,
    borderRadius: 12,
    backgroundColor: '#fff',
    padding: 10,
    elevation: 2,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  left: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  right: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    // fontWeight: '600',
    fontSize: moderateScale(12),
  },
  subtitle: {
    fontSize: moderateScale(11),
    color: 'gray',
  },
  badge: {
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  expandArea: {
    marginTop: 10,
    padding: 8,
    backgroundColor: '#fafafa',
    borderRadius: 8,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    color: 'gray',
    fontSize: 16,
  },
});

export default WorkFlowTasks;
