import { getEnvConfig } from '../../../../features/chat/api/chatCredentials';

import AWS from 'aws-sdk';
import mime from 'mime';
import {Buffer} from 'buffer';
import RNFS from 'react-native-fs';

/**
 * Uploads files of any type to AWS S3.
 * @param files Array of files to upload. Each file should have:
 *   - `uri` (string): The file URI or base64 data.
 *   - `fileName` (string): The name of the file.
 *   - `type` (string): The MIME type of the file (optional).
 * @returns Array of uploaded files with their S3 URLs.
 */

export const uploadToS3 = async (
  files: any[],
  setUploading: (val: boolean) => void,
  setProgress: (val: number) => void,
  s3_bucket_name:string = ''
) => {
  if (Array.isArray(files) && !files.length) {
    return 'No files selected!';
  }
  const {
    AWS_CHAT_BUCKET_NAME,
    AWS_REGION,
    AWS_ACCESS_KEY,
    AWS_SECRET_KEY
  } = await getEnvConfig();
  if (!AWS_CHAT_BUCKET_NAME || !AWS_REGION || !AWS_ACCESS_KEY || !AWS_SECRET_KEY) {
    throw new Error('AWS configuration is missing.');
  }
  console.log("AWS_CHAT_BUCKET_NAME", AWS_CHAT_BUCKET_NAME)
  if (!s3_bucket_name){
    s3_bucket_name = AWS_CHAT_BUCKET_NAME ?? ''
  }

  // Configure AWS SDK
  AWS.config.update({
    region: AWS_REGION,
    accessKeyId: AWS_ACCESS_KEY,
    secretAccessKey: AWS_SECRET_KEY,
  });
  
  const s3 = new AWS.S3();

  setUploading(true); 

  const uploadedFiles = await Promise.all(
    files.map(async (file, index) => {
      try {
        if (!file.name) {
          throw new Error('File name not available.');
        }

        const fileType =
          file.mime_type ||
          mime.getType(file.uri) ||
          'application/octet-stream';
        const fileName =
          `${Date.now()}_${file.name.replace(/[^a-zA-Z0-9.]/g, '_')}` ||
          file.uri.split('/').pop() ||
          `file-${Date.now()}`;
        const fileContent = await RNFS.readFile(file.uri, 'base64');
        const fileBody = Buffer.from(fileContent, 'base64');

        const params = {
          Bucket: s3_bucket_name,
          Key: fileName,
          Body: fileBody,
          ContentType: fileType,
          // ACL: 'public-read',
        };

        const upload = s3.upload(params);

        upload.on('httpUploadProgress', progress => {
          const percent = Math.round((progress.loaded / progress.total) * 100);
          setProgress(percent); 
        });

        const uploadResult = await upload.promise();
        console.log('upload result in aws', uploadResult);
        return {
          ...file,
          url: uploadResult.Location,
        };
      } catch (error) {
        console.log('S3 Upload Error:', error);
        return {...file, url: undefined};
      }
    }),
  );

  setUploading(false); 
  return uploadedFiles;
};


// below code for upgrade for aws client version 3 (but it is not supported the uploading feature)

// import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
// import RNFS from 'react-native-fs';
// import mime from 'mime';
// import {Buffer} from 'buffer';
// import {awsConfigurations} from '../../chatCredentials';

// const {AWS_BUCKET_NAME, AWS_ACCESS_KEY, AWS_SECRET_KEY, AWS_REGION} = awsConfigurations;

// // Configure AWS SDK v3
// const s3Client = new S3Client({
//   region: AWS_REGION,
//   credentials: {
//     accessKeyId: AWS_ACCESS_KEY,
//     secretAccessKey: AWS_SECRET_KEY,
//   },
// });

// const getFileType = (fileName: string): string => {
//   return mime.getType(fileName) || 'application/octet-stream';
// };

// const uploadToS3 = async (
//   files: any[],
//   setUploading?: (val: boolean) => void,
//   setProgress?: (val: number) => void,
// ): Promise<{
//   extension: string;
//   mime_type: string;
//   name: string;
//   size: string | number;
//   url: string;
// }[]> => {
//   try {
//     if (setUploading) setUploading(true);

//     if (Array.isArray(files) && !files.length) {
//       if (setUploading) setUploading(false);
//       throw new Error('No files selected!');
//     }

//     const uploadedUrls: {
//       extension: string;
//       mime_type: string;
//       name: string;
//       size: string | number;
//       url: string;
//     }[] = [];

//     for (let i = 0; i < files.length; i++) {
//       const fileObj = files[i];
//       const fileContent = await RNFS.readFile(fileObj.uri, 'base64');
//       const fileBuffer = Buffer.from(fileContent, 'base64');

//       const fileName = fileObj.name || fileObj.uri.split('/').pop() || `file-${Date.now()}`;
//       const fileKey = `${Date.now()}_${fileName.replace(/[^a-zA-Z0-9.]/g, '_')}`;
//       const mimeType = fileObj.mime_type || getFileType(fileName);

//       console.log('Uploading file:', fileName, 'as', fileKey);

//       // Get file size
//       const fileInfo = await RNFS.stat(fileObj.uri);
//       const size = fileInfo.size || 0;

//       const response = await s3Client.send(
//         new PutObjectCommand({
//           Bucket: AWS_BUCKET_NAME,
//           Key: fileKey,
//           Body: fileBuffer,
//           ContentType: mimeType,
//           ACL: 'public-read'
//         })
//       );

//       // Unfortunately, AWS SDK v3 doesn't support progress events in the same way as v2
//       // We could simulate progress here if needed
//       if (setProgress) {
//         setProgress(((i + 1) / files.length) * 100);
//       }

//       const extension = fileName.split('.').pop() || '';
//       const url = `https://${AWS_BUCKET_NAME}.s3.${AWS_REGION}.amazonaws.com/${fileKey}`;

//       const fileData = {
//         extension,
//         mime_type: mimeType,
//         name: fileName,
//         size,
//         url
//       };

//       console.log('Upload response:', response);
//       uploadedUrls.push(fileData);
//       console.log('uploadedUrls', uploadedUrls);
//     }

//     if (setUploading) setUploading(false);
//     return uploadedUrls;
//   } catch (error) {
//     console.log('S3 upload error:', error);
//     if (setUploading) setUploading(false);
//     throw error;
//   }
// };

// export { uploadToS3 };
