import React, {useState, useCallback, useEffect} from 'react';
import {<PERSON>, ScrollView, Alert, StatusBar, Text} from 'react-native';
import {
  useTheme,
  Divider,
  ActivityIndicator,
  Surface,
} from 'react-native-paper';
import {useQuery} from '@apollo/client';
import {
  GET_EMAIL_MESSAGE,
  GET_MESSAGES_BY_THREAD_ID,
} from '../EmailGraphql/EmailQueries';
import {
  EmailDetailScreenProps,
  EmailMessage,
  EmailAttachment,
} from '../Types/EmailDetailTypes';
import EmailDetailHeader from '../Components/EmailDetailHeader';
import EmailContent from '../Components/EmailContent';
import EmailAttachments from '../Components/EmailAttachments';
import EmailReplyInput from '../Components/EmailReplyInput';
import ThreadList from '../Components/ThreadList';
import createEmailDetailStyles from '../Styles/EmailDetailStyles';
import SafeContainerView from '../../../Components/SafeContainer';

const EmailDetail: React.FC<EmailDetailScreenProps> = ({navigation, route}) => {
  const theme = useTheme();
  const styles = createEmailDetailStyles(theme);
  const userId = '81437d5a-30b1-703d-af41-e9509e85d990';
  // const mailId = '<EMAIL>';
  // State
  const [email, setEmail] = useState<EmailMessage | null>(null);
  const [threadMessages, setThreadMessages] = useState<EmailMessage[]>([]);

  // Get email ID and data from route params
  const emailId = route?.params?.emailId;
  const threadId = route?.params?.threadId;
  const emailFromParams = route?.params?.email;
  const mailId = route?.params?.mailId;
  // console.log("maildId",mailId)
  // Query for email details if not provided in params
  const {data: emailData, loading: emailLoading} = useQuery(GET_EMAIL_MESSAGE, {
    variables: {
      user_id: userId,
      email_date_message_id: emailId,
    },
    skip: !emailId || !!emailFromParams,
  });

  // Query for thread messages
  const {data: threadData, loading: threadLoading} = useQuery(
    GET_MESSAGES_BY_THREAD_ID,
    {
      variables: {
        mail_id: mailId,
        thread_id: threadId ?? '',
      },
      skip: !threadId || !mailId,
    },
  );

  // Set email from params or query
  useEffect(() => {
    if (emailFromParams) {
      setEmail(emailFromParams);
    } else if (emailData?.getEmailMessage) {
      setEmail(emailData.getEmailMessage);
    }
  }, [emailFromParams, emailData]);

  // Set thread messages from query
  useEffect(() => {
    // console.log("thread data", threadData)
    if (threadData?.getMessagesByThreadId) {
      setThreadMessages(threadData.getMessagesByThreadId);
    }
  }, [threadData]);

  // Handle back button press
  const handleBack = useCallback(() => {
    navigation?.goBack();
  }, [navigation]);

  // Handle archive button press
  const handleArchive = useCallback(() => {
    Alert.alert('Archive', 'Email archived');
  }, []);

  // Handle delete button press
  const handleDelete = useCallback(() => {
    Alert.alert('Delete Email', 'Are you sure you want to delete this email?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Delete',
        onPress: () => {
          // Delete logic would go here
          Alert.alert('Deleted', 'Email deleted');
          navigation?.goBack();
        }, 
        style: 'destructive',
      },
    ]);
  }, [navigation]);

  // Handle history button press
  const handleHistory = useCallback(() => {
    Alert.alert('History', 'View email history');
  }, []);

  // Handle reply button press
  const handleReply = useCallback(() => {
    if (email) {
      navigation?.navigate('EmailCompose', {
        messageType: 'reply',
        originalMessage: email,
        threadId: email.thread_id,
      });
    }
  }, [email, navigation]);

  // Handle reply all button press
  const handleReplyAll = useCallback(() => {
    if (email) {
      navigation?.navigate('EmailCompose', {
        messageType: 'replyAll',
        originalMessage: email,
        threadId: email.thread_id,
      });
    }
  }, [email, navigation]);

  // Handle forward button press
  const handleForward = useCallback(() => {
    if (email) {
      navigation?.navigate('EmailCompose', {
        messageType: 'forward',
        originalMessage: email,
        threadId: email.thread_id,
      });
    }
  }, [email, navigation]);

  // Handle share button press
  const handleShare = useCallback(() => {
    Alert.alert('Share', 'Share email');
  }, []);

  // Handle more button press
  const handleMore = useCallback(() => {
    Alert.alert('More Options', 'More email options');
  }, []);

  // Handle attachment press
  const handleAttachmentPress = useCallback((attachment: EmailAttachment) => {
    Alert.alert('Attachment', `Opening ${attachment.attachment_file_name}`);
  }, []);

  // If email is not loaded yet, show loading state
  if (!email && (emailLoading || !emailId)) {
    return (
      // <SafeContainerView
      //   backgroundColor={theme.colors.primary}
      //   style={{paddingTop: 0}}>
      //   <StatusBar
      //     barStyle="light-content"
      //     backgroundColor={theme.colors.primary}
      //     translucent={false}
      //     hidden={false}
      //   />
      //   <View style={styles.container}>
      //      <EmailDetailHeader
      //       email={
      //         {
      //           sender_name: 'Loading...',
      //           sender_mail: '',
      //           email_subject: 'Loading...',
      //           email_date: new Date().toISOString(),
      //           latest_message: '',
      //         } as EmailMessage
      //       }
      //       onBack={handleBack}
      //       onArchive={handleArchive}
      //       onDelete={handleDelete}
      //       onHistory={handleHistory}
      //       onReply={handleReply}
      //       onReplyAll={handleReplyAll}
      //       onForward={handleForward}
      //       onShare={handleShare}
      //       onMore={handleMore}
      //     /> 
        
      //    </View>
      // </SafeContainerView> 

      <View
      style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
      <ActivityIndicator size="large" color={theme.colors.primary} />
    </View>
    );
  }

  return (
    <SafeContainerView
      backgroundColor={theme.colors.primary}
      style={{paddingTop: 0}}>
      <StatusBar
        // barStyle="light-content"
        backgroundColor={theme.colors.primary}
        translucent={false}
        hidden={false}
      />
      <View style={styles.container}>
        <EmailDetailHeader
          email={email as EmailMessage}
          onBack={handleBack}
          onArchive={handleArchive}
          onDelete={handleDelete}
          onHistory={handleHistory}
          onReply={handleReply}
          onReplyAll={handleReplyAll}
          onForward={handleForward}
          onShare={handleShare}
          onMore={handleMore}
          threadMessages = {threadMessages}
        />

        {/* <ScrollView style={styles.scrollView}> */}
        <ThreadList
          messages={threadMessages}
          loading={threadLoading}
          onReply={handleReply}
          onReplyAll={handleReplyAll}
          onForward={handleForward}
          onAttachmentPress={handleAttachmentPress}
        />
        {/* </ScrollView> */}
      </View>
    </SafeContainerView>
  );
};

export default EmailDetail;
