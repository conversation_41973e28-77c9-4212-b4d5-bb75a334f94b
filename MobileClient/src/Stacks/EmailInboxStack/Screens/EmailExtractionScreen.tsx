import { View, ScrollView } from 'react-native';
import React, { useEffect, useState } from 'react';
import emailExtractionStyles from '../Styles/EmailExtraction.Styles';
import { useRoute } from '@react-navigation/native';
import ApiClient from '../../../../src/Common/API/ApiClient';
import { Text, Card, ActivityIndicator } from 'react-native-paper';
import AppHeader from '../../../../src/Components/AppHeader';
import CustomInput from '../../../Components/UI/TextInput';
import EmptyorErrorComponent from '../../../Components/EmptyOrError/EmptyOrErrorComponent';
import Config from 'react-native-config';
import { sampleEnquiryData } from '../Utils/SampleTestData';
const EmailExtractionScreen = () => {
  const [enquiryLabelsData, setEnquiryLabelsData] = useState({});
  const [enquiryLabelError, setEnquiryLabelError] = useState(null);
  const [loading, setLoading] = useState(true);
  
  const styles = emailExtractionStyles();
  const route = useRoute<any>();
  
  const { transportationType, enquiryType, threadMessages } = route.params ?? {
    transportationType: null,
    enquiryType: null,
  };

  const API_EMAIL_EXTRACTION = Config.API_EMAIL_EXTRACTION 
    ? `${Config.API_EMAIL_EXTRACTION}/process-thread`
    : 'https://fopwhdqcsa.execute-api.ap-south-1.amazonaws.com/api/process-thread';

  const fetchEmailExtraction = async () => {
    try {
      setLoading(true);
      const payload = {
        transport_type: transportationType?.toLowerCase(),
        transport_mode: enquiryType?.toLowerCase(),
        emails: threadMessages || []
      };

      const response = await ApiClient.post(API_EMAIL_EXTRACTION, payload);
      console.log("Response of email extraction api data", response?.data?.entities?.enquiry_fields);
      setEnquiryLabelsData(response?.data?.entities?.enquiry_fields || {});
      setEnquiryLabelError(null);
    } catch (error) {
      console.log('Error in fetch email extraction', error);
      setEnquiryLabelError(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEmailExtraction();
  }, []);

  const capitalizeLabel = (label: string) => {
    return label
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // const renderLCLUnitsDetails = (lclData: any) => {
  //   if (!lclData || typeof lclData !== 'object') return null;

  //   return (
  //     <CustomCard 
  //       title="LCL By Units Details" 
  //       style={styles.sectionCard}
  //     >
  //       <View style={styles.cardContent}>
  //         {Object.entries(lclData).map(([subKey, subValue]) => (
  //           <CustomInput
  //             key={subKey}
  //             label={capitalizeLabel(subKey)}
  //             value={subValue?.toString() || ''}
  //             editable={false}
  //             style={styles.input}
  //           />
  //         ))}
  //       </View>
  //     </CustomCard>
  //   );
  // };

  const renderBasicDetails = () => {
    const basicFields = Object.entries(sampleEnquiryData).filter(
      ([key]) => key !== 'lcl_by_units_details'
    );


    return (

        <View style={styles.cardContent}>
          {basicFields.map(([key, value]) => {
            // Skip nested objects
            if (typeof value === 'object' && value !== null) return null;

            return (
              <CustomInput
                key={key}
                label={capitalizeLabel(key)}
                value={value?.toString() || ''}
                style={styles.input}
              />
            );
          })}
        </View>
    );
  };

  const renderContent = () => {
    // if (loading) {
    //   return (
    //     <View style={styles.loadingContainer}>
    //       <ActivityIndicator size="large" color="#007AFF" />
    //       <Text style={styles.loadingText}>Processing email data...</Text>
    //     </View>
    //   );
    // }

    // if (enquiryLabelError) {
    //   return (
    //     <EmptyorErrorComponent
    //       message="There was an error processing the email thread. Please try again."
        
    //     />
    //   );
    // }

    // if (Object.keys(enquiryLabelsData).length === 0) {
    //   return (
    //     <EmptyorErrorComponent
    //       message="No shipment details were extracted from the email thread."
      
    //     />
    //   );
    // }

    return (
      <View style={styles.contentContainer}>
        <Text style={styles.subHeader}>
          Transportation: {transportationType} | Mode: {enquiryType}
        </Text>
        
        {renderBasicDetails()}
        {/* {enquiryLabelsData.lcl_by_units_details && 
          renderLCLUnitsDetails(enquiryLabelsData.lcl_by_units_details)
        } */}
      </View>
    );
  };

  return (
      <View style={styles.container}>
        <AppHeader title="Email Extraction" />
        <ScrollView 
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {renderContent()}
        </ScrollView>
      </View>

  );
};

export default EmailExtractionScreen;