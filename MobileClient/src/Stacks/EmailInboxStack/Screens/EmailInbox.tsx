import React, { useState, useCallback, useRef, useEffect } from 'react';
import { StyleSheet, View, Animated, StatusBar, Dimensions } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme, FAB as PaperFAB } from 'react-native-paper';
import { useSelector } from 'react-redux';
import SafeContainerView from '../../../Components/SafeContainer';
import { useEmails } from '../Hooks/useEmails';
import { useEnhancedEmailSearch } from '../Hooks/useEnhancedEmailSearch';
import EnhancedEmailHeader from '../Components/EnhancedEmailHeader';
import AnimatedEmailList from '../Components/AnimatedEmailList';
import EmailFilters from '../Components/EmailFilters';
import EmailAccountSelector from '../Components/EmailAccountSelector';
import { EmailMessage } from '../Types/EmailTypes';
import AppHeader from '../../../Components/AppHeader';
import { SearchIcon } from 'lucide-react-native';
import { moderateScale, scale } from 'react-native-size-matters';
import { TextInput, TouchableOpacity } from 'react-native';
// import { useRef, useEffect, useState } from 'react';
import { Search, X } from 'lucide-react-native'; // swap for your icon library
import SearchBar from '../../../Components/SearchInput';
const { width } = Dimensions.get('window');
const EmailInbox = () => {
  const navigation = useNavigation();
  const theme = useTheme();
  const userId =
    useSelector((state: any) => state.userId) || '81437d5a-30b1-703d-af41-e9509e85d990';
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(
    undefined // Default to 'Booking' for now to match the screenshot
  );
  const [selectedEmailAccount, setSelectedEmailAccount] = useState<any>(null);

  // Get emails data
  const { emails, loading, refetch, hasMore, loadMore } = useEmails({
    category: selectedCategory,
    mailId: selectedEmailAccount?.email_address,
  });

  // Search functionality
  const {
    searchQuery,
    setSearchQuery,
    searchResults,
    loading: searchLoading,
    isSearchActive,
  } = useEnhancedEmailSearch();

  // Handle category change
  const handleCategoryChange = useCallback((category: string | undefined) => {
    setSelectedCategory(category);
  }, []);

  // console.log("selectedEmailAccount?.email_address",selectedEmailAccount?.email_address)
  // Handle email press
  const handleEmailPress = useCallback(
    (email: EmailMessage) => {
      // console.log("email navigation data", email)
      // Navigate to email detail screen
      navigation.navigate('EmailDetail', {
        emailId: email.email_date_message_id,
        threadId: email.thread_id,
        email: email,
        mailId: selectedEmailAccount?.email_address,
      });
    },
    [navigation, selectedEmailAccount]
  );

  // Handle new mail button press
  const handleNewMailPress = useCallback(() => {
    navigation.navigate('EmailCompose', {
      mailId: selectedEmailAccount?.email_address,
    });
  }, [navigation, selectedEmailAccount?.email_address]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);
  const [showSearch, setShowSearch] = useState(false);
  const searchAnimation = useRef(new Animated.Value(0)).current;
  const searchInputRef = useRef(null);

  useEffect(() => {
    Animated.timing(searchAnimation, {
      toValue: showSearch ? 1 : 0,
      duration: 250,
      useNativeDriver: false,
    }).start();
    if (showSearch) {
      setTimeout(() => {
        if (searchInputRef.current) searchInputRef.current.focus();
      }, 300);
    }
  }, [showSearch, searchAnimation]);

  const searchBarWidth = searchAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, width - scale(55)],
  });

  const searchOpacity = searchAnimation.interpolate({
    inputRange: [0, 0.7, 1],
    outputRange: [0, 0, 1],
  });

  const handleToggle = () => {
    if (showSearch) setSearchQuery('');
    setShowSearch(!showSearch);
  };

  const AppHeaderRight = (
    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
      <Animated.View
        style={{
          width: searchBarWidth,
          opacity: searchOpacity,
          marginRight: 8,
          // overflow: 'hidden',
          backgroundColor: '#f0f0f0',
          borderRadius: 8,
          height: 40,
          justifyContent: 'center',
        }}
      >
        {showSearch && (
          <SearchBar
            ref={searchInputRef}
            style={{
              // paddingHorizontal: 12,
              fontSize: 15,
              flex: 1,
              // height: 40,
              color: '#222',
            }}
            placeholder="Search emails..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            returnKeyType="search"
            // clearButtonMode="while-editing"
            placeholderTextColor="#999"
          />
        )}
      </Animated.View>
      <TouchableOpacity onPress={handleToggle}>
        {showSearch ? <X size={22} color="#444" /> : <Search size={22} color="#444" />}
      </TouchableOpacity>
    </View>
  );

  // Display either search results or regular emails
  const displayedEmails = isSearchActive ? searchResults : emails;

  // console.log("theme.colors.background",theme.colors.background)
  return (
    <SafeContainerView>
      <StatusBar
        barStyle="light-content"
        backgroundColor={theme.colors.primary}
        translucent={false}
        hidden={false}
      />
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {/* <EnhancedEmailHeader
          searchText={searchQuery}
          onSearchChange={setSearchQuery}
        /> */}
        <AppHeader title="Email Inbox" rightElement={AppHeaderRight} hideBackAction={true} />
        <EmailAccountSelector
          userId={userId}
          selectedAccount={selectedEmailAccount}
          onSelectAccount={setSelectedEmailAccount}
        />

        <EmailFilters selectedCategory={selectedCategory} onCategoryChange={handleCategoryChange} />

        <AnimatedEmailList
          emails={displayedEmails}
          loading={isSearchActive ? searchLoading : loading}
          onRefresh={handleRefresh}
          onEmailPress={handleEmailPress}
          loadMore={loadMore}
        />

        <Animated.View
          style={{
            position: 'absolute',
            right: 10,
            bottom: 36,
          }}
        >
          <PaperFAB
            icon="square-edit-outline"
            style={[styles.fab, { backgroundColor: theme.colors.primary }]}
            onPress={handleNewMailPress}
            color={theme.colors.onPrimary}
          />
        </Animated.View>
      </View>
    </SafeContainerView>
  );
};

export default EmailInbox;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  fab: {
    borderRadius: 28,
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});
