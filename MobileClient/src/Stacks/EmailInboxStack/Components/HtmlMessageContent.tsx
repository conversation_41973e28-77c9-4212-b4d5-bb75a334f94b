import React, { useMemo, useState, useCallback } from 'react';
import { View, useWindowDimensions, StyleSheet, Linking } from 'react-native';
import { useTheme } from 'react-native-paper';
import { WebView } from 'react-native-webview';
import { convertPlainTextToHtml } from '../Utils/HtmlFormatter';

interface HtmlMessageContentProps {
  html: string;
  baseStyle?: any;
  minHeight?: number; // optional: fallback height
}

const HtmlMessageContent: React.FC<HtmlMessageContentProps> = ({ html, baseStyle, minHeight = 40 }) => {
  const { width } = useWindowDimensions();
  const theme = useTheme();
  const [webHeight, setWebHeight] = useState<number>(minHeight);

  const contentHtml = useMemo(() => convertPlainTextToHtml(html), [html]);

  const injectedCSS = useMemo(() => {
    const fontSize = 12;
    const lineHeight = baseStyle?.lineHeight ?? 20;
    const fontFamily = baseStyle?.fontFamily ?? theme.fonts.bodyMedium.fontFamily;
    const color = theme.colors.onSurface;
    const linkColor = theme.colors.primary;
    // console.log('html data in mail detail', html)
    return `
      <style>
        html, body { margin:0; padding:0; }
        body {
          color:${color};
          font-size:${fontSize}px;
          line-height:${lineHeight}px;
          font-family:${fontFamily}, -apple-system, system-ui, Roboto, "Segoe UI", Arial, sans-serif;
          -webkit-font-smoothing:antialiased; -moz-osx-font-smoothing:grayscale;
          word-break:break-word; overflow-wrap:anywhere;
        }
        p { margin:0 0 10px 0; }
        a { color:${linkColor}; text-decoration:underline; }
        img, video, iframe { max-width:100%; height:auto; }
        table { width:100%; border-collapse:collapse; }
        th, td { padding:6px; }
        ${theme.dark ? `body { background:#000; }` : ''}
        /* Wrap all user content so we can measure it exactly */
        
      </style>
    `;
  }, [theme, baseStyle]);

  // JS that measures height and posts it to RN. Runs on load and on resize/mutations.
  const autoHeightJS = `
    (function() {
      function postHeight() {
        var h = document.documentElement.scrollHeight || document.body.scrollHeight || 0;
        window.ReactNativeWebView && window.ReactNativeWebView.postMessage(JSON.stringify({ type: 'HEIGHT', height: h }));
      }
      // Post after DOM ready and again after a tick for images
      function ready(fn){ if(document.readyState!='loading'){fn()} else {document.addEventListener('DOMContentLoaded', fn)} }
      ready(function(){ postHeight(); setTimeout(postHeight, 100); setTimeout(postHeight, 300); });

      // Observe DOM changes (new images, fonts, etc.)
      var ro = new ResizeObserver(postHeight);
      ro.observe(document.body);

      var mo = new MutationObserver(function(){ setTimeout(postHeight, 50); });
      mo.observe(document.body, { attributes:true, childList:true, subtree:true, characterData:true });

      // Recompute on window resize/viewport changes
      window.addEventListener('load', postHeight);
      window.addEventListener('resize', postHeight);

      // Enable link target=_blank behavior to be intercepted by RN
      Array.prototype.forEach.call(document.querySelectorAll('a[href]'), function(a){ a.setAttribute('target','_blank'); });
      true;
    })();
  `;

  const htmlDoc = useMemo(() => `
    <!doctype html>
    <html>
      <head>
        <meta charset="utf-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"/>
        ${injectedCSS}
      </head>
      <body>
        <div id="__content">
          ${contentHtml}
        </div>
      </body>
    </html>
  `, [injectedCSS, contentHtml]);

  // Remount on content/style mode change to ensure fresh measurement
  const webKey = useMemo(() => {
    const s = JSON.stringify({
      len: contentHtml.length,
      fs: baseStyle?.fontSize ?? 14,
      lh: baseStyle?.lineHeight ?? 20,
      dark: !!theme.dark,
    });
    return s;
  }, [contentHtml, baseStyle, theme.dark]);

  const onMessage = useCallback((e: any) => {
    try {
      const data = JSON.parse(e.nativeEvent.data);
      if (data?.type === 'HEIGHT' && typeof data.height === 'number') {
        setWebHeight(Math.max(data.height, minHeight));
      }
    } catch {}
  }, [minHeight]);

  const handleNavigationRequest = (request: any) => {
    const url = request?.url ?? '';
    if (url.startsWith('http://') || url.startsWith('https://')) {
      Linking.openURL(url);
      return false;
    }
    return true;
  };

  return (
    <View style={[styles.container, { width }]}>
      <WebView
        key={webKey}
        source={{ html: htmlDoc }}
        style={{ width, height: webHeight,paddingHorizontal:10,paddingTop:10 }}         
        javaScriptEnabled
        injectedJavaScript={autoHeightJS}             
        onMessage={onMessage}                         
        scrollEnabled={false}                          
        contentMode="mobile"
        originWhitelist={['*']}
        onShouldStartLoadWithRequest={handleNavigationRequest}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flexShrink: 1 }, // don't force height; let measured height apply
});

export default HtmlMessageContent;