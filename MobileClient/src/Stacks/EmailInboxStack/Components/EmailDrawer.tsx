import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { DrawerContentScrollView } from '@react-navigation/drawer';
import { Inbox, Send, FileText, Trash2, Users, X, Star, Mail } from 'lucide-react-native';
import ApiClient from '../../../Common/API/ApiClient';
import { getRefreshToken } from '../../../Common/Utils/Storage';
// import { EmailLabel, LabelsResponse } from '../types/emailTypes';

// Icon mapping for different label types
const getLabelIcon = (labelName: string) => {
  switch (labelName.toUpperCase()) {
    case 'INBOX':
      return Inbox;
    case 'SENT':
      return Send;
    case 'DRAFT':
      return FileText;
    case 'TRASH':
      return Trash2;
    case 'STARRED':
      return Star;
    case 'UNREAD':
      return Mail;
    default:
      return Mail;
  }
};

function DrawerItem({ icon: Icon, label, count, onPress, isActive = false }) {
  return (
    <TouchableOpacity
      style={[styles.item, isActive && styles.activeItem]}
      onPress={onPress}
      activeOpacity={0.75}
    >
      <Icon size={20} color={isActive ? '#0B5FFF' : '#1f2937'} style={styles.icon} />
      <Text style={[styles.label, isActive && styles.activeLabel]}>{label}</Text>
      {count > 0 && (
        <View style={styles.countBadge}>
          <Text style={styles.countText}>{count}</Text>
        </View>
      )}
    </TouchableOpacity>
  );
}

export default function EmailDrawer(props) {
  const { navigation } = props;
  const [active, setActive] = useState('INBOX');
  const [labels, setLabels] = useState<EmailLabel[]>([]);
  const [categories, setCategories] = useState<EmailLabel[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchLabels();
  }, []);

  const fetchLabels = async () => {
    try {
      setLoading(true);
      const refreshToken = await getRefreshToken();
      console.log('refreshToken', refreshToken);
      const response = await ApiClient.post('/email/gmail/labels', {
        refreshToken,
      });

      if (response.data?.labels) {
        const { system, categories: categoriesData } = response.data.labels;

        // Filter system labels that should be visible
        const visibleSystemLabels = system.filter(
          (label: EmailLabel) => label.visibility.labelList === 'labelShow'
        );

        setLabels(visibleSystemLabels);
        setCategories(categoriesData.children || []);
      }
    } catch (error) {
      console.error('Error fetching labels:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNav = (labelId: string, labelName: string) => {
    setActive(labelId);
    navigation.navigate('BottomTabStack', {
      screen: 'EmailCenter',
      params: {
        screen: labelName,
        labelId: labelId,
      },
    });
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0B5FFF" />
        <Text style={styles.loadingText}>Loading labels...</Text>
      </View>
    );
  }

  return (
    <DrawerContentScrollView {...props} contentContainerStyle={{ flex: 1 }}>
      {/* Header */}
      <View style={styles.headerRow}>
        <Text style={styles.title}>Email Center</Text>
        <TouchableOpacity
          style={styles.closeIconContainer}
          onPress={() => navigation.closeDrawer()}
        >
          <X size={22} color="#111827" />
        </TouchableOpacity>
      </View>

      {/* System Labels */}
      <View style={{ marginTop: 20 }}>
        <Text style={styles.sectionTitle}>Mailbox</Text>
        {labels.map((label) => {
          const IconComponent = getLabelIcon(label.name);
          return (
            <DrawerItem
              key={label.id}
              icon={IconComponent}
              label={label.name.charAt(0) + label.name.slice(1).toLowerCase()}
              count={label.counts.unread}
              isActive={active === label.id}
              onPress={() => handleNav(label.id, label.name)}
            />
          );
        })}
      </View>

      {/* Categories */}
      {categories.length > 0 && (
        <View style={{ marginTop: 20 }}>
          <Text style={styles.sectionTitle}>Categories</Text>
          {categories.map((category) => (
            <DrawerItem
              key={category.id}
              icon={Mail}
              label={category.name.charAt(0) + category.name.slice(1).toLowerCase()}
              count={category.counts.unread}
              isActive={active === category.id}
              onPress={() => handleNav(category.id, category.name)}
            />
          ))}
        </View>
      )}

      {/* Contacts */}
      <View style={{ marginTop: 20 }}>
        <DrawerItem
          icon={Users}
          label="Contacts"
          count={0}
          isActive={active === 'Contacts'}
          onPress={() => handleNav('Contacts', 'Contacts')}
        />
      </View>
    </DrawerContentScrollView>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#111827',
  },
  closeIconContainer: {
    padding: 6,
    borderRadius: 16,
    backgroundColor: '#fff',
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
    paddingHorizontal: 20,
    paddingBottom: 8,
    textTransform: 'uppercase',
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
  },
  activeItem: {
    backgroundColor: '#EEF4FF',
  },
  icon: {
    marginRight: 14,
  },
  label: {
    flex: 1,
    fontSize: 15,
    color: '#1f2937',
  },
  activeLabel: {
    color: '#0B5FFF',
    fontWeight: '700',
  },
  countBadge: {
    backgroundColor: '#DC2626',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  countText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
});
