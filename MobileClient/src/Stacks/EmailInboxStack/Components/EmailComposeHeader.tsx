import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useTheme, Appbar, Text } from 'react-native-paper';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { EmailComposeHeaderProps } from '../Types/EmailComposeTypes';

const EmailComposeHeader: React.FC<EmailComposeHeaderProps> = ({
  onBack,
  onSend,
  isSending,
  selectedEmailAccount,
}) => {
  const theme = useTheme();

  return (
    <Appbar.Header mode="small" style={[styles.header, { backgroundColor: '#fff' }]}>
      <Appbar.BackAction
        size={moderateScale(20)}
        color={theme.colors.onSurface}
        onPress={onBack}
      />
      <Appbar.Content
        title={
          <View>
            <Text style={styles.title}>New Message</Text>
            {selectedEmailAccount ? (
              <Text style={[styles.subtitle,{color:'#777'}]}>{selectedEmailAccount}</Text>
            ) : null}
          </View>
        }
      />
      {/* <Button
        mode="contained"
        onPress={onSend}
        disabled={isSending}
        loading={isSending}
        icon="send"
        style={[styles.sendButton,{backgroundColor:theme.colors.primaryContainer}]}
        labelStyle={[styles.sendButtonLabel,{color:theme.colors.onPrimaryContainer}]}
      >
        Send
      </Button> */}
      {/* <TouchableOpacity style={{
        // backgroundColor:theme.colors.onPrimaryContainer,
        borderRadius:20,
        flexDirection:'row',
        paddingVertical:verticalScale(3),
        paddingHorizontal:scale(5),
        // gap:scale(5),
        alignItems:"center"
      }} >
        <SendHorizonal color={theme.colors.primary} size={moderateScale(15)} />
        <Text variant='semiBold' style={{
          color:theme.colors.primary
        }} >send</Text>

      </TouchableOpacity> */}
    </Appbar.Header>
  );
};

export default EmailComposeHeader;

const styles = StyleSheet.create({
  header: {
    elevation: 0,
    alignItems: 'center',
  },
  title: {
    fontSize: moderateScale(16),
    fontFamily: 'Geist-SemiBold',
    color: '#000',
  },
  subtitle: {
    fontSize: moderateScale(12),
    fontFamily: 'Geist-Medium',
    marginTop: 2,
  },
  sendButton: {
    marginRight: 12,
    borderRadius: 20,
    paddingHorizontal: scale(5),
  },
  sendButtonLabel: {
    fontSize: moderateScale(12),
    fontFamily: 'Geist-Medium',
  },
});
