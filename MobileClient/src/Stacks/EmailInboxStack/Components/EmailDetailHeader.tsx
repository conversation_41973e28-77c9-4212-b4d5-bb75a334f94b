import React, {useState} from 'react';
import {Modal, StyleSheet, TouchableOpacity, View} from 'react-native';
import {useTheme, Menu, Divider, Appbar, IconButton, Text} from 'react-native-paper';
import {EmailDetailHeaderProps} from '../Types/EmailDetailTypes';
import { Ship, Truck, Plane, ArrowDown, ArrowUp } from 'lucide-react-native';
import { OptionCard } from '../../LeadsAppStack/components/OptionCard';
import { moderateScale, scale, verticalScale } from 'react-native-size-matters';
import { useNavigationHook } from '../../../Hooks/Navigation/useNavigationHook';

const EmailDetailHeader: React.FC<EmailDetailHeaderProps> = ({
  onBack,
  onArchive,
  onDelete,
  onHistory,
  onReply,
  onReplyAll,
  onForward,
  onShare,
  onMore,
  threadMessages
}) => {
  const theme = useTheme();
  const [menuVisible, setMenuVisible] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [transportationType, setTransportationType] = useState<string | null>(null);
  const [enquiryType, setEnquiryType] = useState<string | null>(null);
  const openMenu = () => setMenuVisible(true);
  const closeMenu = () => setMenuVisible(false);

  const {navigation} = useNavigationHook();
  const handleMenuAction = (action: () => void) => {
    closeMenu();
    action();
  };

  const handleSubmit = () => {
    setModalVisible(false);
    navigation.navigate('EmailExtractionScreen', {
      transportationType, enquiryType, threadMessages
    });
  }

  return (
    <View  >
    <Appbar.Header>
      <Appbar.BackAction
        onPress={onBack}
        color={theme.colors.onSurface}
        size={22}
      />
      <Appbar.Content
        title=""
        color={theme.colors.onPrimary}
        titleStyle={{fontSize: 18, fontWeight: 'bold'}}
      />

      {/* Primary actions */}
      <IconButton
        icon="archive-outline"
        iconColor={theme.colors.onSurface}
        size={22}
        onPress={onArchive}
        style={styles.actionButton}
      />
      <IconButton
        icon="delete-outline"
        iconColor={theme.colors.onSurface}
        size={22}
        onPress={onDelete}
        style={styles.actionButton}
      />

      {/* More menu */}
      <Menu
        visible={menuVisible}
        onDismiss={closeMenu}
        anchor={
          <IconButton
            icon="dots-vertical"
            iconColor={theme.colors.onSurface}
            size={22}
            onPress={openMenu}
            style={styles.actionButton}
          />
        }
        contentStyle={{backgroundColor: theme.colors.surface}}>
        {/* <Menu.Item
          onPress={() => handleMenuAction(onReply)}
          title="Reply"
          leadingIcon="reply"
        />
        {onReplyAll && (
          <Menu.Item
            onPress={() => handleMenuAction(onReplyAll)}
            title="Reply All"
            leadingIcon="reply-all"
          />
        )}
        <Menu.Item
          onPress={() => handleMenuAction(onForward)}
          title="Forward"
          leadingIcon="forward"
        />
        <Divider /> */}
        <Menu.Item
          onPress={() => handleMenuAction(onShare)}
          title="Move to folder"
          leadingIcon="folder-outline"
        />
        <Menu.Item
          onPress={() => handleMenuAction(onShare)}
          title="Share"
          leadingIcon="share-variant"
        />
        <Divider />
        <Menu.Item
          onPress={() => handleMenuAction(() => {})}
          title="Mark unread"
          leadingIcon="email-mark-as-unread"
        />
        <Menu.Item
          onPress={() => handleMenuAction(onHistory)}
          title="History"
          leadingIcon="clock-outline"
        />
         <Menu.Item
          onPress={() => {
            setMenuVisible(false)
            setModalVisible(true);
          }}
          title="Create enquiry form"
          leadingIcon="file-document-outline"
        />
      </Menu>
    </Appbar.Header>
    <Modal transparent visible={modalVisible} animationType="fade">
        <View style={styles.overlay}>
          <View style={styles.container}>
            <Text variant='semiBold' style={styles.title}>Create Enquiry Form</Text>
            <Text style={styles.subtitle}>
              Please select the transportation type and enquiry type for your enquiry.
            </Text>

            <Text variant='medium' style={styles.label}>Transportation Type</Text>
            <View style={styles.optionGrid}>
              <OptionCard
                icon={Ship}
                label="Sea"
                isSelected={transportationType === "Sea"}
                onPress={() => setTransportationType("Sea")}
              />
              <OptionCard
                icon={Plane}
                label="Air"
                isSelected={transportationType === "Air"}
                onPress={() => setTransportationType("Air")}
              />
              <OptionCard
                icon={Truck}
                label="Land"
                isSelected={transportationType === "Land"}
                onPress={() => setTransportationType("Land")}
              />
            </View>

            <Text variant='medium' style={styles.label}>Enquiry Type</Text>
            <View style={styles.optionGrid}>
              <OptionCard
                icon={ArrowDown}
                label="Import"
                isSelected={enquiryType === "Import"}
                onPress={() => setEnquiryType("Import")}
              />
              <OptionCard
                icon={ArrowUp}
                label="Export"
                isSelected={enquiryType === "Export"}
                onPress={() => setEnquiryType("Export")}
              />
            </View>

            <View style={styles.actions}>
              <TouchableOpacity
                style={styles.cancelBtn}
                onPress={() => setModalVisible(false)}>
                <Text variant='medium' style={styles.cancelText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.submitBtn,
                  !(transportationType && enquiryType) && styles.disabled,
                ]}
                onPress={handleSubmit}
                disabled={!(transportationType && enquiryType)}>
                <Text variant='medium' style={styles.submitText}>Submit</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  actionButton: {
    margin: 0,
  },

  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.3)",
    justifyContent: "center",
    alignItems: "center",
  },
  container: {
    backgroundColor: "white",
    borderRadius: scale(12),
    padding: scale(16),
    width: "85%",
  },
  title: {
    marginBottom: verticalScale(6),
    fontSize: moderateScale(14)
  },
  subtitle: {
    color: "#999",
    fontSize: moderateScale(11)
  },
  label: {
    marginTop: verticalScale(12),
    marginBottom: verticalScale(6),
    fontSize: moderateScale(12)
  },
  optionGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: scale(12),
  },
  actions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: verticalScale(20),
  },
  cancelBtn: {
    marginRight: scale(12),
    paddingVertical: verticalScale(8),
    paddingHorizontal: scale(16),
    borderRadius: scale(8),
    borderWidth:1,
    borderColor:"#e4e4e4"
  },
  cancelText: {
    color: "#333",
    fontSize:moderateScale(13)
  },
  submitBtn: {
    backgroundColor: "#4f6ef7",
    paddingVertical: verticalScale(8),
    paddingHorizontal: scale(16),
    borderRadius: scale(8),
  },
  submitText: {
    color: "white",
    fontSize:moderateScale(13)
  },
  disabled: {
    backgroundColor: "#cbd5e1",
  },
});

export default EmailDetailHeader;
