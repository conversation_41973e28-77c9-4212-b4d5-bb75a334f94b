import {EmailMessage, EmailAttachment} from './EmailTypes';

export {EmailMessage, EmailAttachment};

export interface EmailDetailScreenProps {
  route?: {
    params?: {
      emailId?: string;
      threadId?: string;
      email?: EmailMessage;
      mailId? : string;
    };
  };
  navigation?: any;
}

export interface EmailDetailHeaderProps {
  email: EmailMessage;
  onBack: () => void;
  onArchive: () => void;
  onDelete: () => void;
  onHistory: () => void;
  onReply: () => void;
  onReplyAll?: () => void;
  onForward: () => void;
  onShare: () => void;
  onMore: () => void;
  threadMessages : any;
}

export interface EmailContentProps {
  email: EmailMessage;
  expanded: boolean;
  onToggleExpand: () => void;
}

export interface EmailAttachmentsProps {
  attachments: EmailAttachment[];
  onAttachmentPress: (attachment: EmailAttachment) => void;
}

export interface EmailThreadProps {
  threadMessages: EmailMessage[];
  onMessagePress: (message: EmailMessage) => void;
}

export interface EmailReplyInputProps {
  onSend: (message: string) => void;
  onAttach: () => void;
  onVoice: () => void;
  onEmoji: () => void;
  onTemplate: () => void;
}

export interface ThreadMessage {
  id: string;
  sender: {
    name: string;
    email: string;
    avatar?: string;
  };
  date: string;
  content: string;
}

export interface BookingDetails {
  origin: string;
  destination: string;
  readyDate: string;
  deliveryDateRequired: string;
  incoterms: string;
}

export interface CargoDetails {
  commodity: string;
  quantity: string;
  totalWeight: string;
  totalVolume?: string;
  hazardous?: string;
}

export interface AdditionalServices {
  services: string[];
}

export interface EmailSender {
  name: string;
  title?: string;
  company?: string;
  phone?: string;
}
