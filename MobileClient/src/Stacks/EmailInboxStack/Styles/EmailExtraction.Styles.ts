import { StyleSheet } from 'react-native';
import { scale, verticalScale, moderateScale } from 'react-native-size-matters';
import { fontFamily } from '../../..//Common/Theme/typography';
export  const emailExtractionStyles = () => {

 return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#F8F9FA',
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: verticalScale(20),
    },
    contentContainer: {
      paddingHorizontal: scale(16),
      paddingTop: verticalScale(16),
    },
    header: {
      fontSize: moderateScale(24),
      color: '#1A1A1A',
      fontFamily: fontFamily.bold,
    },
    subHeader: {
      fontSize: moderateScale(14),
      color: '#6B7280',
      fontFamily: fontFamily.medium,
      lineHeight: moderateScale(20),
    },
    sectionCard: {
      marginBottom: verticalScale(16),
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      borderRadius: scale(12),
    },
    cardContent: {
      paddingVertical: scale(16),
    },
    input: {
      marginBottom: verticalScale(12),
      backgroundColor: '#FFFFFF',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: verticalScale(60),
    },
    loadingText: {
      marginTop: verticalScale(16),
      fontSize: moderateScale(16),
      color: '#6B7280',
      fontFamily: fontFamily.medium,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: scale(32),
      paddingVertical: verticalScale(60),
    },
    errorTitle: {
      fontSize: moderateScale(20),
      fontWeight: '600',
      color: '#EF4444',
      textAlign: 'center',
      marginBottom: verticalScale(8),
      fontFamily: fontFamily.semiBold,
    },
    errorMessage: {
      fontSize: moderateScale(14),
      color: '#6B7280',
      textAlign: 'center',
      lineHeight: moderateScale(20),
      marginBottom: verticalScale(24),
      fontFamily: fontFamily.regular,
    },
    retryButton: {
      paddingHorizontal: scale(24),
      paddingVertical: verticalScale(12),
      backgroundColor: '#007AFF',
      borderRadius: scale(8),
    },
    retryButtonText: {
      color: '#FFFFFF',
      fontSize: moderateScale(16),
      fontWeight: '600',
      fontFamily: fontFamily.semiBold,
    },
    emptyStateContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: scale(32),
      paddingVertical: verticalScale(60),
    },
    emptyStateTitle: {
      fontSize: moderateScale(18),
      fontWeight: '600',
      color: '#374151',
      textAlign: 'center',
      marginBottom: verticalScale(8),
      fontFamily: fontFamily.semiBold,
    },
    emptyStateMessage: {
      fontSize: moderateScale(14),
      color: '#6B7280',
      textAlign: 'center',
      lineHeight: moderateScale(20),
      marginBottom: verticalScale(24),
      fontFamily: fontFamily.regular,
    },
    sectionTitle: {
      fontSize: moderateScale(18),
      fontWeight: '600',
      color: '#1F2937',
      fontFamily: fontFamily.semiBold,
      marginBottom: verticalScale(4),
    },
    badge: {
      backgroundColor: '#EBF4FF',
      paddingHorizontal: scale(8),
      paddingVertical: verticalScale(4),
      borderRadius: scale(12),
      alignSelf: 'flex-start',
      marginBottom: verticalScale(8),
    },
    badgeText: {
      fontSize: moderateScale(12),
      color: '#1E40AF',
      fontWeight: '500',
      fontFamily: fontFamily.medium,
    },
  });
};

export default emailExtractionStyles;
