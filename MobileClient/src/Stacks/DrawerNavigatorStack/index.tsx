import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { createDrawerNavigator, DrawerContentScrollView } from '@react-navigation/drawer';
import { useNavigationState, useFocusEffect } from '@react-navigation/native';
import BottomTabStack from '../../Common/Routes/BottomTabStack';
import { Avatar } from 'react-native-paper';
import {
  Package,
  Calendar,
  FileText,
  AlertCircle,
  Wallet,
  X,
  LayoutDashboard,
} from 'lucide-react-native';
import EmailDrawer from '../EmailInboxStack/Components/EmailDrawer';


function CustomDrawerContent(props) {
  // Get the current route name using navigation state selector
  const currentRouteName = useNavigationState((state) => {
    // Helper function to find the deepest route name
    const findDeepestRoute = (navState) => {
      // Add null/undefined checks
      if (
        !navState ||
        !navState.routes ||
        !Array.isArray(navState.routes) ||
        navState.routes.length === 0
      ) {
        return 'CustomerDashboard';
      }

      // Check if index is valid
      if (
        typeof navState.index !== 'number' ||
        navState.index < 0 ||
        navState.index >= navState.routes.length
      ) {
        return 'CustomerDashboard';
      }

      const currentRoute = navState.routes[navState.index];

      // Check if currentRoute exists and has the expected structure
      if (!currentRoute || typeof currentRoute !== 'object') {
        return 'CustomerDashboard';
      }

      // Recursively check nested states
      if (currentRoute.state && typeof currentRoute.state === 'object') {
        return findDeepestRoute(currentRoute.state);
      }

      return currentRoute.name || 'CustomerDashboard';
    };

    // Return fallback if state is undefined/null
    if (!state) {
      return 'CustomerDashboard';
    }

    return findDeepestRoute(state);
  });

  // Initialize with current route or default
  const [activeRoute, setActiveRoute] = React.useState('CustomerDashboard');

  // Update active route when navigation state changes or on mount
  React.useEffect(() => {
    console.log('Current route name:', currentRouteName);
    const routeName = currentRouteName || 'CustomerDashboard';
    setActiveRoute(routeName);
  }, [currentRouteName]);

  // Also check on focus to ensure we catch the initial navigation
  useFocusEffect(
    React.useCallback(() => {
      const routeName = currentRouteName || 'CustomerDashboard';
      setActiveRoute(routeName);
    }, [currentRouteName])
  );

  const handleNavigation = (screenName) => {
    setActiveRoute(screenName);
    props.navigation.navigate('BottomTabStack', {
      screen: 'Home',
      params: {
        screen: 'CustomerStack',
        params: {
          screen: screenName,
        },
      },
    });
  };

  return (
    <DrawerContentScrollView {...props} contentContainerStyle={{ flex: 1 }}>
      <View style={styles.headerRow}>
        <View style={styles.header}>
          <Avatar.Text label="AC" size={40} style={{ backgroundColor: '#91bfff' }} />
          <View style={{ marginLeft: 12 }}>
            <Text style={styles.company}>Acme Corp</Text>
            <Text style={styles.email}><EMAIL></Text>
          </View>
        </View>
        <TouchableOpacity
          style={styles.closeIconContainer}
          onPress={() => props.navigation.closeDrawer()}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <X size={24} color="#222" />
        </TouchableOpacity>
      </View>

      <View style={styles.menu}>
        <DrawerItem
          icon="layout-dashboard"
          label="Dashboard"
          isActive={activeRoute === 'CustomerDashboard' || activeRoute === 'CustomerStack'}
          onPress={() => handleNavigation('CustomerDashboard')}
        />
          <DrawerItem
            icon="file-document-outline"
            label="Enquiry"
            isActive={activeRoute === 'Enquiries'}
            onPress={() => handleNavigation('Enquiries')}
          />
          <DrawerItem
            icon="calendar"
            label="Quotes"
            isActive={activeRoute === 'QuotesAndBooking'}
            onPress={() => handleNavigation('QuotesAndBooking')}
          />
        <DrawerItem
          icon="Ship"
          label="Tracking"
          isActive={activeRoute === 'Shipments'}
          onPress={() => handleNavigation('Shipments')}
        />
        <DrawerItem
          icon="alert-circle-outline"
          label="Pending"
          isActive={activeRoute === 'PendingActions'}
          onPress={() => handleNavigation('PendingActions')}
        />
        <DrawerItem
          icon="cash-multiple"
          label="Billing"
          isActive={activeRoute === 'Billing'}
          onPress={() => handleNavigation('Billing')}
        />
      </View>
    </DrawerContentScrollView>
  );
}

function DrawerItem({ icon, label, onPress, isActive = false }) {
  const iconColor = isActive ? '#2196F3' : '#222';

  const getIcon = () => {
    switch (icon) {
      case 'layout-dashboard':
        return <LayoutDashboard size={22} color={iconColor} style={styles.iconStyle} />;
      case 'calendar':
        return <Calendar size={22} color={iconColor} style={styles.iconStyle} />;
      case 'file-document-outline':
        return <FileText size={22} color={iconColor} style={styles.iconStyle} />;
      case 'alert-circle-outline':
        return <AlertCircle size={22} color={iconColor} style={styles.iconStyle} />;
      case 'cash-multiple':
        return <Wallet size={22} color={iconColor} style={styles.iconStyle} />;
      case 'Ship':
      default:
        return <Package size={22} color={iconColor} style={styles.iconStyle} />;
    }
  };

  return (
    <TouchableOpacity
      style={[styles.item, isActive && styles.activeItem]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {getIcon()}
      <Text style={[styles.label, isActive && styles.activeLabel]}>{label}</Text>
    </TouchableOpacity>
  );
}
const Drawer = createDrawerNavigator();

const DynamicDrawerContent = (props: any) => {
  const currentRoute = useNavigationState((state) => {
    // Navigate through the state to find the current bottom tab
    const tabState = state?.routes?.find((route) => route.name === 'BottomTabStack')?.state;
    return tabState?.routes?.[tabState.index]?.name;
  });

  // Return EmailDrawer when on EmailInbox tab, otherwise default drawer
  if (currentRoute === 'EmailInbox') {
    return <EmailDrawer {...props} />;
  }

  return <CustomDrawerContent {...props} />;
};

const DrawerStackNavigator = () => (
  <Drawer.Navigator
    initialRouteName="BottomTabStack"
    screenOptions={{ headerShown: false, drawerType: 'front', swipeEnabled: false }}
    drawerContent={(props) => <DynamicDrawerContent {...props} />}
  >
    <Drawer.Screen name="BottomTabStack" component={BottomTabStack} />
  </Drawer.Navigator>
);

const styles = StyleSheet.create({
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  closeIconContainer: {
    padding: 4,
    borderRadius: 16,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  company: {
    fontWeight: 'bold',
    fontSize: 16,
    color: '#222',
  },
  email: {
    fontSize: 13,
    color: '#888',
    marginTop: 2,
  },
  menu: {
    marginTop: 20,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 24,
  },
  activeItem: {
    backgroundColor: '#f0f7ff',
    borderRightWidth: 3,
    borderRightColor: '#2196F3',
  },
  iconStyle: {
    marginRight: 16,
  },
  label: {
    fontSize: 15,
    color: '#222',
  },
  activeLabel: {
    fontWeight: 'bold',
    color: '#2196F3',
  },
});

export default DrawerStackNavigator;
