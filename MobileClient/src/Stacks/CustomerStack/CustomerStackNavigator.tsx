import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import CustomerDashboard from './Screens/CustomerDashboard/CustomerDashboard';
import ShipmentsScreen from './Screens/Shipments/ShipmentsScreen';
import Billing from './Screens/Billing/Billing';
import QuotesAndBooking from './Screens/QuotesAndBooking/QuotesAndBooking';
import Enquiries from './Screens/Enquiries/Enquiries';
import PendingActions from './Screens/PendingActions/PendingActions';
import ShipmentsDetails from './Screens/Shipments/ShipmentDetails/ShipmentsDetails';
import QuoteDetailsScreen from './Screens/QuotesAndBooking/QuoteDetailsScreen';
import CustomerOverView from './CustomerOverview/CustomerOverview';
// import other screens when available

const Stack =createNativeStackNavigator();

const CustomerStackNavigator = () => (
  <Stack.Navigator initialRouteName="CustomerDashboard" screenOptions={{headerShown:false}}>
    <Stack.Screen name="CustomerDashboard" component={CustomerDashboard} />
    <Stack.Screen name="Shipments" component={ShipmentsScreen} />
    <Stack.Screen name="Billing" component={Billing} />
    <Stack.Screen name="QuotesAndBooking" component={QuotesAndBooking} />
    <Stack.Screen name="Enquiries" component={Enquiries} />
    <Stack.Screen name="PendingActions" component={PendingActions} />
    <Stack.Screen name="ShipmentDetails" component={ShipmentsDetails} />
    <Stack.Screen name="QuoteDetails" component={QuoteDetailsScreen} />
    <Stack.Screen name="CustomerOverView" component={CustomerOverView}/>

    {/* Add more screens here as you create them in Billing, Enquiries, PendingActions, QuotesAndBooking */}
  </Stack.Navigator>
);

export default CustomerStackNavigator;
