import { View } from "react-native";
import { Text } from "react-native-paper";
import { moderateScale } from "react-native-size-matters";

// ✅ Card Heading
export const CardHeading = ({ title }: { title: string }) => {
  return (
    <View style={{ marginVertical: moderateScale(6), alignItems: "flex-start" }}>
      <Text style={{ fontWeight: "700", fontSize: moderateScale(14) }}>
        {title}
      </Text>
    </View>
  );
};

// ✅ Secondary Card
const SecondaryCard = ({ children, styles }: any) => {
  return (
    <View
      style={[
        {
          // minHeight:moderateScale(70),
          minWidth:moderateScale(150),
          backgroundColor: "#F9FAFB",
          paddingVertical: moderateScale(6),
          paddingHorizontal: moderateScale(6),
          borderWidth: moderateScale(1),
          borderColor: "#e2e8f0",
          borderRadius: moderateScale(12),
        },
        styles,
      ]}
    >
      {children}
    </View>
  );
};

export default SecondaryCard;
