// components/CustomerOverview.tsx

import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ActivityIndicator, ScrollView, Image } from 'react-native';
import { Badge, Text } from 'react-native-paper';
import { moderateScale, scale } from 'react-native-size-matters';
import { useSelector } from 'react-redux';
import { PrimraryCard } from './Cards';
import SecondaryCard, { CardHeading } from './InnerCard';
import {
  Briefcase,
  BriefcaseIcon,
  CalendarDays,
  DollarSign,
  FileText,
  MoveDown,
  MoveRight,
  Plane,
  Search,
  Ship,
  Target,
  Truck,
  Users,
} from 'lucide-react-native';

import {
  CustomerOverviewProps,
  Stats,
  Invoice,
  Job,
  Contact,
  SalesActivity,
  RecentEnquiry,
} from './types';

import {
  formatNumber,
  getDateConvert,
  getStatusStyle,
  fetchOrganizationStatics,
  fetchInvoiceList,
  fetchAllJobs,
  fetchOrgContacts,
  fetchAllActivityEvents,
  fetchAllEnquiries,
} from './CustomerOver.functions';
import LinearGradient from 'react-native-linear-gradient';
import { fontSize } from '../../../Common/Theme/typography';
import { stat } from 'react-native-fs';

const CustomerOverView: React.FC<CustomerOverviewProps> = ({ org_id, type }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [stats, setStats] = useState<Stats>({});
  const [invoiceList, setInvoiceList] = useState<Invoice[]>([]);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [saleActivityTypes, setSalesActivityTypes] = useState<SalesActivity[]>([]);
  const [invoicePage, setInvoicePage] = useState<number>(1);
  const [jobsPage, setJobsPage] = useState<number>(1);
  const [hasMoreInvoices, setHasMoreInvoices] = useState<boolean>(true);
  const [hasMoreJobs, setHasMoreJobs] = useState<boolean>(true);
  const [recentQueries, setRecentQueries] = useState<RecentEnquiry[]>([]);

  const controlUnitsData = useSelector(
    (state: any) => state?.GlobalAppStateData?.selectedControlUnit?.data
  );

  const getIcons = (item: string) => {
    switch (item) {
      case 'SEA':
        return <Ship size={24} color="#2563eb" />;
      case 'AIR':
        return <Plane size={24} color="#9333ea" />;
      default:
        return <Truck size={24} color="#d97706" />;
    }
  };

  const loadMoreInvoices = async () => {
    if (hasMoreInvoices && !loading) {
      try {
        setLoading(true);
        const result = await fetchInvoiceList(
          org_id,
          controlUnitsData?.id || null,
          invoicePage + 1
        );
        setInvoiceList((prev) => [...prev, ...result.invoices]);
        setHasMoreInvoices(result.hasMore);
        setInvoicePage(invoicePage + 1);
      } catch (error) {
        console.error('Error loading more invoices:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const loadMoreJobs = async () => {
    if (hasMoreJobs && !loading) {
      try {
        setLoading(true);
        const result = await fetchAllJobs(org_id, controlUnitsData?.id || null, jobsPage + 1);
        setJobs((prev) => [...prev, ...result.jobs]);
        setHasMoreJobs(result.hasMore);
        setJobsPage(jobsPage + 1);
      } catch (error) {
        console.error('Error loading more jobs:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const loadData = async () => {
    try {
      setLoading(true);
      const controlUnitId = controlUnitsData?.id || null;

      const [statsData, invoiceData, jobsData, contactsData, activitiesData, enquiriesData] =
        await Promise.all([
          fetchOrganizationStatics(org_id, controlUnitId),
          fetchInvoiceList(org_id, controlUnitId),
          fetchAllJobs(org_id, controlUnitId),
          fetchOrgContacts(org_id, controlUnitId),
          fetchAllActivityEvents(org_id, controlUnitId),
          fetchAllEnquiries(org_id, controlUnitId, type),
        ]);

      setStats(statsData);
      setInvoiceList(invoiceData.invoices);
      setHasMoreInvoices(invoiceData.hasMore);
      setJobs(jobsData.jobs);
      setHasMoreJobs(jobsData.hasMore);
      setContacts(contactsData);
      setSalesActivityTypes(activitiesData);
      setRecentQueries(enquiriesData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (org_id) {
      loadData();
    }
  }, [org_id]);

  const renderDashboardStats = () => {
    if(!stats || Object.keys(stats).length===0) return null
    return (
    <View style={styles.statsContainer}>
      <SecondaryCard styles={styles.statistics}>
        <View style={[styles.statHeader]}>
          <Text style={[styles.baseText, { width: moderateScale(86) }]}>
            Total Amount Receivable
          </Text>
          <DollarSign color="green" />
        </View>
        <View style={styles.divider} />
        <View style={[styles.statValue]}>
          <Text variant="semiBold" style={styles.amountText}>
            {stats?.currency_code || ''} {stats?.acc_receivables_amount || '0'}
          </Text>
        </View>
      </SecondaryCard>
      <SecondaryCard styles={styles.statistics}>
        <View style={[styles.statHeader, { gap: moderateScale(12) }]}>
          <Text style={styles.baseText}>Total Open Jobs</Text>
          <Briefcase color="#2563eb" />
        </View>
        <View style={styles.divider} />
        <View style={[styles.statValue]}>
          <Text variant="semiBold" style={styles.amountText}>
            {formatNumber(stats?.total_jobs || '0')}
          </Text>
        </View>
      </SecondaryCard>
      <SecondaryCard styles={styles.statistics}>
        <View style={[styles.statHeader, { gap: moderateScale(12) }]}>
          <Text style={styles.baseText}>Win Ratio</Text>
          <Text>
            <Target color="#ea580c" />
          </Text>
        </View>
        <View style={styles.divider} />
        <View style={[styles.statValue]}>
          <Text variant="semiBold" style={styles.amountText}>
            {stats?.win_ratio_percentage || '0'}%
          </Text>
        </View>
      </SecondaryCard>
    </View>

    )
  }
    // <PrimraryCard>
;

  const renderRecentEnquiries = () => {
    if (!recentQueries?.length) return null;

    return (
      <PrimraryCard>
        <View style={styles.sectionHeader}>
          <Search />
          <CardHeading title="Recent Enqueries" />
        </View>
        {recentQueries.map((item, index) => (
          <View key={item.enquiry_id || `enquiry-${index}`} style={styles.cardMargin}>
            <SecondaryCard>
              <View style={[styles.secondaryPadding, styles.enquiryContent]}>
                <View style={styles.rowBetween}>
                  <View style={styles.enquiryTypeContainer}>
                    <Text>{getIcons(item.enquiry_type || '')}</Text>
                    <Text variant="bold" style={styles.baseText}>
                      {item.enquiry_type}
                    </Text>
                  </View>
                  <Text
                    variant="bold"
                    style={[
                      styles.statusBadge,
                      {
                        backgroundColor: item.status_name === 'Quoted' ? '#fef9c3' : '#e5e7eb',
                        color: item.status_name === 'Quoted' ? '#854d0ec7' : '#1f2937',
                      },
                    ]}
                  >
                    {item.status_name}
                  </Text>
                </View>
                <Text style={styles.baseText}>{item.reference_number}</Text>
                <View style={styles.locationContainer}>
                  <View style={styles.locationItem}>
                    <Image
                      style={styles.flagImage}
                      source={{ uri: item.from_country_flag }}
                      resizeMode="cover"
                    />
                    <Text
                      numberOfLines={2}
                      ellipsizeMode="tail"
                      style={[styles.baseText, { width: 75 }]}
                    >
                      {/* {(item.from_port_name || item.from_country_name)?.length > 15
                                                ? `${(item.from_port_name || item.from_country_name)?.substring(0, 10)}...`
                                                : (item.from_port_name || item.from_country_name)
                                            } */}
                      {item.from_port_name || item.from_country_name}
                    </Text>
                  </View>
                  <MoveRight />
                  <View style={styles.locationItem}>
                    <Image
                      style={styles.flagImage}
                      source={{ uri: item.to_country_flag }}
                      resizeMode="cover"
                    />
                    <Text
                      numberOfLines={2}
                      ellipsizeMode="tail"
                      style={[styles.baseText, { width: 75 }]}
                    >
                      {/* {(item.to_port_name || item.from_country_name)?.length > 15
                                                ? `${(item.to_port_name || item.from_country_name)?.substring(0, 10)}...`
                                                : (item.to_port_name || item.from_country_name)
                                            } */}
                      {item.to_port_name || item.from_country_name}
                    </Text>
                  </View>
                </View>
              </View>
            </SecondaryCard>
          </View>
        ))}
      </PrimraryCard>
    );
  };

  const renderInvoiceList = () => {
    if (!invoiceList?.length) return null;

    return (
      <PrimraryCard>
        <View style={styles.sectionHeader}>
          <FileText color="#3377EE" />
          <CardHeading title="Customer Invoices" />
        </View>
        {invoiceList.map((item, index) => {
          // const statusStyle = getStatusStyle(item?.invoice_status_name || '');
          return (
            <View key={item?.reference_number || `invoice-${index}`} style={styles.cardMargin}>
              <SecondaryCard>
                <View style={styles.secondaryPadding}>
                  <View
                    style={[styles.rowBetween, styles.gap6, { paddingRight: moderateScale(6) }]}
                  >
                    <Text variant="semiBold" style={{ fontSize: moderateScale(12) }}>
                      {item?.reference_number || 'No Reference'}
                    </Text>
                    <View
                      style={{
                        borderRadius: moderateScale(8),
                        paddingHorizontal: moderateScale(6),
                        paddingVertical: moderateScale(2),
                        backgroundColor:
                          item?.invoice_status_name === 'sent' ? '#3377ee' : '#f59e0b',
                      }}
                    >
                      <Text style={[styles.baseText, { color: '#fff' }]}>
                        {item?.invoice_status_name || 'Unknown'}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.invoiceDetailsRow}>
                    <View style={styles.invoiceLeftContent}>
                      <Text variant="semiBold" style={styles.baseText}>
                        {item?.customer_name || 'Unknown Customer'}
                      </Text>
                      <View style={styles.sectionHeader}>
                        <CalendarDays size={16} />
                        <Text style={styles.baseText}>{item?.invoice_date || 'No Date'}</Text>
                      </View>
                      <Text variant="semiBold" style={styles.baseText}>
                        {item?.currency_code || ''} {item?.invoice_amount || '0'}
                      </Text>
                    </View>
                  </View>
                </View>
              </SecondaryCard>
            </View>
          );
        })}
        {hasMoreInvoices && (
          <View style={styles.loadMoreContainer}>
            <Text style={styles.loadMoreText} onPress={loadMoreInvoices}>
              Load More Invoices
            </Text>
          </View>
        )}
      </PrimraryCard>
    );
  };

  const renderJobs = () => {
    if (!jobs?.length) return null;

    return (
      <PrimraryCard>
        <View style={styles.sectionHeader}>
          <BriefcaseIcon color="#ea580c" />
          <CardHeading title="Recent Jobs" />
        </View>
        {jobs.map((item, index) => (
          <View key={item?.job_id || `job-${index}`} style={styles.cardMargin}>
            <SecondaryCard>
              <View style={[styles.gap4, styles.jobCardContent]}>
                <View style={styles.rowBetween}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'center',
                      alignItems: 'center',
                      gap: moderateScale(4),
                    }}
                  >
                    <Text>{getIcons(item?.mode_of_shipment)}</Text>
                    <Text variant="semiBold" style={styles.headerSubtitle}>
                      {item?.mode_of_shipment || 'Unknown Mode'}
                    </Text>
                  </View>
                  <Text
                    style={[
                      styles.jobStatus,
                      {
                        backgroundColor: item?.job_status_name === 'Draft' ? '#FEF9C3' : '#F3F4F6',
                      },
                    ]}
                  >
                    {item?.job_status_name || 'Unknown Status'}
                  </Text>
                </View>
                <Text variant="bold" style={[styles.baseText, { fontSize: moderateScale(12) }]}>
                  {item?.reference_number || 'No Reference'}
                </Text>
                <View
                  style={{
                    gap: moderateScale(12),
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    paddingHorizontal: moderateScale(2),
                  }}
                >
                  <Text>({item?.from_port_code || '--'})</Text>
                  <MoveRight size={16} /><Text>({item.to_port_code || '--'})</Text>
                </View>
                <Text style={styles.baseText}>{item?.incoterm_name || 'No Incoterm'}</Text>
                <Text style={styles.baseText}>{item?.organization_name || 'No Organization'}</Text>
              </View>
            </SecondaryCard>
          </View>
        ))}
        {hasMoreJobs && (
          <View style={styles.loadMoreContainer}>
            <Text style={styles.loadMoreText} onPress={loadMoreJobs}>
              Load More Jobs
            </Text>
          </View>
        )}
      </PrimraryCard>
    );
  };

  const renderContacts = () => {
    if (!contacts?.length) return null;

    return (
      <PrimraryCard>
        <View style={styles.sectionHeader}>
          <Users color="#4f46e5" />
          <CardHeading title="Key Contacts" />
        </View>
        {contacts.map((item, index) => (
          <View key={item?.contact_id || `contact-${index}`} style={styles.cardMargin}>
            <SecondaryCard>
              <View style={{ flexDirection: 'row', gap: moderateScale(12) }}>
                <View
                  style={{
                    width: moderateScale(40),
                    height: moderateScale(40),
                    borderRadius: moderateScale(20),
                    backgroundColor: '#3377ff',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold' }}>
                    {item?.contact_name ? item.contact_name.charAt(0).toUpperCase() : ''}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <View style={[styles.rowBetween, { flexWrap: 'wrap' }]}>
                    <Text
                      style={[
                        styles.contactName,
                        { fontSize: moderateScale(12), flex: 1, marginRight: moderateScale(8) },
                      ]}
                      variant="semiBold"
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {item?.contact_name || 'Unnamed Contact'}
                    </Text>
                    <Text
                      style={[
                        styles.contactStatus,
                        {
                          color: item?.status_id === 1 ? '#fff' : 'black',
                          backgroundColor: item?.status_id === 1 ? '#3377ff' : '#fff',
                          maxWidth: moderateScale(80),
                        },
                      ]}
                    >
                      {item?.status_id === 1 ? 'Active' : 'Inactive'}
                    </Text>
                  </View>
                  <View style={styles.primaryBadgeContainer}>
                    {item?.is_primary === 1 && <Text style={styles.primaryBadge}>Primary</Text>}
                  </View>
                  <Text style={styles.contactEmail}>{item?.emails?.[0]?.email || 'No Email'}</Text>
                </View>
              </View>
            </SecondaryCard>
          </View>
        ))}
      </PrimraryCard>
    );
  };

  const renderSalesActivities = () => {
    if (!saleActivityTypes?.length) return null;

    return (
      <PrimraryCard>
        <View style={styles.sectionHeader}>
          <Users />
          <CardHeading title="Recent Activities" />
        </View>
        {saleActivityTypes.map((item, index) => (
          <View key={item?.sales_activity_id || `activity-${index}`} style={styles.cardMargin}>
            <SecondaryCard>
              <View style={[{ flex: 1 }, styles.secondaryPadding, styles.gap4]}>
                <View style={styles.rowBetween}>
                <Text variant="semiBold" style={styles.activityTitle}>
{item?.sales_activity_title || 'No Title'}
                  </Text>
                  <Text variant="semiBold" style={styles.activityState}>
                    {item?.sales_activity_state_name || 'No State'}
                  </Text>
                </View>
                <View style={styles.activityInfo}>
                  <Text style={styles.baseText}>{getDateConvert(item?.start_date_time || '')}</Text>
                  <Text style={styles.baseText}>{item?.sales_activity_type_name || 'No Type'}</Text>
                </View>
                <View style={styles.rowBetween}>
                  <Text variant="semiBold" style={styles.activityPriority}>
                    {item?.sales_activity_priority_name || 'No Priority'}
                  </Text>
                  <Text style={styles.baseText}>
                    {item?.assigned_users?.[0]?.first_name || 'Unassigned'}
                  </Text>
                </View>
                <Text style={styles.baseText}>{item?.organization_name || 'No Organization'}</Text>
              </View>
            </SecondaryCard>
          </View>
        ))}
      </PrimraryCard>
    );
  };

  return (
    <ScrollView style={styles.gap6}>
      {renderDashboardStats()}
      {renderInvoiceList()}
      {renderJobs()}
      {renderContacts()}
      {renderSalesActivities()}
      {renderRecentEnquiries()}
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3377ee" />
        </View>
      )}
    </ScrollView>
  );
};

export default CustomerOverView;

const styles = StyleSheet.create({
  // Layout styles
  gap4: {
    gap: moderateScale(4),
  },
  gap6: {
    gap: moderateScale(6),
  },
  gap12: {
    gap: moderateScale(12),
  },
  rowBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  // Padding and margin
  primaryPadding: {
    paddingHorizontal: moderateScale(12),
    // paddingVertical: moderateScale(12)
  },
  secondaryPadding: {
    paddingHorizontal: moderateScale(12),
    paddingVertical: moderateScale(8),
  },
  cardMargin: {
    marginVertical: moderateScale(6),
  },

  // Typography
  baseText: {
    fontSize: moderateScale(11),
  },
  headerTitle: {
    fontSize: moderateScale(14),
  },
  headerSubtitle: {
    fontSize: moderateScale(11),
  },
  amountText: {
    fontSize: moderateScale(11),
  },
  contactName: {
    fontSize: moderateScale(11),
  },
  contactEmail: {
    marginTop: moderateScale(4),
    fontSize: moderateScale(11),
    color: 'gray',
  },
  activityTitle: {
    fontSize: moderateScale(12),
  },

  // Header styles
  headerContainer: {
    paddingHorizontal: moderateScale(16),
    paddingVertical: moderateScale(12),
    gap: moderateScale(6),
  },

  // Section header
  sectionHeader: {
    flexDirection: 'row',
    gap: scale(6),
    alignItems: 'center',
  },

  // Stats container
  statsContainer: {
    // gap: moderateScale(12)
    flexDirection: 'row',
    flexWrap: 'wrap',
  },

  // Common containers
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  // statValue: {
  //     // alignSelf: "flex-end"
  // },
  amountContainer: {
    alignItems: 'flex-end',
    minWidth: moderateScale(80),
    paddingLeft: moderateScale(8),
  },

  // Divider
  divider: {
    marginVertical: scale(6),
  },

  // Job styles
  jobCardContent: {
    marginVertical: moderateScale(12),
    paddingHorizontal: moderateScale(12),
  },
  jobStatus: {
    paddingHorizontal: moderateScale(8),
    paddingVertical: moderateScale(2),
    borderRadius: moderateScale(6),
    color: '#111827',
    fontSize: moderateScale(11),
  },

  // Contact styles
  contactStatus: {
    borderRadius: moderateScale(8),
    paddingHorizontal: moderateScale(6),
    paddingVertical: moderateScale(2),
    marginLeft: moderateScale(8),
    fontSize: moderateScale(11),
    maxWidth: moderateScale(80), // Ensure badge doesn't grow too wide
    flexShrink: 1, // Allow badge to shrink if needed
  },
  primaryBadgeContainer: {
    alignSelf: 'flex-start',
  },
  primaryBadge: {
    marginTop: moderateScale(4),
    fontSize: moderateScale(11),
  },

  // Activity styles
  activityState: {
    backgroundColor: '#dbeafe',
    color: '#1e40af',
    borderRadius: moderateScale(12),
    paddingHorizontal: moderateScale(6),
    paddingVertical: moderateScale(2),
    fontSize: moderateScale(11),
  },
  activityPriority: {
    backgroundColor: '#fef9c3',
    color: '#854d0e',
    borderRadius: moderateScale(6),
    paddingHorizontal: moderateScale(6),
    paddingVertical: moderateScale(2),
    fontSize: moderateScale(11),
  },
  activityInfo: {
    flexDirection: 'row',
    paddingHorizontal: moderateScale(1),
    gap: moderateScale(12),
  },

  // Enquiry styles
  enquiryContent: {
    gap: moderateScale(8),
    marginVertical: moderateScale(6),
  },
  enquiryTypeContainer: {
    flexDirection: 'row',
    gap: moderateScale(4),
    alignItems: 'center',
  },
  statusBadge: {
    borderRadius: moderateScale(12),
    paddingVertical: moderateScale(2),
    paddingHorizontal: moderateScale(8),
    fontSize: moderateScale(11),
  },
  locationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: moderateScale(12),
  },
  locationItem: {
    flexDirection: 'row',
    gap: scale(6),
    alignItems: 'center',
  },
  flagImage: {
    width: moderateScale(34),
    height: moderateScale(20),
    borderRadius: moderateScale(4),
  },

  // Invoice styles
  invoiceDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    // marginTop: moderateScale(8)
  },
  invoiceLeftContent: {
    flex: 1,
    gap: moderateScale(4),
  },
  invoiceAmountContainer: {
    // alignItems: 'flex-end',
    // minWidth: moderateScale(80),
    // paddingLeft: moderateScale(8)
  },

  // Load more styles
  loadMoreContainer: {
    alignItems: 'center',
    paddingVertical: moderateScale(12),
  },
  loadMoreText: {
    color: '#3377ee',
    fontSize: moderateScale(11),
  },

  // Loading styles
  loadingContainer: {
    paddingVertical: moderateScale(20),
    alignItems: 'center',
  },

  // Center container
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statistics: {
    backgroundColor: '#fff',
    paddingVertical: moderateScale(12),
    paddingHorizontal: moderateScale(16),
    marginVertical: moderateScale(10),
    marginHorizontal: moderateScale(12),
    borderWidth: moderateScale(1),
    borderColor: '#e2e8f0',
    borderRadius: moderateScale(12),
    // gap: scale(8),

    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,

    elevation: 4,
  },
});
