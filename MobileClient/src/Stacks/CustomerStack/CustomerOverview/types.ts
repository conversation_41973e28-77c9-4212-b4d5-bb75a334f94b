// types/CustomerOverview.types.ts

export interface CustomerOverviewProps {
  org_id: string;
  type: string;
}

export interface Stats {
  currency_code?: string;
  acc_receivables_amount?: string;
  total_jobs?: string;
  win_ratio_percentage?: string;
}

export interface Invoice {
  reference_number?: string;
  customer_name?: string;
  invoice_status_name?: string;
  invoice_date?: string;
  currency_code?: string;
  invoice_amount?: string;
}

export interface Job {
  job_id?: string;
  mode_of_shipment?: string;
  job_status_name?: string;
  incoterm_name?: string;
  organization_name?: string;
  reference_number?: string;
}

export interface Contact {
  contact_id?: string;
  contact_name?: string;
  status_id?: number;
  is_primary?: number;
  emails?: Array<{ email: string }>;
}

export interface SalesActivity {
  sales_activity_id?: string;
  sales_activity_title?: string;
  sales_activity_state_name?: string;
  start_date_time?: string;
  sales_activity_type_name?: string;
  sales_activity_priority_name?: string;
  assigned_users?: Array<{ first_name: string }>;
  organization_name?: string;
}

export interface RecentEnquiry {
  enquiry_id?: string;
  enquiry_type?: string;
  status_name?: string;
  reference_number?: string;
  from_country_flag?: string;
  from_port_name?: string;
  from_country_name?: string;
  to_country_flag?: string;
  to_port_name?: string;
  to_country_name?: string;
}

export interface ContextPayload {
  schemaName: string | null;
  contextJson: {
    account_id: string | null;
    workspace_id: string | null ;
    control_unit_id: string | null;
    org_id: string;
    account_user_id: string | null ;
  };
  transportationType: null;
  operationType: string;
}

export interface StatusStyle {
  container: {
    borderRadius: number;
    paddingHorizontal: number;
    paddingVertical: number;
    borderWidth: number;
    borderColor: string;
    backgroundColor: string;
  };
  text: {
    color: string;
  };
}