import { StyleSheet, View, ScrollView, TouchableOpacity } from 'react-native';
import { Text } from 'react-native-paper';
import React from 'react';
import { ArrowLeft, Check } from 'lucide-react-native';
import AppHeader from '../../../../Components/AppHeader';

const QuoteDetailsScreen = ({ route, navigation }) => {
  // Get quote data from navigation params or use default
  const quoteData = route?.params?.quote || {
    id: 'QT-2025-001',
    company: 'Global Shipping Co.',
    type: 'FCL',
    status: 'Active',
    price: '$2,850',
    validUntil: '15-02-2025',
    from: 'Dubai, UAE',
    to: 'Jeddah, KSA',
    service: "FCL 40' HC",
    transitTime: '18-22 days',
    priceBreakdown: [
      { label: 'Ocean/Air Freight:', amount: '$2,200' },
      { label: 'Ocean/Air Freight:', amount: '$2,200' },
      { label: 'Ocean/Air Freight:', amount: '$2,200' },
    ],
    total: '$2,850',
  };

  const handleBack = () => {
    if (navigation) {
      navigation.goBack();
    } else {
      console.log('Back pressed');
    }
  };

  const handleRequestChanges = () => {
    console.log('Request Changes pressed');
    // Add request changes logic here
  };

  const handleBook = () => {
    console.log('Book pressed');
    // Add booking logic here
  };

  const getTypeColor = (type) => {
    return type === 'FCL' ? '#3377FF' : '#27c087';
  };

  const getTypeBgColor = (type) => {
    return type === 'FCL' ? '#e3f2fd' : '#e8f5f0';
  };

  const getStatusColor = (status) => {
    return status === 'Active' ? '#27c087' : '#666';
  };

  const getStatusBgColor = (status) => {
    return status === 'Active' ? '#e8f5f0' : '#f5f5f5';
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <AppHeader title={quoteData.company}/>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Main Quote Card */}
        <View style={styles.mainCard}>
          {/* Company and Price Header */}
          <View style={styles.companyHeader}>
            <View style={styles.companyInfo}>
              <Text style={styles.companyName}>{quoteData.company}</Text>
              <View style={styles.badgeContainer}>
                <View
                  style={[styles.typeBadge, { backgroundColor: getTypeBgColor(quoteData.type) }]}
                >
                  <Text style={[styles.typeBadgeText, { color: getTypeColor(quoteData.type) }]}>
                    {quoteData.type}
                  </Text>
                </View>
                <View
                  style={[
                    styles.statusBadge,
                    { backgroundColor: getStatusBgColor(quoteData.status) },
                  ]}
                >
                  <Text
                    style={[styles.statusBadgeText, { color: getStatusColor(quoteData.status) }]}
                  >
                    {quoteData.status}
                  </Text>
                </View>
              </View>
            </View>
            <View style={styles.priceContainer}>
              <Text style={styles.price}>{quoteData.price}</Text>
              <Text style={styles.validUntil}>Valid until {quoteData.validUntil}</Text>
            </View>
          </View>

          {/* Route and Service Info */}
          <View style={styles.routeSection}>
            <View style={styles.routeRow}>
              <View style={styles.routeItem}>
                <Text style={styles.routeLabel}>From:</Text>
                <Text style={styles.routeValue}>{quoteData.from}</Text>
              </View>
              <View style={styles.routeItem}>
                <Text style={styles.routeLabel}>To:</Text>
                <Text style={styles.routeValue}>{quoteData.to}</Text>
              </View>
            </View>
            <View style={styles.routeRow}>
              <View style={styles.routeItem}>
                <Text style={styles.routeLabel}>Service:</Text>
                <Text style={styles.routeValue}>{quoteData.service}</Text>
              </View>
              <View style={styles.routeItem}>
                <Text style={styles.routeLabel}>Transit Time:</Text>
                <Text style={styles.routeValue}>{quoteData.transitTime}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Price Breakdown Section */}
        <View style={styles.priceBreakdownCard}>
          <Text style={styles.sectionTitle}>Price Breakdown</Text>
          <View style={styles.breakdownContainer}>
            {(quoteData.priceBreakdown || []).map((item, index) => (
              <View key={index} style={styles.breakdownRow}>
                <Text style={styles.breakdownLabel}>{item.label}</Text>
                <Text style={styles.breakdownAmount}>{item.amount}</Text>
              </View>
            ))}
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Total:</Text>
              <Text style={styles.totalAmount}>{quoteData.total}</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Action Buttons */}
      <View style={styles.bottomActions}>
    flexDirection: 'row',
        <View style={{flexDirection:'row',gap: 12,}}>

        <TouchableOpacity style={styles.requestChangesButton} onPress={handleRequestChanges}>
          <Text style={styles.requestChangesText}>Request Changes</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.bookButton} onPress={handleBook}>
          <View style={styles.bookButtonContent}>
            <Text style={styles.bookText}>Book</Text>
            <Check size={20} color="#fff" />
          </View>
        </TouchableOpacity>
        </View>
        <TouchableOpacity style={styles.quoteButton}>
          <View style={styles.bookButtonContent}>
            <Text style={styles.quotebuttontext}>Get Quote</Text>
            <Check size={20} color="#fff" />
          </View>
        </TouchableOpacity>

      </View>
    </View>
  );
};

export default QuoteDetailsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backButton: {
    padding: 8,
    marginRight: 12,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#191f2b',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  mainCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 18,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  companyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  companyInfo: {
    flex: 1,
    marginRight: 16,
  },
  companyName: {
    fontSize: 18,
    fontWeight: '700',
    color: '#191f2b',
    marginBottom: 8,
  },
  badgeContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  typeBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  typeBadgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusBadgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  price: {
    fontSize: 24,
    fontWeight: '700',
    color: '#27c087',
    marginBottom: 4,
  },
  validUntil: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  routeSection: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 16,
  },
  routeRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  routeItem: {
    flex: 1,
  },
  routeLabel: {
    fontSize: 12,
    color: '#a0a0ab',
    fontWeight: '500',
    marginBottom: 4,
  },
  routeValue: {
    fontSize: 14,
    color: '#191f2b',
    fontWeight: '600',
  },
  priceBreakdownCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 18,
    marginBottom: 100, // Space for bottom buttons
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#191f2b',
    marginBottom: 16,
  },
  breakdownContainer: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 16,
  },
  breakdownRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f8f8f8',
  },
  breakdownLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  breakdownAmount: {
    fontSize: 14,
    color: '#191f2b',
    fontWeight: '600',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderTopWidth: 2,
    borderTopColor: '#f0f0f0',
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 16,
    color: '#191f2b',
    fontWeight: '700',
  },
  totalAmount: {
    fontSize: 18,
    color: '#27c087',
    fontWeight: '700',
  },
  bottomActions: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    gap: 12,
  },
  requestChangesButton: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  requestChangesText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
  },
  bookButton: {
    flex: 1,
    backgroundColor: '#3377FF',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  quoteButton:{
     backgroundColor: '#f8f9fa',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  quotebuttontext:{
 fontSize: 16,
    fontWeight: '600',
    color: '#666',
  },
  bookButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  bookText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});
