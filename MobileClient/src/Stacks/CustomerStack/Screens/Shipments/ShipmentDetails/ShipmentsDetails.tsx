const DetailRow = ({ label, value, isLast = false }) => (
  <View style={[styles.detailRow, !isLast && styles.detailRowBorder]}>
    <Text style={styles.detailLabel}>{label}</Text>
    <Text style={styles.detailValue}>{value}</Text>
  </View>
);
import { StyleSheet, View, ScrollView, TouchableOpacity, FlatList, } from 'react-native';
import { DocumentPickerResponse } from "react-native-document-picker";
import React, {  useState } from 'react';
import { Button, Text } from 'react-native-paper';
import AppHeader from '../../../../../Components/AppHeader';
import { Package, Plane, Package2, Mail, Truck, Upload, File, MoreVertical, FileText } from 'lucide-react-native';
import { moderateScale, scale } from 'react-native-size-matters';
import { getStatusBgColor, getStatusColor } from '../../../../SalesManagementStack/utils/getTypeBadge';
import { pickFiles } from '../../../../ChatStack/ChatUtils/ImageUtils';
import Tabs from '../../../../../Components/Tabs';


const ShipmentsDetails = () => {
  const [activeTab, setActiveTab] = useState('Overview');
    const [uploads,SetUploads]=useState([
      { 
    title: "Bill of Lading", 
    fileName: "aaf60007-0efe-4940-a9e9-c49cab1ea645.jpg", 
    uri: "content://com.android.providers.media.documents/document/image%3A39" 
  },
  { 
    title: "Invoice", 
    fileName: "c12b3e77-19d2-4c58-92e1-74d1e812fbb4.jpg", 
    uri: "content://com.android.providers.media.documents/document/image%3A40" 
  },
  { 
    title: "Packing List", 
    fileName: "d44b0c88-8b6f-43b3-86e9-84a1c812fbc9.jpg", 
    uri: "content://com.android.providers.media.documents/document/image%3A41" 
  },
  { 
    title: "Delivery Note", 
    fileName: "e55d2d99-7a7f-44c9-93e2-95b2d812fbda.jpg", 
    uri: "content://com.android.providers.media.documents/document/image%3A42" 
  }
    ])


  const tabs = [{ key: 'Overview', label: 'Overview' },
  { key: 'Tracking', label: 'Tracking' },
  { key: 'Parties', label: 'Parties' },
  { key: 'Documents', label: 'Documents' }];

  const shipmentData = {
    awbNumber: '020-12345678',
    flightNo: 'EK 507',
    status: 'In Transit',
    deliveryStatus: 'Delivered',
    progress: 65,
    route: {
      from: 'Dubai, UAE',
      to: 'Mumbai, India',
    },
    cargo: {
      category: 'Electronics',
      type: 'General Cargo',
      quantity: '2',
      weight: '45.5 kg',
      containerType: "20' Standard",
      transportBy: 'Standard Cargo',
    },
    delivery: {
      expectedDelivery: 'Feb 15, 2024',
      deliveryCode: 'NA',
    },
    parties: {
      shipper: {
        company: 'Al Rashid Electronics LLC',
        location: 'Dubai, UAE',
      },
      consignee: {
        company: 'Mumbai Tech Solutions',
        location: 'Mumbai, India',
      },
    },
  };
  const handleUpload=async()=>{
    try {
      const docs=await pickFiles()
      if(docs){

        SetUploads((prev)=>[...prev,...docs])
        console.log("File upload initiated",docs);
      }
      
    } catch (error) {
      console.log(error)
      
    }
    // await DocumentPi

    // Logic to handle file upload
    
  }

  // const TabButton = ({ title, isActive, onPress }) => (
  //   <TouchableOpacity
  //     style={[styles.tabButton, isActive && styles.activeTabButton]}
  //     onPress={onPress}
  //   >
  //     <Text style={[styles.tabText, isActive && styles.activeTabText]}>{title}</Text>
  //   </TouchableOpacity>
  // );

  const SectionHeader = ({ icon: IconComponent, title }) => (
    <View style={styles.sectionHeader}>
      <IconComponent size={20} color="#333" />
      <Text style={styles.sectionTitle}>{title}</Text>
    </View>
  );

  const PartyCard = ({ title, company, location, titleColor }) => (
    <View style={styles.partyCard}>
      <View style={styles.partyHeader}>
        <Text style={[styles.partyTitle, { color: titleColor }]}>{title}</Text>
        <TouchableOpacity style={styles.emailButton}>
          <Mail size={20} color="#666" />
        </TouchableOpacity>
      </View>
      <View style={styles.partyContent}>
        <Text style={styles.companyName}>{company}</Text>
        <Text style={styles.location}>{location}</Text>
      </View>
    </View>
  );

const UploadDocCard = ({ title, fileName }) => {
  return (
    <View
      style={{
        flexDirection: "row",
        padding: moderateScale(8),
        borderRadius: moderateScale(8),
        backgroundColor: "#fff",
        gap: scale(12),
        // elevation: 0.4,
        marginVertical: moderateScale(6),
        alignItems: "center",
      }}
    >
      <View style={{paddding:moderateScale(12), backgroundColor:'#d8e0f1ff',padding:moderateScale(6),borderRadius:moderateScale(5)}}>
      <FileText size={moderateScale(20)} color="#3377ff" />

      </View>

      <View style={{ flex: 1,gap:moderateScale(4) }}>
        <Text style={{  fontSize: moderateScale(14)  }} variant='semiBold'>
          {title}
        </Text>

        <Text style={{ fontSize: moderateScale(12), color: "#666" }}>
          {fileName}
        </Text>
      </View>
      <View>
        <MoreVertical size={moderateScale(20)} color="#666" />
      </View>
    </View>
  );
};

  const renderOverviewContent = () => (
    <ScrollView showsVerticalScrollIndicator={false} >
      {/* AWB Number Section */}
     

      <View style={styles.awbSection}>
        <View style={styles.awbHeader}>
          <View style={styles.awbLeft}>
            <View style={styles.iconContainer}>
              <Package size={24} color="#fff" />
            </View>
            <View>
              <Text style={styles.awbLabel}>AWB Number:</Text>
              <Text style={styles.awbNumber}>{shipmentData.awbNumber}</Text>
            </View>
          </View>
          <View style={[styles.statusBadge,{backgroundColor: getStatusBgColor(shipmentData.status)}]}>
            <Text style={[styles.statusText,{color:getStatusColor(shipmentData.status)}]}>{shipmentData.status}</Text>
          </View>
        </View>

        {/* Flight and Progress */}
        <View style={styles.flightSection}>
          <View style={styles.flightRow}>
            <Text style={styles.flightLabel}>Flight No: {shipmentData.flightNo}</Text>
            <Text style={styles.deliveredText}>{shipmentData.deliveryStatus}</Text>
          </View>

          {/* Progress Bar */}
          <View style={styles.progressContainer}>
            <View style={[styles.progressBar,{backgroundColor:'#fff'}]}>
              <View style={[styles.progressFill,{backgroundColor: getStatusColor(shipmentData.status),  width: `${shipmentData.progress}%` }]} />
            </View>
            <Text style={styles.progressText}>{shipmentData.progress}% Complete</Text>
          </View>
        </View>
      </View>
   

      {/* Air Freight Route */}
      <View style={styles.section}>
        <SectionHeader icon={Plane} title="Air Freight Route" />
        <View style={styles.routeContainer}>
          {/* <DetailRow label="From:" value={shipmentData.route.from} />
          <DetailRow label="To:" value={shipmentData.route.to} isLast={true} /> */}
          <View style={{flex:1}}>
            <Text  style={styles.fromToContainer}>From :</Text>
<Text style={styles.fromToValue}  >{shipmentData.route.from}</Text>
          </View>
          <View style={{flex:1}}>
            <Text style={styles.fromToContainer}>To :</Text>
<Text>{shipmentData.route.to}</Text>
          </View>
        </View>
      </View>

      {/* Cargo Details */}
      <View style={styles.section}>
        <SectionHeader icon={Package2} title="Cargo Details" />
        <View style={styles.cargoContainer}>
          <DetailRow label="Cargo Category" value={shipmentData.cargo.category} />
          <DetailRow label="Cargo Type" value={shipmentData.cargo.type} />
          <DetailRow label="Quantity" value={shipmentData.cargo.quantity} />
          <DetailRow label="Weight" value={shipmentData.cargo.weight} />
          <DetailRow label="Container Type" value={shipmentData.cargo.containerType} />
          <DetailRow label="Transport by" value={shipmentData.cargo.transportBy} isLast={true} />
        </View>
      </View>

      {/* Delivery Information */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Package size={20} color="#333" />
          <Text style={styles.sectionTitle}>Delivery Information</Text>
        </View>
        <View style={styles.deliveryContainer}>
          <DetailRow label="Expected Delivery:" value={shipmentData.delivery.expectedDelivery} />
          <DetailRow
            label="Delivery Code:"
            value={shipmentData.delivery.deliveryCode}
            isLast={true}
          />
        </View>
      </View>
    </ScrollView>
  );

  const renderTrackingContent = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Tracking Information</Text>
      <Text style={styles.placeholderText}>Tracking details will be shown here</Text>
    </View>
  );

  const renderPartiesContent = () => (
    <>
      {/* Shipper Card */}
      <PartyCard
        title="Shipper"
        company={shipmentData.parties.shipper.company}
        location={shipmentData.parties.shipper.location}
        titleColor="#27c087"
      />

      {/* Consignee Card */}
      <PartyCard
        title="Consignee"
        company={shipmentData.parties.consignee.company}
        location={shipmentData.parties.consignee.location}
        titleColor="#47b6e6"
      />
    </>
  );

  const renderDocumentsContent = () => (
    <View style={{flex: 1,marginBottom:moderateScale(12)}}>

      <TouchableOpacity onPress={()=>handleUpload()} style={{width:'60%',alignSelf:'flex-end',flexDirection:'row',justifyContent:'center',alignItems:'center',}}>
        <View style={styles.documentButton}>
          <Upload size={22} color="#666" />
          <Text style={styles.buttonText} variant='bold'>Upload Documents</Text>
        </View>
        
        </TouchableOpacity> 
        <View style={{paddingTop:moderateScale(1)}}>
          <FlatList
          data={uploads}
          keyExtractor={(item)=>item.uri}
          renderItem={({item})=>(
             <View style={{padding:moderateScale(8),borderRadius:moderateScale(8),backgroundColor:'#fff',gap:12,elevation:0.4,marginVertical:moderateScale(6)}}> 
           <UploadDocCard title={item.title || 'Bill of Lading'} fileName={item.fileName || item.name} />
          </View>
          )}
          />
          {/* {uploads.map((item,index)=>(
             <View style={{padding:moderateScale(8),borderRadius:moderateScale(8),backgroundColor:'#fff',gap:12,elevation:0.4,marginVertical:moderateScale(12)}}> 
            <UploadDocCard key={item.uri} title={item.title || 'Bill of Lading'} fileName={item.fileName || item.name} />
          </View>
          ))} */}
        </View>



      
      </View>

  )

  const renderActiveTabContent = () => {
    switch (activeTab) {
      case 'Overview':
        return renderOverviewContent();
      case 'Tracking':
        return renderTrackingContent();
      case 'Parties':
        return renderPartiesContent();
        case 'Documents':
          return renderDocumentsContent();
      default:
        return renderOverviewContent();
    }
  };

  return (
    <View style={styles.container}>
      <AppHeader title="Shipments Details" />

      {/* Tabs */}
      <View >
        <Tabs
  tabs={tabs}
  activeTab={activeTab}
  onTabPress={(key) => setActiveTab(key)}
  showCounts={false} // or true if you want numbers beside tabs
/>

        {/* {tabs.map((tab) => (
          <TabButton
            key={tab}
            title={tab}
            isActive={activeTab === tab}
            onPress={() => setActiveTab(tab)}
          />
        ))} */}
      </View>

      <View style={styles.content}>
        {renderActiveTabContent()}
      </View>
    </View>
  );
};

export default ShipmentsDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  
  buttonText:{
    fontSize: moderateScale(14),
    color: '#666',
    // marginLeft: moderateScale(8),
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  documentButton:{
    flexDirection:'row',
    justifyContent:'center',
    alignItems:'center',
    borderRadius:moderateScale(8),
    borderColor:'#E4E4E4',
    borderWidth:moderateScale(1),
    gap:moderateScale(4),
    paddingVertical:moderateScale(8),
    paddingHorizontal:moderateScale(12),
    backgroundColor:'#fff',
    
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTabButton: {
    borderBottomColor: '#3377FF',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
  },
  activeTabText: {
    color: '#3377FF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  awbSection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
  },
  awbHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  awbLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#3377FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  awbLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  awbNumber: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
  },
  statusBadge: {
    // backgroundColor: '#e3f2fd',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    // color: '#fff',
  },
  flightSection: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 16,
  },
  flightRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  flightLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  deliveredText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
  },
  progressContainer: {
    // flexDirection: 'row',
    // alignItems: 'center',
    // justifyContent: 'space-between',
    marginBottom:moderateScale(16),
    gap: moderateScale(8),
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: '#e8e8e8',
    borderRadius: 3,
    marginRight: 12,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3377FF',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  routeContainer: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
    flexDirection: 'row',
    marginBottom:moderateScale(16)
  },
  cargoContainer: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
  },
  deliveryContainer: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  detailRowBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#f8f8f8',
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
    textAlign: 'right',
    flex: 1,
  },
  placeholderText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 20,
  },
  partyCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 1,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 1 },
    shadowRadius: 3,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  partyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  partyTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  emailButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
  },
  partyContent: {
    paddingLeft: 0,
  },
  companyName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#191f2b',
    marginBottom: 4,
  },
  location: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  fromToContainer:{
    fontSize: moderateScale(12),
    color: '#a0a0ab',
    marginBottom: 4,
  },
  fromToValue:{
     fontSize: 14,
      color: '#191f2b',
      

  }
});
