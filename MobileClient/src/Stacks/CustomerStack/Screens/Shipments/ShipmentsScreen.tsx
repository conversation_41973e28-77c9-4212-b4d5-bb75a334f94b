import { StyleSheet, View, ScrollView, TouchableWithoutFeedback } from 'react-native';
import { Text } from 'react-native-paper';
import React from 'react';
import AppHeader from '../../../../Components/AppHeader';
import { Package, Truck,Plane,Ship } from 'lucide-react-native'; // You may need to install lucide-react-native
import { moderateScale } from 'react-native-size-matters';
import { get } from 'lodash';
import { getStatusBgColor, getStatusColor } from '../../../SalesManagementStack/utils/getTypeBadge';
import { TransportationTypeIcon } from '../../../../Components/UI/Menu/CustomerAppIcons';

const ShipmentsScreen = ({route,navigation}) => {
 const shipments = [
  // Air Shipments (AWB)
  {
    id: "AWB-12345678",
    type: "Air",
    status: "In Transit",
    from: "Dubai, UAE",
    to: "Mumbai, India",
    currentLocation: "Over Arabian Sea",
    eta: "27-01-2025",
    progress: 70,
    icon: "plane",
  },
  {
    id: "AWB-98765432",
    type: "Air",
    status: "Loading",
    from: "London, UK",
    to: "New York, USA",
    currentLocation: "Heathrow Airport, London",
    eta: "29-01-2025",
    progress: 10,
    icon: "plane",
  },
  {
    id: "AWB-45678912",
    type: "Air",
    status: "Delivered",
    from: "Singapore",
    to: "Tokyo, Japan",
    currentLocation: "Tokyo Airport",
    eta: "15-01-2025",
    progress: 100,
    icon: "plane",
  },

  // Sea Shipments (HBL)
  {
    id: "HBL-2025-001",
    type: "Sea",
    status: "At Port",
    from: "Shanghai, China",
    to: "Los Angeles, USA",
    currentLocation: "Pacific Ocean",
    eta: "20-02-2025",
    progress: 65,
    icon: "ship",
  },
  {
    id: "HBL-2025-002",
    type: "Sea",
    status: "In Transit",
    from: "Hamburg, Germany",
    to: "New York, USA",
    currentLocation: "Atlantic Ocean",
    eta: "15-02-2025",
    progress: 80,
    icon: "ship",
  },
  {
    id: "HBL-2025-003",
    type: "Sea",
    status: "Loading",
    from: "Port Klang, Malaysia",
    to: "Sydney, Australia",
    currentLocation: "Port Klang",
    eta: "12-03-2025",
    progress: 20,
    icon: "ship",
  },

  // Land Shipments (CBL)
  {
    id: "CBL-2025-001",
    type: "Land",
    status: "In Transit",
    from: "Berlin, Germany",
    to: "Paris, France",
    currentLocation: "On Highway A4, Germany",
    eta: "25-01-2025",
    progress: 55,
    icon: "truck",
  },
  {
    id: "CBL-2025-002",
    type: "Land",
    status: "Loading",
    from: "Los Angeles, USA",
    to: "Chicago, USA",
    currentLocation: "LA Warehouse",
    eta: "05-02-2025",
    progress: 15,
    icon: "truck",
  },
  {
    id: "CBL-2025-003",
    type: "Land",
    status: "Delivered",
    from: "Toronto, Canada",
    to: "Vancouver, Canada",
    currentLocation: "Vancouver Depot",
    eta: "10-01-2025",
    progress: 100,
    icon: "truck",
  },
];











  const handleCardPress = (item) => {
    console.log('Card pressed:', item.id);
    // Add navigation or other logic here
    navigation.navigate('ShipmentDetails', { shipmentId: item.id });
  };

  const ShipmentCard = ({ item }) => {
    const IconComponent = TransportationTypeIcon(item.icon)

    return (
      <TouchableWithoutFeedback onPress={() => handleCardPress(item)}>
        <View style={styles.card}>
          {/* Card Header */}
          <View style={styles.cardHeader}>
            <View style={styles.headerLeft}>
              <View style={styles.iconContainer}>
                <IconComponent size={24}  />
              </View>
              <View style={styles.idContainer}>
                <Text variant="medium" style={styles.idLabel}>
                  {item.type}:
                </Text>
                <Text variant="regular" style={styles.idValue}>
                  {item.id}
                </Text>
              </View>
            </View>
            <View style={[styles.statusBadge, { backgroundColor: getStatusBgColor(item.status) }]}>
              <Text
                style={[styles.statusText, { color: getStatusColor(item.status) }]}
                variant="semiBold"
              >
                {item.status}
              </Text>
            </View>
          </View>

          {/* Route Information */}
          <View style={styles.routeContainer}>
            <View style={styles.routeItem}>
              <Text style={styles.routeLabel} variant="medium">
                From:
              </Text>
              <Text style={styles.routeValue}>{item.from}</Text>
            </View>
            <View style={styles.routeItem}>
              <Text style={styles.routeLabel} variant="medium">
                To:
              </Text>
              <Text style={styles.routeValue}>{item.to}</Text>
            </View>
          </View>

          {/* Current Location */}
          <View style={styles.locationContainer}>
            <Text style={styles.locationLabel}>Current Location</Text>
            <Text style={styles.locationValue}>{item.currentLocation}</Text>
          </View>

          {/* Progress Bar */}
          <View style={styles.progressContainer}>
            <View style={[styles.progressBar,{backgroundColor: "#fff"}]}>
              <View style={[styles.progressFill,{backgroundColor: '#3377ff',  width: `${item.progress}%` }]} />
            </View>
          </View>

          {/* ETA and Progress */}
          <View style={styles.bottomContainer}>
            <Text style={styles.etaText}>ETA: {item.eta}</Text>
            <Text style={styles.progressText}>{item.progress}% Complete</Text>
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  };

  return (
    <View style={styles.container}>
      <AppHeader title="My Shipments" showDrawer={true} />
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {shipments.map((item, index) => (
          <ShipmentCard key={item.id} item={item} />
        ))}
      </ScrollView>
    </View>
  );
};

export default ShipmentsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContainer: {
    padding: 16,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 18,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    // backgroundColor: '#3377FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  idContainer: {
    // flex: 1,
    gap: moderateScale(6),
    flexDirection:'row',
  },
  idLabel: {
    fontSize: moderateScale(12),
    color: '#a0a0ab',
   
    marginBottom: 2,
  },
  idValue: {
    fontSize: moderateScale(13),
    color: '#191f2b',
    //fontWeight: '700',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: moderateScale(11),
    //fontWeight: '600',
  },
  routeContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  routeItem: {
    flex: 1,
  },
  routeLabel: {
    fontSize: moderateScale(12),
    color: '#a0a0ab',
    //fontWeight: '500',
    marginBottom: 4,
  },
  routeValue: {
      fontSize: 14,
      color: '#191f2b',
    //fontWeight: '600',
  },
  locationContainer: {
    marginBottom: 16,
  },
  locationLabel: {
    fontSize: 12,
    color: '#a0a0ab',
    //fontWeight: '500',
    marginBottom: 4,
  },
  locationValue: {
    fontSize: 14,
    color: '#191f2b',
    //fontWeight: '600',
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressBar: {
    height: 6,
    backgroundColor: '#e8e8e8',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
     // Default color, can be dynamic based on status
    borderRadius: 3,
  },
  bottomContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  etaText: {
    fontSize: 12,
    color: '#a0a0ab',
    //fontWeight: '500',
  },
  progressText: {
    fontSize: 12,
    color: '#191f2b',
    //fontWeight: '700',
  },
});
