import React from 'react';
import { View, ScrollView, StyleSheet, TouchableOpacity, SafeAreaView, Image, KeyboardAvoidingView, Platform } from 'react-native';
import { Text, Checkbox, Button, RadioButton } from 'react-native-paper';
import { Building2, Plus, Trash2 } from 'lucide-react-native';
import AppHeader from '../../../../Components/AppHeader';
import CustomInput from '../../../../Components/UI/TextInput';
import { verticalScale, scale, moderateScale } from 'react-native-size-matters';
import { fontFamily } from '../../../../Common/Theme/typography';
import { DropdownField } from '../../../../Components/UI/Menu/DropdownModal';
import { Option, useCompanyDetailsForm } from './useCompanyDetailsForm';
import { CountryPicker } from 'react-native-country-codes-picker';
import { selectImages } from '../../../ChatStack/ChatUtils/ImageUtils';
import { TextInput as PaperTextInput } from 'react-native-paper';
import { useSelector } from 'react-redux';
import EmailInput from '../../../../Components/UI/Menu/EmailInput';
import { generateYearList } from '../../../SalesManagementStack/utils/generateTimeStringToDate';
import FastImage from 'react-native-fast-image';

const yearOptions = generateYearList();
const CompanyDetailsForm = ({ route, navigation }: any) => {
  const { companyDetails, fetchProspectDetails } = route.params;
  const {
    form,
    errors,
    handleChange,
    handleSubmit,
    industryOptions,
    companySizeOptions,
    orgTypeOptions,
    currencyOptions,
    areaOptions,
    addressTypeOptions,
    countryOptions,
    cityOptions,
    phoneTypeOptions,
    emailTypeOptions,
    socialTypeOptions,
    showCountryPicker,
    setShowCountryPicker,
    countryPickerIndex,
    setCountryPickerIndex,
    logo,
    setLogo,
    removeEmail,
    removePhone,
    removeAddress,
    removeSocial,
    restButton,
    handleEmailValidationChange,
    handleEmailChange,
    searchCities,
    handleAddressChange,
    handlePhoneChange,
  } = useCompanyDetailsForm({
    orgDetails: companyDetails.org_details,
    navigation,
    fetchProspectDetails,
  });

  const renderBlockHeader = (title: string, onPress: () => void,isMandatory?:boolean) => (
    <View style={styles.blockHeader}>
      <Text style={styles.sectionTitle}>{title}
        {isMandatory ? <Text style={{ color: 'red' }}> *</Text>:''}
      </Text>
      <TouchableOpacity onPress={onPress}>
        <Plus size={18} color="#2563EB" />
      </TouchableOpacity>
    </View>
  );
  return (
    <View style={{ flex: 1, backgroundColor: '#fff' }}>
      <AppHeader title="Edit Company Details" customHeaderStyles={{ backgroundColor: '#fff' }} />
      {/* <ScrollView contentContainerStyle={styles.container}> */}
       <KeyboardAvoidingView  
              style={{ flex: 1 }}           
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            > 
            <ScrollView  
            keyboardShouldPersistTaps="handled"
             contentContainerStyle={styles.scrollContent} 
             showsVerticalScrollIndicator={false}>
              <>
        <View
          style={{
            paddingBottom: verticalScale(30),
            paddingTop: verticalScale(10),
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <View style={{ position: 'relative' }}>
            {logo ? (
              <FastImage
                source={{ uri: logo }}
                style={{
                  height: moderateScale(100),
                  width: moderateScale(100),
                  borderRadius: 60,
                  backgroundColor: '#e5e5e5',
                }}
              />
            ) : (
              <View
                style={{
                  height: moderateScale(100),
                  width: moderateScale(100),
                  borderRadius: 60,
                  backgroundColor: '#e5e5e5',
                  alignItems:"center",
                  justifyContent:"center"
                }}
              >
              <Building2
                size={moderateScale(50)}
                color={"#bbb"}
                style={{marginRight: 0}}
              />
              </View>
            )}
            <TouchableOpacity
              style={{
                position: 'absolute',
                top: 0,
                right: 0,
                backgroundColor: '#2563EB',
                borderRadius: 999,
                padding: scale(6),
              }}
              onPress={async () => {
                const imageData = await selectImages();
                if (imageData && imageData.length > 0) {
                  setLogo(imageData[0].uri);
                  handleChange('org_logo_url', imageData[0].uri);
                }
              }}
            >
              <Plus size={16} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Basic Information</Text>

        <CustomInput
          label="Company Name"
          value={form.company_name}
          onChangeText={(text) => handleChange('company_name', text)}
          errorText={errors.company_name}
          isMandatory={true}
        />
        <CustomInput
          label="Organization Code"
          value={form.org_code}
          onChangeText={(text) => handleChange('org_code', text)}
          errorText={''}
          isMandatory={false}
        />
        <DropdownField
          label="Industry"
          value={form.industry_id?.Text}
          data={industryOptions}
          keyField="Value"
          valueField="Text"
          onSelect={(item: Option) => handleChange('industry_id', item)}
          showAsterisk={false}
          enableSearch
          searchPlaceholderText='Search industry...'
        />
        <DropdownField
          label="Company Size"
          value={form.company_size_id?.Text}
          data={companySizeOptions}
          keyField="Value"
          valueField="Text"
          onSelect={(item) => handleChange('company_size_id', item)}
          showAsterisk={false}
          type='radio'
          radioOptionContainerStyle={styles.radioContainer}
        />
        {/* <CustomInput
          label="Year Founded"
          value={form.year_founded?.toString()}
          onChangeText={(text) => handleChange('year_founded', text)}
          keyboardType="numeric"
        /> */}
        <DropdownField
          label="Year Founded"
          data={yearOptions}
          value={form.year_founded?.toString()}
          onSelect={(item) => handleChange('year_founded', item.Value)}
          keyField="Value"
          valueField="Value"
          labelField={'Value'}
        />
        <Text style={styles.sectionLabel}>
          Organization Types <Text style={styles.asterisk}>*</Text>
        </Text>
        <Text style={styles.helperText}>Select applicable organization types</Text>

        <View style={styles.checkboxGrid}>
          {orgTypeOptions.map((item: Option) => (
            <View key={item.Value} style={styles.checkboxColumn}>
              <View style={styles.checkboxRow}>
                <Checkbox.Android
                  status={form.org_types?.includes(item.Value) ? 'checked' : 'unchecked'}
                  onPress={() => {
                    const selected = form.org_types || [];
                    const newValue = selected.includes(item.Value)
                      ? selected.filter((v) => v !== item.Value)
                      : [...selected, item.Value];
                    handleChange('org_types', newValue);
                  }}
                />
                <Text style={styles.checkboxLabel}>{item.Text}</Text>
              </View>
            </View>
          ))}
          {errors.org_types && (
            <Text style={{ color: 'red', fontSize: 12 }}>{errors.org_types}</Text>
          )}
        </View>
        <DropdownField
          label="Currency"
          value={form.currency_id?.Text}
          data={currencyOptions}
          keyField="Value"
          valueField="Text"
          onSelect={(item: Option) => handleChange('currency_id', item)}
          errorText={errors.currency_id}
        />
        {/* {errors.currency_id && (
          <Text style={{ color: 'red', fontSize: 12 }}>{errors.currency_id}</Text>
        )} */}

        {renderBlockHeader('Company Address', () => {
          handleChange('addresses', [
            ...form.addresses,
            {
              address: '',
              address_type_id: null,
              country_id: null,
              city_id: null,
              postal_code: '',
            },
          ]);
        },true)}

        {form.addresses.map((item: any, index: number) => (
          <View key={index} style={styles.card}>
             <DropdownField
              label="Type"
              value={item.address_type_id?.Text}
              data={addressTypeOptions}
              keyField="Value"
              valueField="Text"
              // onSelect={(val) => handleChange(`addresses.${index}.address_type_id`, val)}
              onSelect={(val) => handleAddressChange(index,`address_type_id`, val)}
              showAsterisk={false}
              errorText={errors.addresses?.[index]?.address_type_id}
            />
            <CustomInput
              label="Address"
              value={item.address}
              // onChangeText={(text) => handleChange(`addresses.${index}.address`, text)}
              onChangeText={(val) => handleAddressChange(index,`address`, val)}
              placeholder='Enter address'
              errorText={errors.addresses?.[index]?.address}
            />
            <DropdownField
              label="Country"
              value={item.country_id?.Text}
              data={countryOptions}
              keyField="Value"
              valueField="Text"
              // onSelect={(val) => handleChange(`addresses.${index}.country_id`, val)}
              onSelect={(val) => handleAddressChange(index,`country_id`, val)}
              showAsterisk={false}
              enableSearch
              searchPlaceholderText='Search country...'
              errorText={errors.addresses?.[index]?.country_id}
            />
            <DropdownField
              label="City"
              value={item.city_id?.Text}
              data={cityOptions[index] || cityOptions?.all || []}
              keyField="Value"
              valueField="Text"
              onSelect={(val) => handleAddressChange(index,`city_id`, val)}
              showAsterisk={false}
              enableSearch
              searchPlaceholderText='Search city...'
              errorText={errors.addresses?.[index]?.city_id}
              onSearch={(text) => searchCities(text, index)}
              enablePagination={false}
            />
            <CustomInput
              label="Postal Code"
              value={item.postal_code}
              // onChangeText={(text) => handleChange(`addresses.${index}.postal_code`, text)}
              onChangeText={(val) => handleAddressChange(index,`postal_code`, val)}
              keyboardType="numeric"
              placeholder='Postal code'
              errorText={errors.addresses?.[index]?.postal_code}
            />
            {index > 0 && (
              <TouchableOpacity
                style={styles.deleteIcon}
                onPress={() => {
                  removeAddress(index);
                }}
              >
                <Trash2 size={18} color="#DC2626" />
              </TouchableOpacity>
            )}
          </View>
        ))}

        {renderBlockHeader('Phone Number', () => {
          handleChange('phone_numbers', [
            ...form.phone_numbers,
            {
              contact_phone_number_type_id: null,
              contact_phone_number: '',
              is_on_whatsapp: false,
              is_on_telegram: false,
            },
          ]);
        },true)}
        {form.phone_numbers?.map((phone: any, index: number) => (
          <View key={index} style={styles.phoneRow}>
            <DropdownField
              label="Phone Type"
              value={phone.contact_phone_number_type_id?.Text}
              data={phoneTypeOptions}
              keyField="Value"
              valueField="Text"
              onSelect={(item) =>
                handleChange(`phone_numbers.${index}.contact_phone_number_type_id`, item)
              }
              enableSearch
              searchPlaceholderText='Search phone type...'
              showAsterisk={false}
            />
            {errors?.phone_numbers?.[index]?.contact_phone_number_type_id && (
              <Text style={{ color: 'red', fontSize: 12 }}>
                {errors.phone_numbers[index].contact_phone_number_type_id}
              </Text>
            )}
            <View>
              <Text style={styles.label}>
                Phone Number
              </Text>
            </View>
            <View style={styles.phoneInputRow}>
              <TouchableOpacity
                style={[styles.countryCodeBox,{
                  width:scale(75)
                }]}
                onPress={() => {
                  setShowCountryPicker(true);
                  setCountryPickerIndex(index);
                }}
              >
                <Text style={styles.flag}>{phone.countryFlag || ''}</Text>
                <Text style={styles.countryCode}>{phone.country_code}</Text>
              </TouchableOpacity>

              <CustomInput
                value={phone.contact_phone_number}
                // onChangeText={(text: string) =>
                //   handleChange(`phone_numbers.${index}.contact_phone_number`, text)
                // }
                onChangeText={(text:string) => handlePhoneChange(index,text)}
                placeholder="Enter phone number"
                keyboardType="phone-pad"
                label=""
                style= {{  
                  marginBottom: 0 ,
                  flex:1,
                  width: scale(210),
                }}
              />
            </View>
            {errors?.phone_numbers?.[index]?.contact_phone_number && (
              <Text style={{ color: 'red', fontSize: 12 }}>
                {errors.phone_numbers[index].contact_phone_number}
              </Text>
            )}
            <View style={styles.checkRow}>
              <Checkbox.Android
                status={phone.is_on_whatsapp ? 'checked' : 'unchecked'}
                onPress={() =>
                  handleChange(`phone_numbers.${index}.is_on_whatsapp`, !phone.is_on_whatsapp)
                }
              />
              <Text>WhatsApp</Text>
              <Checkbox.Android
                status={phone.is_on_telegram ? 'checked' : 'unchecked'}
                onPress={() =>
                  handleChange(`phone_numbers.${index}.is_on_telegram`, !phone.is_on_telegram)
                }
              />
              <Text>Telegram</Text>
            </View>
            {index !== 0 && (
              <TouchableOpacity
                style={styles.deleteIcon}
                onPress={() => {
                  removePhone(index);
                }}
              >
                <Trash2 size={18} color="#DC2626" />
              </TouchableOpacity>
            )}
          </View>
        ))}

        {renderBlockHeader('Email Address', () => {
          handleChange('emails', [...form.emails, { email_type_id: null, email: '' }]);
        })}
        {form.emails.map((item, index) => (
          <View key={index} style={styles.card}>
            <DropdownField
              label="Email Type"
              value={item.email_type_id?.Text}
              data={emailTypeOptions}
              keyField="Value"
              valueField="Text"
              onSelect={(val: Option) => handleChange(`emails.${index}.email_type_id`, val)}
              enableSearch
              searchPlaceholderText='Search email type...'
            />
            {errors?.emails?.[index]?.email_type_id && (
              <Text style={{ color: 'red', fontSize: 12 }}>
                {errors?.emails[index]?.email_type_id}
              </Text>
            )}
            {/* <CustomInput
              label="Email"
              value={item.email}
              onChangeText={(text: string) => handleChange(`emails.${index}.email`, text)}
              keyboardType="email-address"
              isMandatory
            /> */}
            <EmailInput
              value={item.email}
              onChangeText={(text) => handleEmailChange(index, text)}
              placeholder="Enter your email"
              onValidationChange={(isValid) => handleEmailValidationChange(index, isValid)}
              style={{marginTop:verticalScale(5)}}
            />

            {errors?.emails?.[index]?.email && (
              <Text style={{ color: 'red', fontSize: 12 }}>{errors?.emails[index]?.email}</Text>
            )}

            {index > 0 && (
              <TouchableOpacity
                style={styles.deleteIcon}
                onPress={() => {
                  removeEmail(index);
                }}
              >
                <Trash2 size={18} color="#DC2626" />
              </TouchableOpacity>
            )}
          </View>
        ))}
        <View style={styles.rowHeader}>
          <Text style={styles.sectionTitle}>Social Media</Text>
          <TouchableOpacity
            onPress={() =>
              handleChange('social_medias', [
                ...form.social_medias,
                { social_media_type_id: null, social_media_handle: '' },
              ])
            }
          >
            <Plus size={18} color="#2563EB" />
          </TouchableOpacity>
        </View>

        {form.social_medias?.map((social: any, index: number) => (
          <View key={index} style={styles.socialRow}>
            <DropdownField
              label="Platform"
              value={social.social_media_type_id?.Text}
              data={socialTypeOptions}
              keyField="Value"
              valueField="Text"
              onSelect={(item) => handleChange(`social_medias.${index}.social_media_type_id`, item)}
              showAsterisk={false}
              enableSearch
              searchPlaceholderText='Search platform...'
            />
            <CustomInput
              label=""
              value={social.social_media_handle}
              onChangeText={(text) =>
                handleChange(`social_medias.${index}.social_media_handle`, text)
              }
              placeholder='Enter handle or URL'
              style={{marginTop:verticalScale(5)}}
            />
            {index > 0 && (
              <TouchableOpacity
                style={styles.deleteIcon}
                onPress={() => {
                  removeSocial(index);
                }}
              >
                <Trash2 size={18} color="#DC2626" />
              </TouchableOpacity>
            )}
          </View>
        ))}

        {/* Financial Info */}
        <Text style={styles.sectionTitle}>Financial Information</Text>
        <CustomInput
          label="Annual Revenue"
          value={form.annual_revenue?.toString()}
          onChangeText={(text) => handleChange('annual_revenue', text)}
          keyboardType="numeric"
          style={{textAlign:"right"}}
        />
        <CustomInput
          label="Annual Growth Rate (%)"
          value={form.annual_growth_rate?.toString()}
          onChangeText={(text) => handleChange('annual_growth_rate', text)}
          keyboardType="numeric"
          style={{textAlign:"right"}}
        />

        {/* Additional Info with radio buttons */}
        <Text style={styles.sectionTitle1}>Additional Information</Text>
        <DropdownField
          label="Area"
          value={form.area?.Text}
          data={areaOptions}
          keyField="Value"
          valueField="Text"
          onSelect={(item: Option) => handleChange('area', item)}
          enableSearch
          searchPlaceholderText='Search area...'
        />
        {errors.area && <Text style={{ color: 'red', fontSize: 12 }}>{errors.area}</Text>}

        <Text style={styles.radioLabel}>Source</Text>
        <RadioButton.Group
          value={form.category}
          onValueChange={(val) => handleChange('category', val)}
        >
          <View style={styles.row}>
            <RadioButton.Android value="internal" />
            <Text>Internal</Text>
            <RadioButton.Android value="external" />
            <Text>External</Text>
          </View>
        </RadioButton.Group>

        <Text style={styles.radioLabel}>Customer Type</Text>
        <RadioButton.Group
          value={form.customer_type}
          onValueChange={(val) => handleChange('customer_type', val)}
        >
          <View style={styles.row}>
            <RadioButton.Android value="cash" />
            <Text>Cash</Text>
            <RadioButton.Android value="credit" />
            <Text>Credit</Text>
          </View>
        </RadioButton.Group>

        {/* Business Info */}
        <Text style={styles.sectionTitle}>Business Information</Text>
        <CustomInput
          label="Expected Business Value"
          value={form.expected_business_value?.toString()}
          onChangeText={(text) => handleChange('expected_business_value', text)}
          keyboardType="numeric"
          style={{textAlign:"right"}}
        />
        <CustomInput
          label="Decision Timeline (days)"
          value={form.decision_timeline?.toString()}
          onChangeText={(text) => handleChange('decision_timeline', text)}
          keyboardType="numeric"
          style={{textAlign:"right"}}
        />
        {/* <CustomInput
          label="Pain Points"
          value={form.pain_points}
          onChangeText={(text) => handleChange('pain_points', text)}
          multiline
        /> */}
        <Text style={styles.label} variant="semiBold">Pain Points</Text>
        <PaperTextInput
          value={form.pain_points}
          onChangeText={(text) => handleChange('pain_points', text)}
          mode="outlined"
          multiline
          placeholder=""
          style={styles.textArea}
          contentStyle={{
            fontSize: moderateScale(12),
            fontFamily: fontFamily.regular,
            textAlignVertical: 'top',
            paddingVertical: verticalScale(0),
          }}
          outlineColor="#D1D5DB"
          activeOutlineColor="#2563EB"
          placeholderTextColor={"#ccc"}
          scrollEnabled={Platform.OS === "ios" ? false : true}
        />
        {/* <CustomInput
          label="Key Decision Makers"
          value={form.key_decision_makers}
          onChangeText={(text) => handleChange('key_decision_makers', text)}
          multiline
        /> */}
        <Text style={styles.label} variant="semiBold">Key Decision Makers</Text>
        <PaperTextInput
          value={form.key_decision_makers}
          onChangeText={(text) => handleChange('key_decision_makers', text)}
          mode="outlined"
          multiline
          placeholder=""
          style={styles.textArea}
          contentStyle={{
            fontSize: moderateScale(12),
            fontFamily: fontFamily.regular,
            textAlignVertical: 'top',
            paddingVertical: verticalScale(0),
          }}
          outlineColor="#D1D5DB"
          activeOutlineColor="#2563EB"
          placeholderTextColor={"#ccc"}
          scrollEnabled={Platform.OS === "ios" ? false : true}
        />
        {/* <CustomInput
          label="Decision Criteria"
          value={form.decision_criteria}
          onChangeText={(text) => handleChange('decision_criteria', text)}
          multiline
        /> */}
         <Text style={styles.label} variant="semiBold">Decision Criteria</Text>
        <PaperTextInput
          value={form.decision_criteria}
          onChangeText={(text) => handleChange('decision_criteria', text)}
          mode="outlined"
          multiline
          placeholder=""
          style={styles.textArea}
          contentStyle={{
            fontSize: moderateScale(12),
            fontFamily: fontFamily.regular,
            textAlignVertical: 'top',
            paddingVertical: verticalScale(0),
          }}
          outlineColor="#D1D5DB"
          activeOutlineColor="#2563EB"
          placeholderTextColor={"#ccc"}
          scrollEnabled={Platform.OS === "ios" ? false : true}
        />
        </>
      </ScrollView>
      </KeyboardAvoidingView>
      <SafeAreaView>
        <View style={styles.actionRow}>
          <Button
            mode="contained"
            style={styles.cancelButton}
            textColor="#000"
            onPress={restButton}
          >
            Reset
          </Button>
          <Button
            mode="contained"
            style={styles.saveButton}
            textColor="#fff"
            onPress={handleSubmit}
          >
            Save Changes
          </Button>
        </View>
      </SafeAreaView>
      <CountryPicker
        show={showCountryPicker}
        pickerButtonOnPress={(item) => {
          if (countryPickerIndex !== null) {
            const updated = [...form.phone_numbers];
            updated[countryPickerIndex].countryCode = item.dial_code;
            updated[countryPickerIndex].countryFlag = item.flag;
            handleChange('phone_numbers', updated);
          }
          setShowCountryPicker(false);
          setCountryPickerIndex(null);
        }}
        lang="en"
        style={{ modal: { height: '80%' } }}
        onBackdropPress={() => setShowCountryPicker(false)}
      />
    </View>
  );
};

export default CompanyDetailsForm;

const styles = StyleSheet.create({
  container: {
    padding: scale(16),
    paddingBottom: verticalScale(30),
    backgroundColor: '#fff',
    paddingTop: 0,
  },
    scrollContent: {
    paddingHorizontal: scale(16),
  },
  sectionTitle: {
    fontSize: moderateScale(15),
    fontFamily: fontFamily.semiBold,
    marginBottom: verticalScale(6),
    marginTop:moderateScale(8),
    color: '#1F2937',
  },
  sectionTitle1: {
    fontSize: moderateScale(15),
    fontFamily: fontFamily.semiBold,
    marginBottom: verticalScale(6),
    marginTop:moderateScale(16),
    color: '#1F2937',
  },
  card: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 6,
    padding: moderateScale(12),
    marginBottom: verticalScale(12),
    backgroundColor: '#F9FAFB',
    position: 'relative',
  },
  deleteIcon: {
    position: 'absolute',
    top: scale(6),
    right: scale(6),
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(10),
    marginBottom:moderateScale(4)
  },
  radioLabel: {
    fontFamily: fontFamily.medium,
    fontSize: moderateScale(14),
    marginVertical: verticalScale(2),
  },
  blockHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: verticalScale(20),
  },
  rowHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionHeader: {
    fontSize: moderateScale(15),
    fontFamily: fontFamily.semiBold,
    color: '#1F2937',
    marginVertical: verticalScale(10),
  },
  countryCodeBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: scale(8),
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    height: verticalScale(28),
  },
  flag: {
    fontSize: moderateScale(16),
    marginRight: scale(4),
  },
  countryCode: {
    fontSize: moderateScale(14),
    color: '#1F2937',
    fontFamily: fontFamily.medium,
  },
  phoneInputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(10),
    marginBottom: verticalScale(10),
  },
  phoneRow: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 6,
    padding: moderateScale(12),
    marginBottom: verticalScale(12),
    backgroundColor: '#F9FAFB',
    position: 'relative',
  },
  checkRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(10),
    marginTop: verticalScale(6),
  },
  socialRow: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 6,
    padding: moderateScale(12),
    marginBottom: verticalScale(12),
    backgroundColor: '#F9FAFB',
    position: 'relative',
  },
  label: {
    fontSize: moderateScale(12),
    marginVertical: 4,
    color: '#111827',
    marginBottom: verticalScale(5),
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    gap: scale(10),
    paddingVertical: verticalScale(10),
  },
  cancelButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: '#E5E7EB',
  },
  saveButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: '#2563EB',
  },
  checkboxGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: verticalScale(10),
  },
  checkboxColumn: {
    width: '48%',
    marginBottom: verticalScale(6),
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkboxLabel: {
    fontSize: moderateScale(11),
    fontFamily: fontFamily.regular,
    flexShrink: 1,
  },
  helperText: {
    fontSize: moderateScale(10),
    color: '#6B7280',
    marginBottom: verticalScale(6),
  },
  asterisk: {
    color: 'red',
  },
  sectionLabel: {
    fontFamily: fontFamily.semiBold,
    fontSize: moderateScale(13),
    marginBottom: verticalScale(5),
    marginTop: verticalScale(10),
  },
   textArea: {
    borderRadius: 6,
    backgroundColor: '#fff',
    fontFamily: fontFamily.regular,
    height: verticalScale(100),
    marginBottom: verticalScale(8),
  },
   radioContainer:{
      borderWidth:0,
      backgroundColor:"",
      paddingHorizontal:scale(2.5),
      marginHorizontal:0,
      marginBottom:0,
      paddingVertical:0,
      paddingTop:verticalScale(5),
    }
});
