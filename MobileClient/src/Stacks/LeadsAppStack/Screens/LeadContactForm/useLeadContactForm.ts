import { useEffect, useRef, useState } from 'react';
import {
  getAccountId,
  getSchemaName,
  getSelectedWorkspaceId,
  getUserId,
} from '../../../../Common/Utils/Storage';
import ApiClient from '../../../../Common/API/ApiClient';
import { useSelector } from 'react-redux';
import { v4 as uuidv4 } from 'uuid';
import { showToast } from '../../../../Components/AppToaster/AppToaster';
import { ContactType } from '../../../SalesManagementStack/screens/ProspectDetails/useProspectDetails';
import { countryCodes } from '../../../../../node_modules/react-native-country-codes-picker/constants/countryCodes';

interface Option {
  Text: string;
  Value: string;
}

interface PhoneType {
  number: string;
  type: Option | null;
  whatsapp: boolean;
  telegram: boolean;
  countryCode: string;
  countryFlag: string;
}

const masterData = {
  genders: [
    { id: 'Male', name: 'Male' },
    { id: 'Female', name: 'Female' },
    { id: 'Other', name: 'Other' },
  ],
  departments: [
    { id: 'Sales', name: 'Sales' },
    { id: 'Marketing', name: 'Marketing' },
    { id: 'Engineering', name: 'Engineering' },
    { id: 'Finance', name: 'Finance' },
    { id: 'HR', name: 'Human Resources' },
    { id: 'Operations', name: 'Operations' },
    { id: 'IT', name: 'Information Technology' },
    { id: 'Legal', name: 'Legal' },
    { id: 'Executive', name: 'Executive' },
  ],
  roles: [
    { id: 'CEO', name: 'Chief Executive Officer' },
    { id: 'CTO', name: 'Chief Technology Officer' },
    { id: 'CFO', name: 'Chief Financial Officer' },
    { id: 'Manager', name: 'Manager' },
    { id: 'Director', name: 'Director' },
    { id: 'Executive', name: 'Executive' },
    { id: 'Lead', name: 'Team Lead' },
    { id: 'Senior', name: 'Senior Professional' },
    { id: 'Junior', name: 'Junior Professional' },
    { id: 'Associate', name: 'Associate' },
    { id: 'Analyst', name: 'Analyst' },
    { id: 'Coordinator', name: 'Coordinator' },
  ],
  statuses: [
    { id: 'Active', name: 'Active' },
    { id: 'Inactive', name: 'Inactive' },
    { id: 'Pending', name: 'Pending' },
  ],
};
const getCountryFlag = (dialCode: string): string => {
  const match = countryCodes?.find((c) => c.dial_code === dialCode);
  return match?.flag || '🌐';
};

export const useContactForm = (dropdowns: {
  emailTypes: Option[];
  phoneTypes: Option[];
  socialTypes: Option[];
  ageGroups: Option[];
  org_id: string;
  navigation: any;
  contact?: ContactType;
  fetchContactsAndMasters: () => void;
}) => {
  const genderOptions = masterData.genders.map(({ id, name }) => ({ Text: name, Value: id }));
  const departments = masterData.departments.map(({ id, name }) => ({ Text: name, Value: id }));
  const roles = masterData.roles.map(({ id, name }) => ({ Text: name, Value: id }));
  const statuses = masterData.statuses.map(({ id, name }) => ({ Text: name, Value: id }));
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [countryPickerIndex, setCountryPickerIndex] = useState<number | null>(null);
  const [showCountryPicker, setShowCountryPicker] = useState(false);
  const [form, setForm] = useState<any>({
      contactName: '',
      firstName: '',
      lastName: '',
      emails: [],
      phones: [],
      socials: [],
      gender: null,
      ageGroup: null,
      department: null,
      role: null,
      decisionMaker: false,
      primaryContact: false,
      status: true,
    });
    const initialFormRef = useRef<any>(null);
  const getInitialFormState = (contact: any): any => {
      if (!contact) {
        return {
          contactName: '',
          firstName: '',
          lastName: '',
          emails: [],
          phones: [],
          socials: [],
          gender: null,
          ageGroup: null,
          department: null,
          role: null,
          decisionMaker: false,
          primaryContact: false,
          status: true,
        };
      }

      return {
        contactName: contact.contact_name || '',
        firstName: contact.first_name || '',
        lastName: contact.last_name || '',
        emails: (contact.emails || []).map((e) => ({
          email: e.email,
          type: dropdowns.emailTypes.find((et) => et.Value == e.email_type_id) || null,
        })),
        phones: (contact.phone_numbers || []).map((p) => ({
          number: p.contact_phone_number,
          type: dropdowns.phoneTypes.find((pt) => pt.Value == p.contact_phone_number_type_id) || null,
          whatsapp: !!p.is_on_whatsapp,
          telegram: !!p.is_on_telegram,
          countryCode: p.country_code || '+91',
          countryFlag: getCountryFlag(p.country_code || '+91'),
        })),
        socials: (contact.social_handles || []).map((s) => ({
          handle: s.social_media_handle,
          type: dropdowns.socialTypes.find((st) => st.Value == s.social_media_type_id) || null,
        })),
        gender: genderOptions.find((g) => g.Text == contact.gender) || null,
        ageGroup: dropdowns.ageGroups.find((a) => a.Value == contact.age_group_id) || null,
        department: departments.find((d) => d.Text === contact.department) || null,
        role: roles.find((r) => r.Value == contact.role) || null,
        decisionMaker: contact.decision_maker === 1,
        primaryContact: contact.is_primary === 1,
        // status: statuses.find((s) => s.Text === contact.status_name) || statuses[0],
        status_id: contact.status_id === 1 ? true : false
      };
    };
    useEffect(() => {
      if (!initialFormRef.current && dropdowns.contact) {
        const initialForm = getInitialFormState(dropdowns.contact);
        initialFormRef.current = initialForm;
        setForm(initialForm);
      }
    }, [
      dropdowns.contact,
      dropdowns.emailTypes,
      dropdowns.phoneTypes,
      dropdowns.socialTypes,
      dropdowns.ageGroups,
    ]);
    const resetForm = () => {
      if (initialFormRef.current) {
        setForm(initialFormRef.current);
        setErrors({});
      } else {
        const fresh = getInitialFormState(null);
        setForm(fresh);
        setErrors({});
      }
    };

  const controlUnitId = useSelector(
    (state: any) => state.GlobalAppStateData?.selectedControlUnit?.data?.id
  );

  const handleChange = (key: string, value: any) => {
    setForm((prev) => ({ ...prev, [key]: value }))
    if(errors[key]){
      setErrors((prev) => ({...prev,[key]:""}))
    }
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    // Basic fields
    if (!form.contactName.trim()) newErrors.contactName = 'Contact name is required';
    // if (!form.firstName.trim()) newErrors.firstName = 'First name is required';
    // if (!form.lastName.trim()) newErrors.lastName = 'Last name is required';
    // if (!form.gender) newErrors.gender = 'Gender is required';
    // if (!form.ageGroup) newErrors.ageGroup = 'Age group is required';

    // Email validations
    form.emails.forEach((email, index) => {
      if (!email.type) newErrors[`email_type_${index}`] = 'Email type is required';
      if (!email.email.trim()) newErrors[`email_value_${index}`] = 'Email address is required';
      if (!email.emailValidation) newErrors[`email_value_${index}`] = 'Invalid email address';
    });

    // Phone validations
    form.phones.forEach((phone, index) => {
      if (!phone.type) newErrors[`phone_type_${index}`] = 'Phone type is required';
      if (!/^\d+$/.test(phone.number.trim())) {
        newErrors[`phone_number_${index}`] = 'Phone number must contain only digits';
      }
    });

    // Social media validations
    form.socials.forEach((social, index) => {
      if (!social.type) newErrors[`social_type_${index}`] = 'Social media type is required';
      if (!social.handle.trim()) newErrors[`social_handle_${index}`] = 'Handle/URL is required';
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    try {
      if (!validateForm()) {
        showToast.error('Please fill all required fields');
        return;
      }
      const schemaName = await getSchemaName();
      const account_id = await getAccountId();
      const workspace_id = await getSelectedWorkspaceId();
      const account_user_id = await getUserId();

      const isUpdate = !!dropdowns.contact?.contact_id;

      let deletable_email_ids: string[] = [];
      let deletable_phone_no_ids: string[] = [];
      let deletable_social_media_ids: string[] = [];

      if (isUpdate) {
        // Deleted Emails
        const oldEmails = dropdowns.contact?.emails || [];
        deletable_email_ids = oldEmails
          .filter((oldEmail) => !form.emails.some((newEmail) => newEmail.email === oldEmail.email))
          .map((e) => e.email_id);

        // Deleted Phones
        const oldPhones = dropdowns.contact?.phone_numbers || [];
        deletable_phone_no_ids = oldPhones
          .filter(
            (oldPhone) =>
              !form.phones.some((newPhone) => newPhone.number === oldPhone.contact_phone_number)
          )
          .map((p) => p.phone_no_id);

        // Deleted Socials
        const oldSocials = dropdowns.contact?.social_handles || [];
        deletable_social_media_ids = oldSocials
          .filter(
            (oldSocial) =>
              !form.socials.some((newSocial) => newSocial.handle === oldSocial.social_media_handle)
          )
          .map((s) => s.social_media_id);
      }
      const payload = {
        contextJson: {
          account_user_id,
          account_id,
          workspace_id,
          control_unit_id: controlUnitId,
        },
        [isUpdate ? 'contactData' : 'contactJson']: {
          contact_id: isUpdate ? dropdowns.contact.contact_id : uuidv4(),
          org_id: dropdowns.org_id,
          contact_name: form.contactName,
          // first_name: form.firstName,
          // last_name: form.lastName,
          // gender: form.gender?.Text || '',
          // age_group_id: form.ageGroup?.Value || '',
          decision_maker: form.decisionMaker,
          is_primary: form.primaryContact,
          department: form.department?.Text || '',
          role: form.role?.Value || '',
          status_id: form.status_id ? 1 : 0,

          emails: form.emails.map((e, i) => ({
            ...(isUpdate &&
              dropdowns.contact?.emails?.[i]?.email_id && {
                email_id: dropdowns.contact.emails[i].email_id,
              }),
            email: e.email,
            email_type_id: e.type?.Value,
            org_id: dropdowns.org_id,
          })),

          phone_numbers: form.phones.map((p, i) => ({
            ...(isUpdate &&
              dropdowns.contact?.phone_numbers?.[i]?.phone_no_id && {
                phone_no_id: dropdowns.contact.phone_numbers[i].phone_no_id,
              }),
            contact_phone_number: p.number,
            contact_phone_number_type_id: p.type?.Value,
            is_on_whatsapp: p.whatsapp,
            is_on_telegram: p.telegram,
            country_code: p.countryCode,
            org_id: dropdowns.org_id,
          })),

          social_handles: form.socials.map((s, i) => ({
            ...(isUpdate &&
              dropdowns.contact?.social_handles?.[i]?.social_media_id && {
                social_media_id: dropdowns.contact.social_handles[i].social_media_id,
              }),
            social_media_type_id: s.type?.Value,
            social_media_handle: s.handle,
            org_id: dropdowns.org_id,
          })),
          ...(isUpdate && {
            deletable_email_ids,
            deletable_phone_no_ids,
            deletable_social_media_ids,
          }),
        },
      };

      const endpoint = isUpdate ? '/updateCrmContactProfile' : '/createCrmContactProfile';
      const response = await ApiClient.post(
        endpoint,
        JSON.stringify({ schemaName, payloadJson: JSON.stringify(payload) })
      );

      if (response.data?.success) {
        showToast.success(isUpdate ? "Contact updated successfully" : 'Contact created successfully');
        dropdowns.fetchContactsAndMasters();
        dropdowns.navigation.goBack();
      } else {
        showToast.error('Submission failed!');
      }
    } catch (err) {
      console.error('Submit error:', err);
      showToast.error('Submission failed!');
    }
  };
  const updatePhoneCountry = (index: number, item: any) => {
    const updated = [...form.phones];
    updated[index].countryCode = item.dial_code;
    updated[index].countryFlag = item.flag;
    setForm((prev) => ({ ...prev, phones: updated }));
  };
  const handleFieldChange = (
    section: "emails" | "phones" | "socials",
    index: number,
    field: string,
    value: string,
    errorKey?: string
  ) => {
    setForm(prev => {
      const updated = [...prev[section]];
      updated[index][field] = value;
      return { ...prev, [section]: updated };
    });

    if (errorKey) {
      setErrors(prev => {
        if (!(errorKey in prev)) return prev;
        const updatedErrors = { ...prev };
        delete updatedErrors[errorKey];
        return updatedErrors;
      });
    }
  };

  return {
    form,
    handleChange,
    handleSubmit,
    addEmail: () =>
      setForm((prev) => ({ ...prev, emails: [...prev.emails, { type: dropdowns.emailTypes[0], email: '' }] })),
    handleEmailChange: (i: number, k: string, v: any) => {
      const updated = [...form.emails];
      updated[i][k] = v;
      setForm((prev) => ({ ...prev, emails: updated }));

      let errorKey: string | undefined;

      if (k === 'type') {
        errorKey = `email_type_${i}`;
        handleFieldChange('emails', i, k, v, errorKey);
      }

    },
    removeEmail: (i: number) =>
      setForm((prev) => ({ ...prev, emails: prev.emails.filter((_, idx) => idx !== i) })),
    addPhone: () =>
      setForm((prev) => ({
        ...prev,
        phones: [
          ...prev.phones,
          {
            type: dropdowns.phoneTypes[0],
            number: '',
            whatsapp: false,
            telegram: false,
            countryCode: '+91',
            countryFlag: '🇮🇳',
          },
        ],
      })),
    // handlePhoneChange: (i: number, k: string, v: any) => {
    //   const updated = [...form.phones];
    //   updated[i][k] = v;
    //   setForm((prev) => ({ ...prev, phones: updated }));
    // },
    handlePhoneChange: (i: number, k: string, v: any) => {
      let errorKey: string | undefined;

      if (k === 'type') {
        errorKey = `phone_type_${i}`;
      } else if (k === 'number') {
        errorKey = `phone_number_${i}`;
      }

      handleFieldChange('phones', i, k, v, errorKey);
    },
    removePhone: (i: number) =>
      setForm((prev) => ({ ...prev, phones: prev.phones.filter((_, idx) => idx !== i) })),
    togglePhoneFlag: (i: number, key: 'whatsapp' | 'telegram') => {
      const updated = [...form.phones];
      updated[i][key] = !updated[i][key];
      setForm((prev) => ({ ...prev, phones: updated }));
    },
    addSocial: () =>
      setForm((prev) => ({ ...prev, socials: [...prev.socials, { type: dropdowns.socialTypes[0], handle: '' }] })),
    // handleSocialChange: (i: number, k: string, v: any) => {
    //   const updated = [...form.socials];
    //   updated[i][k] = v;
    //   setForm((prev) => ({ ...prev, socials: updated }));
    // },
    handleSocialChange: (i: number, k: string, v: any) => {
      let errorKey: string | undefined;

      if (k === 'type') {
        errorKey = `social_type_${i}`;
      } else if (k === 'handle') {
        errorKey = `social_handle_${i}`;
      }

      handleFieldChange('socials', i, k, v, errorKey);
    },
    removeSocial: (i: number) =>
      setForm((prev) => ({ ...prev, socials: prev.socials.filter((_, idx) => idx !== i) })),
    emailTypes: dropdowns.emailTypes,
    phoneTypes: dropdowns.phoneTypes,
    socialTypes: dropdowns.socialTypes,
    ageGroups: dropdowns.ageGroups,
    genderOptions,
    departments,
    roles,
    statuses,
    errors,
    setShowCountryPicker,
    showCountryPicker,
    setCountryPickerIndex,
    countryPickerIndex,
    updatePhoneCountry,
    resetForm,
    handleFieldChange
  };
};
