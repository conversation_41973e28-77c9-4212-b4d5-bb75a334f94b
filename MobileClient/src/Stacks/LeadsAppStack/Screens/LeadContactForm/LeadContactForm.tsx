import React from 'react';
import { View, ScrollView, TouchableOpacity, StyleSheet, SafeAreaView, KeyboardAvoidingView, Platform } from 'react-native';
import { Text, Checkbox, Button, Switch } from 'react-native-paper';
import { verticalScale, scale, moderateScale } from 'react-native-size-matters';
import { DropdownField } from '../../../../Components/UI/Menu/DropdownModal';
import { Plus, Trash2 } from 'lucide-react-native';
import { useContactForm } from './useLeadContactForm';
import { fontFamily } from '../../../../Common/Theme/typography';
import AppHeader from '../../../../Components/AppHeader';
import CustomInput from '../../../../Components/UI/TextInput';
import { CountryPicker } from 'react-native-country-codes-picker';
import EmailInput from '../../../../Components/UI/Menu/EmailInput';

const ContactForm = ({ route, navigation }: any) => {
  const {
    emailTypes,
    phoneTypes,
    socialTypes,
    ageGroups,
    org_id,
    fetchContactsAndMasters,
    contact,
  } = route.params || {};

  const props = useContactForm({
    emailTypes,
    phoneTypes,
    socialTypes,
    ageGroups,
    org_id,
    navigation,
    fetchContactsAndMasters,
    contact,
  });

  return (
    <View style={{ backgroundColor: '#fff', flex: 1 }}>
      <AppHeader
        title={contact ? 'Update Contact' : 'Create Contact'}
        customHeaderStyles={{ backgroundColor: '#fff' }}
      />
       <KeyboardAvoidingView  
              style={{ flex: 1 }}           
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            > 
            <ScrollView  
            keyboardShouldPersistTaps="handled"
             contentContainerStyle={styles.container} 
             showsVerticalScrollIndicator={false}>
      {/* <ScrollView contentContainerStyle={styles.container} showsVerticalScrollIndicator={false}> */}
        <Text style={styles.sectionHeader}>Basic Information</Text>

        <CustomInput
          label="Contact Name"
          value={props.form.contactName}
          onChangeText={(text: string) => props.handleChange('contactName', text)}
          placeholder="Enter contact name"
          isMandatory
          errorText={props.errors.contactName}
          maxLength={undefined}
        />
        {/* <CustomInput
          label="First Name"
          value={props.form.firstName}
          onChangeText={(text: string) => props.handleChange('firstName', text)}
          placeholder="Enter first name"
          isMandatory
          errorText={props.errors.firstName}
        />
        <CustomInput
          label="Last Name"
          value={props.form.lastName}
          onChangeText={(text: string) => props.handleChange('lastName', text)}
          placeholder="Enter last name"
          isMandatory
          errorText={props.errors.lastName}
        /> */}

        {/* --- Email Addresses --- */}
        <View style={styles.rowHeader}>
          <Text style={styles.sectionHeader}>Email Addresses</Text>
          <TouchableOpacity onPress={props.addEmail}>
            <Plus size={18} color="#2563EB" />
          </TouchableOpacity>
        </View>

        {props.form.emails.length === 0 ? (
          <Text style={styles.emptyText}>No email addresses added.</Text>
        ) : (props.form.emails.map((item, index) => (
          <View key={index} style={styles.card}>
            <DropdownField
              label="Email Type"
              value={item.type}
              data={props.emailTypes}
              keyField="Value"
              valueField="Text"
              onSelect={(val) => props.handleEmailChange(index, 'type', val)}
              enableSearch
              searchPlaceholderText='Search email type...'
              errorText={props.errors[`email_type_${index}`]}
            />
            {/* {props.errors[`email_type_${index}`] && (
              <Text style={styles.errorText}>{props.errors[`email_type_${index}`]}</Text>
            )} */}
            {/* <CustomInput
              label="Email Address"
              value={item.email}
              onChangeText={(text: string) => props.handleEmailChange(index, 'email', text)}
              placeholder="Enter email address"
              errorText={props.errors[`email_value_${index}`]}
              keyboardType="email-address"
              isMandatory
            /> */}
             <EmailInput
                label="Email"
                value={item.email}
                onChangeText={text =>
                  props.handleFieldChange("emails", index, "email", text, `email_value_${index}`)
                }
                placeholder="Enter your email"
                isMandatory
                errorText={props.errors[`email_value_${index}`]}
                onValidationChange={(valid: boolean) => props.handleEmailChange(index, 'emailValidation', valid)}
              />
            <TouchableOpacity style={styles.deleteIcon} onPress={() => props.removeEmail(index)}>
              <Trash2 size={18} color="#DC2626" />
            </TouchableOpacity>
          </View>
        )))}

        {/* --- Phone Numbers --- */}
        <View style={styles.rowHeader}>
          <Text style={styles.sectionHeader}>Phone Numbers</Text>
          <TouchableOpacity onPress={props.addPhone}>
            <Plus size={18} color="#2563EB" />
          </TouchableOpacity>
        </View>

        {props.form.phones.length === 0 ? (
          <Text style={styles.emptyText}>No phone numbers added.</Text>
        ) : (props.form.phones.map((item, index) => (
          <View key={index} style={styles.card}>
            <DropdownField
              label="Phone Type"
              value={item.type}
              data={props.phoneTypes}
              keyField="Value"
              valueField="Text"
              onSelect={(val) => props.handlePhoneChange(index, 'type', val)}
              enableSearch
              searchPlaceholderText='Search phone type...'
              errorText={props.errors[`phone_type_${index}`]}
            />
            {/* {props.errors[`phone_type_${index}`] && (
              <Text style={styles.errorText}>{props.errors[`phone_type_${index}`]}</Text>
            )} */}
            <Text style={styles.label} variant="semiBold">
              Phone Number
              <Text style={{ color: 'red' }} variant="semiBold">
                {' '}
                *
              </Text>
            </Text>
            <View style={styles.phoneRow}>
              <TouchableOpacity
                style={[styles.countryCodeBox,{
                  width:scale(75)
                }]}
                onPress={() => {
                  props.setCountryPickerIndex(index);
                  props.setShowCountryPicker(true);
                }}
              >
                <Text style={styles.flag}>{item.countryFlag}</Text>
                <Text style={styles.countryCode}>{item.countryCode}</Text>
              </TouchableOpacity>

              <CustomInput
                value={item.number}
                onChangeText={(text: string) => props.handlePhoneChange(index, 'number', text)}
                placeholder="Enter phone number"
                keyboardType="phone-pad"
                isMandatory
                errorText={''}
                label={''}
                style={{  marginBottom: 0 ,
                  flex:1,width: scale(210),
                }}
              />
            </View>

            {props.errors[`phone_number_${index}`] && (
              <Text style={styles.errorText}>{props.errors[`phone_number_${index}`]}</Text>
            )}
            <View style={styles.badgeRow}>
              <Checkbox.Android
                status={item.whatsapp ? 'checked' : 'unchecked'}
                onPress={() => props.togglePhoneFlag(index, 'whatsapp')}
              />
              <Text>WhatsApp</Text>
              <Checkbox.Android
                status={item.telegram ? 'checked' : 'unchecked'}
                onPress={() => props.togglePhoneFlag(index, 'telegram')}
              />
              <Text>Telegram</Text>
            </View>
            <TouchableOpacity style={styles.deleteIcon} onPress={() => props.removePhone(index)}>
              <Trash2 size={18} color="#DC2626" />
            </TouchableOpacity>
          </View>
        )))}

        {/* --- Social Media Handles --- */}
        <View style={styles.rowHeader}>
          <Text style={styles.sectionHeader}>Social Media Handles</Text>
          <TouchableOpacity onPress={props.addSocial}>
            <Plus size={18} color="#2563EB" />
          </TouchableOpacity>
        </View>

        {props.form.socials.length === 0 ? (
          <Text style={styles.emptyText}>No social media handles added.</Text>
        ) : (props.form.socials.map((item, index) => (
          <View key={index} style={styles.card}>
            <DropdownField
              label="Social Media Type"
              value={item.type}
              data={props.socialTypes}
              keyField="Value"
              valueField="Text"
              onSelect={(val) => props.handleSocialChange(index, 'type', val)}
              enableSearch
              searchPlaceholderText='Search social media type...'
              errorText={props.errors[`social_type_${index}`]}
            />
            {/* {props.errors[`social_type_${index}`] && (
              <Text style={styles.errorText}>{props.errors[`social_type_${index}`]}</Text>
            )} */}
            <CustomInput
              label="Handle / URL"
              value={item.handle}
              onChangeText={(text: string) => props.handleSocialChange(index, 'handle', text)}
              placeholder="Enter handle or URL"
              errorText={props.errors[`social_handle_${index}`]}
              isMandatory
            />
            <TouchableOpacity style={styles.deleteIcon} onPress={() => props.removeSocial(index)}>
              <Trash2 size={18} color="#DC2626" />
            </TouchableOpacity>
          </View>
        )))}

        {/* --- Personal Information --- */}
        {/* <Text style={styles.sectionHeader}>Personal Information</Text> */}
        {/* <View style={styles.GenderandAge}>

        <DropdownField
          label="Gender"
          value={props.form.gender}
          data={props.genderOptions}
          keyField="Value"
          valueField="Text"
          onSelect={(val) => props.handleChange('gender', val)}
          errorText={props.errors.gender}
        />

        <DropdownField
          label="Age Group"
          value={props.form.ageGroup}
          data={props.ageGroups}
          keyField="Value"
          valueField="Text"
          onSelect={(val) => props.handleChange('ageGroup', val)}
          errorText={props.errors.ageGroup}
        />
        </View> */}

        {/* --- Professional Information --- */}
        <Text style={styles.sectionHeader}>Professional Information</Text>
        <View style={styles.GenderandAge}>
          <DropdownField
            label="Department"
            value={props.form.department}
            data={props.departments}
            keyField="Value"
            valueField="Text"
            onSelect={(val) => props.handleChange('department', val)}
            showAsterisk={false}
            enableSearch
            searchPlaceholderText='Search department...'
          />
          <DropdownField
            label="Role"
            value={props.form.role}
            data={props.roles}
            keyField="Value"
            valueField="Text"
            onSelect={(val) => props.handleChange('role', val)}
            showAsterisk={false}
            enableSearch
            searchPlaceholderText='Search role...'
          />
        </View>
        {/* --- Flags --- */}
        <View style={styles.checkboxRow}>
          <Checkbox.Android
            status={props.form.decisionMaker ? 'checked' : 'unchecked'}
            onPress={() => props.handleChange('decisionMaker', !props.form.decisionMaker)}
          />
          <View>
            <Text>Decision Maker</Text>
            {/* <Text>Can make decisions on behalf of the organization</Text> */}
          </View>
        </View>
        <View style={styles.checkboxRow}>
          <Checkbox.Android
            status={props.form.primaryContact ? 'checked' : 'unchecked'}
            onPress={() => props.handleChange('primaryContact', !props.form.primaryContact)}
          />
          <View>
            <Text>Primary Contact</Text>
            {/* <Text>Main point of contact for this organization</Text> */}
          </View>
        </View>

        <Text style={styles.sectionHeader}>Status</Text>
         <View style={styles.checkboxRow}>
          <View style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }}>
          <Switch value={props.form.status_id } onValueChange={() => {
            props.handleChange('status_id',!props.form.status_id)
          }} />
          </View>
          <View>
          <Text style={styles.activeText} variant="semiBold">{ props.form.status_id ? "Active" : "Inactive"}</Text>
          <Text style={styles.emptyText} variant='regular' >Toggle to set the contact as Active or Inactive</Text>
          </View>
        </View>
        {/* <DropdownField
          label="Status"
          value={props.form.status}
          data={[
            { Text: 'Active', Value: 'Active' },
            { Text: 'Inactive', Value: 'Inactive' },
            { Text: 'Pending', Value: 'Pending' },
          ]}
          keyField="Value"
          valueField="Text"
          onSelect={(val) => props.handleChange('status', val)}
        /> */}

        <View style={{ height: verticalScale(40) }} />
      </ScrollView>
    </KeyboardAvoidingView>
      {/* --- Actions --- */}
      <SafeAreaView>
        <View style={styles.actionRow}>
          <Button mode="contained"
            onPress={props.resetForm}
            style={styles.cancelButton} textColor="#000">
            Reset
          </Button>
          <Button
            mode="contained"
            style={styles.saveButton}
            textColor="#fff"
            onPress={props.handleSubmit}
          >
            {contact?.contact_id ? 'Update' : 'Create'}
          </Button>
        </View>
      </SafeAreaView>
      <CountryPicker
        show={props.showCountryPicker}
        pickerButtonOnPress={(item) => {
          if (props.countryPickerIndex !== null) {
            props.updatePhoneCountry(props.countryPickerIndex, item);
          }
          props.setShowCountryPicker(false);
          props.setCountryPickerIndex(null);
        }}
        lang="en"
        style={{
          modal: {
            height: '80%',
          },
        }}
        popularCountries={['AE', 'QA', 'OM', 'KW', 'SA', 'BH', 'IN']}
        onBackdropPress={() => props.setShowCountryPicker(false)}
      />
    </View>
  );
};

export default ContactForm;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: scale(16),
    paddingBottom: verticalScale(30),
    backgroundColor: '#fff',
  },
  GenderandAge:{
  flexDirection:'row',
  gap:moderateScale(12),
  alignItems:"center"
  },
  sectionHeader: {
    fontSize: moderateScale(15),
    fontFamily: fontFamily.semiBold,
    color: '#1F2937',
    marginTop: verticalScale(10),
    marginBottom:verticalScale(5)
  },
  rowHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  card: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 6,
    padding: moderateScale(12),
    marginBottom: verticalScale(10),
    backgroundColor: '#F9FAFB',
    position: 'relative',
    width:"100%"
  },
  deleteIcon: {
    position: 'absolute',
    top: scale(6),
    right: scale(6),
  },
  badgeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(10),
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: verticalScale(5),
    gap: scale(5),
  },
  actionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: scale(16),
    gap: scale(10),
    paddingVertical: verticalScale(10),
  },
  cancelButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: '#E5E7EB',
  },
  saveButton: {
    flex: 1,
    borderRadius: 4,
    backgroundColor: '#2563EB',
  },
  errorText: {
    color: '#DC2626',
    fontSize: moderateScale(11),
    marginTop: verticalScale(-6),
    marginBottom: verticalScale(8),
    fontFamily: fontFamily.medium,
  },
  phoneRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: scale(8),
    marginBottom: verticalScale(10),
  },

  countryCodeBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: scale(8),
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    height: verticalScale(28),
  },

  flag: {
    fontSize: moderateScale(16),
    marginRight: scale(4),
  },

  countryCode: {
    fontSize: moderateScale(13),
    color: '#111827',
  },
  label: {
    fontSize: moderateScale(12),
    marginVertical: 4,
    color: '#111827',
    marginBottom: verticalScale(5),
  },
  activeText:{
    fontSize: moderateScale(12),
    color: '#111827',
  },
  emptyText: {
    fontSize: moderateScale(12),
    color: '#6B7280', // gray-500
    fontStyle: 'italic',
    marginBottom: verticalScale(8),
    marginLeft: scale(2),
  },
});
